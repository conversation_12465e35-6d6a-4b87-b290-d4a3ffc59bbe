<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kilat.js Desktop</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/fonts/Inter-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/fonts/JetBrainsMono-Regular.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Meta tags -->
    <meta name="description" content="Kilat.js Desktop - Framework fullstack masa depan dari Nusantara">
    <meta name="author" content="KangPCode">
    <meta name="theme-color" content="#000011">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    
    <!-- Fonts -->
    <style>
      @font-face {
        font-family: 'Inter';
        src: url('/fonts/Inter-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
      
      @font-face {
        font-family: 'Inter';
        src: url('/fonts/Inter-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
        font-display: swap;
      }
      
      @font-face {
        font-family: 'JetBrains Mono';
        src: url('/fonts/JetBrainsMono-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
      
      @font-face {
        font-family: 'Orbitron';
        src: url('/fonts/Orbitron-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
    </style>
    
    <!-- Critical CSS -->
    <style>
      /* Critical styles for initial render */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      html, body {
        height: 100%;
        overflow: hidden;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: #000011;
        color: #ffffff;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #root {
        height: 100%;
        width: 100%;
      }
      
      /* Loading screen */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #000011, #001122, #000033);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-logo {
        font-size: 4rem;
        color: #00ffff;
        margin-bottom: 2rem;
        text-shadow: 0 0 20px #00ffff;
        animation: pulse 2s ease-in-out infinite;
      }
      
      .loading-text {
        font-size: 1.5rem;
        color: #ffffff;
        margin-bottom: 1rem;
        font-weight: 600;
      }
      
      .loading-subtitle {
        font-size: 1rem;
        color: #888888;
        margin-bottom: 2rem;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #333333;
        border-top: 3px solid #00ffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app is ready */
      .app-ready .loading-screen {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease, visibility 0.5s ease;
      }
    </style>
  </head>
  
  <body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
      <div class="loading-logo">⚡</div>
      <div class="loading-text">Kilat.js Desktop</div>
      <div class="loading-subtitle">Framework masa depan dari Nusantara</div>
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- App Initialization Script -->
    <script>
      // App initialization
      window.addEventListener('DOMContentLoaded', () => {
        console.log('🖥️ Kilat.js Desktop starting...');
        
        // Hide loading screen after app loads
        setTimeout(() => {
          document.body.classList.add('app-ready');
          setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
              loadingScreen.remove();
            }
          }, 500);
        }, 2000);
        
        // Performance monitoring
        if (window.performance) {
          window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`⚡ App loaded in ${loadTime.toFixed(2)}ms`);
          });
        }
        
        // Error handling
        window.addEventListener('error', (event) => {
          console.error('🚨 Unhandled error:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
          console.error('🚨 Unhandled promise rejection:', event.reason);
        });
      });
      
      // Keyboard shortcuts
      document.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + R: Reload (development only)
        if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
          if (window.devAPI) {
            event.preventDefault();
            window.devAPI.reload();
          }
        }
        
        // F12: Toggle DevTools (development only)
        if (event.key === 'F12') {
          if (window.devAPI) {
            event.preventDefault();
            window.devAPI.openDevTools();
          }
        }
        
        // Ctrl/Cmd + Shift + I: Toggle DevTools (development only)
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'I') {
          if (window.devAPI) {
            event.preventDefault();
            window.devAPI.openDevTools();
          }
        }
      });
      
      // Theme detection
      if (window.electronAPI) {
        window.electronAPI.getNativeTheme().then(isDark => {
          document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
        });
      }
    </script>
    
    <!-- Main App Script -->
    <script type="module" src="/src/renderer/main.tsx"></script>
  </body>
</html>
