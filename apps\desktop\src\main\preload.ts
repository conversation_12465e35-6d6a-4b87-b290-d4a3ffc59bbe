import { contextBridge, ipc<PERSON>enderer } from 'electron';

/**
 * 🔗 Preload Script
 * Exposes safe APIs to the renderer process
 */

// Define the API interface
export interface ElectronAPI {
  // App info
  getVersion: () => Promise<string>;
  getPlatform: () => Promise<{
    platform: string;
    arch: string;
    version: string;
    electronVersion: string;
  }>;

  // Theme management
  getNativeTheme: () => Promise<boolean>;
  setNativeTheme: (theme: 'system' | 'light' | 'dark') => void;

  // Window controls
  minimizeWindow: () => void;
  maximizeWindow: () => void;
  closeWindow: () => void;

  // Dialog APIs
  showMessage: (options: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<any>;

  // Menu event listeners
  onMenuEvent: (event: string, callback: (...args: any[]) => void) => void;
  removeMenuListener: (event: string) => void;

  // File system (safe operations only)
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<void>;
  
  // Project management
  createProject: (name: string, path: string) => Promise<boolean>;
  openProject: (path: string) => Promise<boolean>;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  // App info
  getVersion: () => ipcRenderer.invoke('app:get-version'),
  getPlatform: () => ipcRenderer.invoke('app:get-platform'),

  // Theme management
  getNativeTheme: () => ipcRenderer.invoke('theme:get-native-theme'),
  setNativeTheme: (theme) => ipcRenderer.send('theme:set-native-theme', theme),

  // Window controls
  minimizeWindow: () => ipcRenderer.send('window:minimize'),
  maximizeWindow: () => ipcRenderer.send('window:maximize'),
  closeWindow: () => ipcRenderer.send('window:close'),

  // Dialog APIs
  showMessage: (options) => ipcRenderer.invoke('dialog:show-message', options),
  showSaveDialog: (options) => ipcRenderer.invoke('dialog:show-save', options),

  // Menu event listeners
  onMenuEvent: (event, callback) => {
    const channel = `menu:${event}`;
    ipcRenderer.on(channel, callback);
  },
  removeMenuListener: (event) => {
    const channel = `menu:${event}`;
    ipcRenderer.removeAllListeners(channel);
  },

  // File system (safe operations only)
  readFile: (path) => ipcRenderer.invoke('fs:read-file', path),
  writeFile: (path, content) => ipcRenderer.invoke('fs:write-file', path, content),

  // Project management
  createProject: (name, path) => ipcRenderer.invoke('project:create', name, path),
  openProject: (path) => ipcRenderer.invoke('project:open', path),
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Also expose some Node.js globals that are safe to use
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  arch: process.arch,
  versions: process.versions,
});

// Development helpers
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('devAPI', {
    openDevTools: () => ipcRenderer.send('dev:open-devtools'),
    reload: () => ipcRenderer.send('dev:reload'),
  });
}

// Security: Remove Node.js globals from window object
if (typeof window !== 'undefined') {
  delete (window as any).require;
  delete (window as any).exports;
  delete (window as any).module;
}

// Log successful preload
console.log('🔗 Preload script loaded successfully');

// Type declaration for global window object
declare global {
  interface Window {
    electronAPI: ElectronAPI;
    nodeAPI: {
      platform: string;
      arch: string;
      versions: NodeJS.ProcessVersions;
    };
    devAPI?: {
      openDevTools: () => void;
      reload: () => void;
    };
  }
}
