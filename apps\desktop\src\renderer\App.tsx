import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { KilatProvider } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import { motion, AnimatePresence } from 'framer-motion';

// Import styles
import 'kilatcss/kilat.css';
import './styles/desktop.css';

// Import components
import { TitleBar } from './components/TitleBar';
import { Sidebar } from './components/Sidebar';
import { StatusBar } from './components/StatusBar';
import { LoadingScreen } from './components/LoadingScreen';

// Import pages
import HomePage from './pages/HomePage';
import DemoPage from './pages/DemoPage';
import ThemesPage from './pages/ThemesPage';
import SettingsPage from './pages/SettingsPage';
import AboutPage from './pages/AboutPage';

/**
 * 🖥️ Kilat.js Desktop Application
 * Electron + React showcase application
 */
function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [theme, setTheme] = useState('cyberpunk');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('home');

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Simulate loading time
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Get platform info
        if (window.electronAPI) {
          const platformInfo = await window.electronAPI.getPlatform();
          console.log('Platform info:', platformInfo);
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('kilat-theme') || 'cyberpunk';
        setTheme(savedTheme);
        
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Handle theme change
  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme);
    localStorage.setItem('kilat-theme', newTheme);
    document.documentElement.setAttribute('data-kilat-theme', newTheme);
    
    // Update native theme if available
    if (window.electronAPI) {
      const nativeTheme = newTheme === 'minimalist' ? 'light' : 'dark';
      window.electronAPI.setNativeTheme(nativeTheme);
    }
  };

  // Handle menu events
  useEffect(() => {
    if (window.electronAPI) {
      // Listen for menu events
      window.electronAPI.onMenuEvent('toggle-theme', () => {
        const themes = ['cyberpunk', 'nusantara', 'minimalist', 'retro', 'aurora'];
        const currentIndex = themes.indexOf(theme);
        const nextTheme = themes[(currentIndex + 1) % themes.length];
        handleThemeChange(nextTheme);
      });

      window.electronAPI.onMenuEvent('new-project', () => {
        console.log('New project requested');
        // Handle new project creation
      });

      window.electronAPI.onMenuEvent('about', () => {
        setCurrentPage('about');
      });
    }
  }, [theme]);

  if (isLoading) {
    return <LoadingScreen theme={theme} />;
  }

  return (
    <KilatProvider config={{ theme, mode: 'dark' }}>
      <Router>
        <div className={`k-desktop-app k-theme-${theme}`}>
          {/* Background Animation */}
          <div className="k-desktop-background">
            <KilatScene
              preset="galaxy"
              autoRotate={true}
              background="transparent"
              className="k-w-full k-h-full"
            />
          </div>

          {/* Title Bar */}
          <TitleBar 
            theme={theme}
            onThemeChange={handleThemeChange}
          />

          {/* Main Layout */}
          <div className="k-desktop-layout">
            {/* Sidebar */}
            <Sidebar
              theme={theme}
              collapsed={sidebarCollapsed}
              onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />

            {/* Main Content */}
            <main className="k-desktop-main">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="k-desktop-content"
                >
                  <Routes>
                    <Route path="/" element={<HomePage theme={theme} />} />
                    <Route path="/demo" element={<DemoPage theme={theme} />} />
                    <Route path="/themes" element={<ThemesPage theme={theme} onThemeChange={handleThemeChange} />} />
                    <Route path="/settings" element={<SettingsPage theme={theme} />} />
                    <Route path="/about" element={<AboutPage theme={theme} />} />
                  </Routes>
                </motion.div>
              </AnimatePresence>
            </main>
          </div>

          {/* Status Bar */}
          <StatusBar theme={theme} />
        </div>
      </Router>
    </KilatProvider>
  );
}

export default App;
