import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Zap, Loader2 } from 'lucide-react';

interface LoadingScreenProps {
  theme: string;
}

const loadingSteps = [
  'Initializing Kilat.js Engine...',
  'Loading UI Components...',
  'Setting up 3D Renderer...',
  'Configuring Theme System...',
  'Preparing Desktop Environment...',
  'Almost Ready...'
];

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ theme }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < loadingSteps.length - 1) {
          return prev + 1;
        }
        return prev;
      });
    }, 300);

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev < 100) {
          return prev + 2;
        }
        return prev;
      });
    }, 40);

    return () => {
      clearInterval(stepInterval);
      clearInterval(progressInterval);
    };
  }, []);

  return (
    <motion.div
      className={`k-loading-screen k-theme-${theme}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background Animation */}
      <div className="k-loading-background">
        <div className="k-loading-particles">
          {Array.from({ length: 50 }).map((_, i) => (
            <motion.div
              key={i}
              className="k-loading-particle"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                opacity: 0
              }}
              animate={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="k-loading-content">
        {/* Logo */}
        <motion.div
          className="k-loading-logo"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: 'spring', 
            stiffness: 200, 
            damping: 20,
            delay: 0.2 
          }}
        >
          <Zap size={64} className="k-loading-icon" />
          <motion.div
            className="k-loading-glow"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        </motion.div>

        {/* Title */}
        <motion.h1
          className="k-loading-title"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          Kilat.js Desktop
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          className="k-loading-subtitle"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          Futuristic Framework for Modern Applications
        </motion.p>

        {/* Progress Bar */}
        <motion.div
          className="k-loading-progress-container"
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: 300, opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <div className="k-loading-progress-track">
            <motion.div
              className="k-loading-progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ ease: 'easeOut' }}
            />
            <motion.div
              className="k-loading-progress-glow"
              animate={{
                x: [0, 300, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
            />
          </div>
          <div className="k-loading-progress-text">
            {progress}%
          </div>
        </motion.div>

        {/* Loading Steps */}
        <div className="k-loading-steps">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              className="k-loading-step"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -10, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Loader2 size={16} className="k-loading-spinner" />
              <span>{loadingSteps[currentStep]}</span>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Version */}
        <motion.div
          className="k-loading-version"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5 }}
        >
          Version 1.0.0
        </motion.div>
      </div>
    </motion.div>
  );
};
