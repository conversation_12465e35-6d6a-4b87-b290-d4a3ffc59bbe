import React from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Zap, 
  Palette, 
  Settings, 
  Info,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface SidebarProps {
  theme: string;
  collapsed: boolean;
  onToggle: () => void;
  currentPage: string;
  onPageChange: (page: string) => void;
}

const menuItems = [
  { id: 'home', label: 'Home', icon: Home, path: '/' },
  { id: 'demo', label: 'Demo', icon: Zap, path: '/demo' },
  { id: 'themes', label: 'Themes', icon: Palette, path: '/themes' },
  { id: 'settings', label: 'Settings', icon: Settings, path: '/settings' },
  { id: 'about', label: 'About', icon: Info, path: '/about' },
];

export const Sidebar: React.FC<SidebarProps> = ({
  theme,
  collapsed,
  onToggle,
  currentPage,
  onPageChange
}) => {
  return (
    <motion.aside
      className={`k-sidebar k-theme-${theme} ${collapsed ? 'k-sidebar-collapsed' : ''}`}
      initial={false}
      animate={{ width: collapsed ? 60 : 240 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
    >
      {/* Toggle Button */}
      <button
        className="k-sidebar-toggle"
        onClick={onToggle}
        title={collapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}
      >
        {collapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
      </button>

      {/* Logo */}
      <div className="k-sidebar-logo">
        <div className="k-logo-icon">⚡</div>
        {!collapsed && (
          <motion.div
            className="k-logo-text"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0.1 }}
          >
            Kilat.js
          </motion.div>
        )}
      </div>

      {/* Navigation */}
      <nav className="k-sidebar-nav">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;

          return (
            <motion.button
              key={item.id}
              className={`k-sidebar-item ${isActive ? 'k-sidebar-item-active' : ''}`}
              onClick={() => onPageChange(item.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              title={collapsed ? item.label : undefined}
            >
              <Icon size={20} className="k-sidebar-icon" />
              {!collapsed && (
                <motion.span
                  className="k-sidebar-label"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  {item.label}
                </motion.span>
              )}
              {isActive && (
                <motion.div
                  className="k-sidebar-indicator"
                  layoutId="sidebar-indicator"
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                />
              )}
            </motion.button>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="k-sidebar-footer">
        {!collapsed && (
          <motion.div
            className="k-sidebar-version"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0.1 }}
          >
            v1.0.0
          </motion.div>
        )}
      </div>
    </motion.aside>
  );
};
