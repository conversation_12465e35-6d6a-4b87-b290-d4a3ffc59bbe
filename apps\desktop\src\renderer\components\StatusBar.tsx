import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  Battery, 
  Clock,
  Cpu,
  HardDrive,
  Activity
} from 'lucide-react';

interface StatusBarProps {
  theme: string;
}

interface SystemInfo {
  isOnline: boolean;
  battery?: number;
  time: string;
  cpu?: number;
  memory?: number;
  platform: string;
}

export const StatusBar: React.FC<StatusBarProps> = ({ theme }) => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    isOnline: navigator.onLine,
    time: new Date().toLocaleTimeString(),
    platform: 'desktop'
  });

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setSystemInfo(prev => ({
        ...prev,
        time: new Date().toLocaleTimeString()
      }));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => setSystemInfo(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setSystemInfo(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Get system info from Electron if available
  useEffect(() => {
    const getSystemInfo = async () => {
      if (window.electronAPI) {
        try {
          const platformInfo = await window.electronAPI.getPlatform();
          setSystemInfo(prev => ({
            ...prev,
            platform: platformInfo.platform || 'desktop'
          }));
        } catch (error) {
          console.warn('Failed to get platform info:', error);
        }
      }
    };

    getSystemInfo();
  }, []);

  const getBatteryIcon = () => {
    if (systemInfo.battery === undefined) return <Battery size={14} />;
    
    if (systemInfo.battery > 75) return <Battery size={14} className="k-text-green-400" />;
    if (systemInfo.battery > 50) return <Battery size={14} className="k-text-yellow-400" />;
    if (systemInfo.battery > 25) return <Battery size={14} className="k-text-orange-400" />;
    return <Battery size={14} className="k-text-red-400" />;
  };

  return (
    <motion.footer
      className={`k-status-bar k-theme-${theme}`}
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.5 }}
    >
      {/* Left Section */}
      <div className="k-status-left">
        <div className="k-status-item">
          <Activity size={14} />
          <span>Kilat.js Desktop</span>
        </div>
        
        <div className="k-status-item">
          <Cpu size={14} />
          <span>{systemInfo.platform}</span>
        </div>
      </div>

      {/* Center Section */}
      <div className="k-status-center">
        <motion.div
          className="k-status-item k-status-time"
          key={systemInfo.time}
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <Clock size={14} />
          <span>{systemInfo.time}</span>
        </motion.div>
      </div>

      {/* Right Section */}
      <div className="k-status-right">
        {systemInfo.cpu !== undefined && (
          <div className="k-status-item">
            <Cpu size={14} />
            <span>{systemInfo.cpu}%</span>
          </div>
        )}

        {systemInfo.memory !== undefined && (
          <div className="k-status-item">
            <HardDrive size={14} />
            <span>{systemInfo.memory}%</span>
          </div>
        )}

        {systemInfo.battery !== undefined && (
          <div className="k-status-item">
            {getBatteryIcon()}
            <span>{systemInfo.battery}%</span>
          </div>
        )}

        <div className="k-status-item">
          {systemInfo.isOnline ? (
            <Wifi size={14} className="k-text-green-400" />
          ) : (
            <WifiOff size={14} className="k-text-red-400" />
          )}
          <span>{systemInfo.isOnline ? 'Online' : 'Offline'}</span>
        </div>
      </div>
    </motion.footer>
  );
};
