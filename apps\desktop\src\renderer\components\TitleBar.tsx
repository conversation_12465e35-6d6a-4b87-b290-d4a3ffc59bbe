import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Minimize2, 
  Maximize2, 
  X, 
  Zap, 
  Palette,
  Settings,
  Info
} from 'lucide-react';

interface TitleBarProps {
  theme: string;
  onThemeChange: (theme: string) => void;
}

/**
 * 🪟 Custom Title Bar Component
 * Native-looking title bar for desktop app
 */
export function TitleBar({ theme, onThemeChange }: TitleBarProps) {
  const [isMaximized, setIsMaximized] = useState(false);
  const [appVersion, setAppVersion] = useState('1.0.0');

  useEffect(() => {
    // Get app version
    if (window.electronAPI) {
      window.electronAPI.getVersion().then(setAppVersion);
    }
  }, []);

  const handleMinimize = () => {
    if (window.electronAPI) {
      window.electronAPI.minimizeWindow();
    }
  };

  const handleMaximize = () => {
    if (window.electronAPI) {
      window.electronAPI.maximizeWindow();
      setIsMaximized(!isMaximized);
    }
  };

  const handleClose = () => {
    if (window.electronAPI) {
      window.electronAPI.closeWindow();
    }
  };

  const themes = [
    { name: 'cyberpunk', icon: '🌆', label: 'Cyberpunk' },
    { name: 'nusantara', icon: '🏝️', label: 'Nusantara' },
    { name: 'minimalist', icon: '⚪', label: 'Minimalist' },
    { name: 'retro', icon: '📼', label: 'Retro' },
    { name: 'aurora', icon: '🌌', label: 'Aurora' }
  ];

  return (
    <div className="k-titlebar" data-theme={theme}>
      {/* Left Section - App Info */}
      <div className="k-titlebar-left">
        <div className="k-app-icon">
          <Zap size={16} />
        </div>
        <div className="k-app-info">
          <span className="k-app-name">Kilat.js Desktop</span>
          <span className="k-app-version">v{appVersion}</span>
        </div>
      </div>

      {/* Center Section - Title */}
      <div className="k-titlebar-center">
        <motion.div 
          className="k-title-text"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          Framework Masa Depan dari Nusantara
        </motion.div>
      </div>

      {/* Right Section - Controls */}
      <div className="k-titlebar-right">
        {/* Theme Selector */}
        <div className="k-theme-selector">
          <button 
            className="k-titlebar-btn k-theme-btn"
            title="Change Theme"
          >
            <Palette size={14} />
          </button>
          <div className="k-theme-dropdown">
            {themes.map((themeOption) => (
              <button
                key={themeOption.name}
                className={`k-theme-option ${theme === themeOption.name ? 'active' : ''}`}
                onClick={() => onThemeChange(themeOption.name)}
                title={themeOption.label}
              >
                <span className="k-theme-icon">{themeOption.icon}</span>
                <span className="k-theme-label">{themeOption.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Settings Button */}
        <button 
          className="k-titlebar-btn"
          title="Settings"
        >
          <Settings size={14} />
        </button>

        {/* About Button */}
        <button 
          className="k-titlebar-btn"
          title="About"
        >
          <Info size={14} />
        </button>

        {/* Window Controls */}
        <div className="k-window-controls">
          <motion.button
            className="k-titlebar-btn k-minimize-btn"
            onClick={handleMinimize}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            title="Minimize"
          >
            <Minimize2 size={14} />
          </motion.button>

          <motion.button
            className="k-titlebar-btn k-maximize-btn"
            onClick={handleMaximize}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            title={isMaximized ? "Restore" : "Maximize"}
          >
            <Maximize2 size={14} />
          </motion.button>

          <motion.button
            className="k-titlebar-btn k-close-btn"
            onClick={handleClose}
            whileHover={{ scale: 1.1, backgroundColor: '#ff4757' }}
            whileTap={{ scale: 0.95 }}
            title="Close"
          >
            <X size={14} />
          </motion.button>
        </div>
      </div>
    </div>
  );
}
