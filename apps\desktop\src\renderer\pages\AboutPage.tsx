import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Info, 
  Zap, 
  Github, 
  Globe, 
  Mail,
  Heart,
  Code,
  Users,
  Star,
  Download,
  ExternalLink
} from 'lucide-react';

interface AboutPageProps {
  theme: string;
}

interface AppInfo {
  version: string;
  buildDate: string;
  platform: string;
  electronVersion?: string;
  nodeVersion?: string;
}

const AboutPage: React.FC<AboutPageProps> = ({ theme }) => {
  const [appInfo, setAppInfo] = useState<AppInfo>({
    version: '1.0.0',
    buildDate: new Date().toLocaleDateString(),
    platform: 'desktop'
  });

  useEffect(() => {
    const getAppInfo = async () => {
      if (window.electronAPI) {
        try {
          const version = await window.electronAPI.getVersion();
          const platformInfo = await window.electronAPI.getPlatform();
          
          setAppInfo({
            version,
            buildDate: new Date().toLocaleDateString(),
            platform: platformInfo.platform || 'desktop',
            electronVersion: platformInfo.electronVersion,
            nodeVersion: platformInfo.version
          });
        } catch (error) {
          console.error('Failed to get app info:', error);
        }
      }
    };

    getAppInfo();
  }, []);

  const features = [
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Built with performance in mind using modern web technologies'
    },
    {
      icon: Code,
      title: 'Developer Friendly',
      description: 'Comprehensive API and excellent developer experience'
    },
    {
      icon: Users,
      title: 'Community Driven',
      description: 'Open source project with active community support'
    },
    {
      icon: Star,
      title: 'Production Ready',
      description: 'Battle-tested and used in production applications'
    }
  ];

  const team = [
    {
      name: 'KangPCode',
      role: 'Creator & Lead Developer',
      avatar: '👨‍💻',
      github: 'kangpcode'
    }
  ];

  const technologies = [
    { name: 'Electron', version: appInfo.electronVersion || '27.0.0' },
    { name: 'React', version: '18.2.0' },
    { name: 'TypeScript', version: '5.3.0' },
    { name: 'Three.js', version: '0.158.0' },
    { name: 'Framer Motion', version: '10.16.0' },
    { name: 'Vite', version: '5.0.0' }
  ];

  return (
    <motion.div
      className={`k-about-page k-theme-${theme}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="k-about-header">
        <motion.div
          className="k-about-logo"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ type: 'spring', stiffness: 200, damping: 20 }}
        >
          <Zap size={64} className="k-about-icon" />
          <div className="k-about-glow" />
        </motion.div>

        <div className="k-about-title">
          <h1>Kilat.js Desktop</h1>
          <p>Futuristic Framework for Modern Applications</p>
          <div className="k-about-version">
            Version {appInfo.version}
          </div>
        </div>
      </div>

      {/* App Info */}
      <div className="k-about-info">
        <h2>Application Information</h2>
        <div className="k-about-info-grid">
          <div className="k-about-info-item">
            <strong>Version</strong>
            <span>{appInfo.version}</span>
          </div>
          <div className="k-about-info-item">
            <strong>Build Date</strong>
            <span>{appInfo.buildDate}</span>
          </div>
          <div className="k-about-info-item">
            <strong>Platform</strong>
            <span>{appInfo.platform}</span>
          </div>
          {appInfo.electronVersion && (
            <div className="k-about-info-item">
              <strong>Electron</strong>
              <span>{appInfo.electronVersion}</span>
            </div>
          )}
          {appInfo.nodeVersion && (
            <div className="k-about-info-item">
              <strong>Node.js</strong>
              <span>{appInfo.nodeVersion}</span>
            </div>
          )}
        </div>
      </div>

      {/* Features */}
      <div className="k-about-features">
        <h2>Key Features</h2>
        <div className="k-about-features-grid">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={index}
                className="k-about-feature"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Icon className="k-about-feature-icon" />
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Technologies */}
      <div className="k-about-technologies">
        <h2>Built With</h2>
        <div className="k-about-tech-grid">
          {technologies.map((tech, index) => (
            <motion.div
              key={index}
              className="k-about-tech"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.05 }}
            >
              <strong>{tech.name}</strong>
              <span>v{tech.version}</span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Team */}
      <div className="k-about-team">
        <h2>Development Team</h2>
        <div className="k-about-team-grid">
          {team.map((member, index) => (
            <motion.div
              key={index}
              className="k-about-member"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="k-about-avatar">{member.avatar}</div>
              <h3>{member.name}</h3>
              <p>{member.role}</p>
              <a
                href={`https://github.com/${member.github}`}
                target="_blank"
                rel="noopener noreferrer"
                className="k-about-github"
              >
                <Github size={16} />
                @{member.github}
              </a>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Links */}
      <div className="k-about-links">
        <h2>Links & Resources</h2>
        <div className="k-about-links-grid">
          <a
            href="https://kilat-js.pcode.my.id"
            target="_blank"
            rel="noopener noreferrer"
            className="k-about-link"
          >
            <Globe className="k-about-link-icon" />
            <span>Official Website</span>
            <ExternalLink size={16} />
          </a>
          <a
            href="https://github.com/kangpcode/kilat.js"
            target="_blank"
            rel="noopener noreferrer"
            className="k-about-link"
          >
            <Github className="k-about-link-icon" />
            <span>Source Code</span>
            <ExternalLink size={16} />
          </a>
          <a
            href="https://kilat-js.pcode.my.id/docs"
            target="_blank"
            rel="noopener noreferrer"
            className="k-about-link"
          >
            <Info className="k-about-link-icon" />
            <span>Documentation</span>
            <ExternalLink size={16} />
          </a>
          <a
            href="mailto:<EMAIL>"
            className="k-about-link"
          >
            <Mail className="k-about-link-icon" />
            <span>Support</span>
            <ExternalLink size={16} />
          </a>
        </div>
      </div>

      {/* Footer */}
      <div className="k-about-footer">
        <p>
          Made with <Heart className="k-about-heart" /> by the Kilat.js team
        </p>
        <p>
          © 2024 KangPCode. Licensed under MIT License.
        </p>
      </div>
    </motion.div>
  );
};

export default AboutPage;
