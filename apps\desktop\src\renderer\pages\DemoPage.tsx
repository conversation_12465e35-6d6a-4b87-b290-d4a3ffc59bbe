import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { KilatScene } from 'kilatanim.js';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  <PERSON>ting<PERSON>,
  Zap,
  Sparkles,
  Layers,
  Palette
} from 'lucide-react';

interface DemoPageProps {
  theme: string;
}

const demoScenes = [
  {
    id: 'galaxy',
    name: 'Galaxy Explorer',
    description: 'Interactive 3D galaxy with particle systems',
    preset: 'galaxy'
  },
  {
    id: 'cyberpunk',
    name: 'Cyberpunk City',
    description: 'Neon-lit futuristic cityscape',
    preset: 'cyberpunk'
  },
  {
    id: 'particles',
    name: 'Particle Storm',
    description: 'Dynamic particle animation system',
    preset: 'particles'
  },
  {
    id: 'waves',
    name: 'Ocean Waves',
    description: 'Fluid wave simulation',
    preset: 'waves'
  }
];

const DemoPage: React.FC<DemoPageProps> = ({ theme }) => {
  const [activeScene, setActiveScene] = useState('galaxy');
  const [isPlaying, setIsPlaying] = useState(true);
  const [showControls, setShowControls] = useState(true);

  const currentScene = demoScenes.find(scene => scene.id === activeScene);

  return (
    <motion.div
      className={`k-demo-page k-theme-${theme}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="k-demo-header">
        <div className="k-demo-title">
          <Zap className="k-demo-icon" />
          <h1>Interactive Demo</h1>
          <p>Experience the power of Kilat.js 3D animations</p>
        </div>

        <div className="k-demo-controls">
          <button
            className={`k-demo-btn ${isPlaying ? 'k-demo-btn-active' : ''}`}
            onClick={() => setIsPlaying(!isPlaying)}
          >
            {isPlaying ? <Pause size={16} /> : <Play size={16} />}
            {isPlaying ? 'Pause' : 'Play'}
          </button>

          <button
            className="k-demo-btn"
            onClick={() => setShowControls(!showControls)}
          >
            <Settings size={16} />
            Controls
          </button>
        </div>
      </div>

      {/* Scene Selector */}
      <div className="k-demo-scenes">
        {demoScenes.map((scene) => (
          <motion.button
            key={scene.id}
            className={`k-demo-scene ${activeScene === scene.id ? 'k-demo-scene-active' : ''}`}
            onClick={() => setActiveScene(scene.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="k-demo-scene-icon">
              {scene.id === 'galaxy' && <Sparkles size={20} />}
              {scene.id === 'cyberpunk' && <Layers size={20} />}
              {scene.id === 'particles' && <Zap size={20} />}
              {scene.id === 'waves' && <Palette size={20} />}
            </div>
            <div className="k-demo-scene-info">
              <h3>{scene.name}</h3>
              <p>{scene.description}</p>
            </div>
            {activeScene === scene.id && (
              <motion.div
                className="k-demo-scene-indicator"
                layoutId="scene-indicator"
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              />
            )}
          </motion.button>
        ))}
      </div>

      {/* 3D Scene */}
      <div className="k-demo-viewport">
        <KilatScene
          preset={currentScene?.preset || 'galaxy'}
          autoRotate={isPlaying}
          controls={showControls}
          background="transparent"
          className="k-w-full k-h-full"
        />

        {/* Scene Info Overlay */}
        <motion.div
          className="k-demo-overlay"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="k-demo-info">
            <h2>{currentScene?.name}</h2>
            <p>{currentScene?.description}</p>
          </div>

          <div className="k-demo-stats">
            <div className="k-demo-stat">
              <span className="k-demo-stat-label">FPS</span>
              <span className="k-demo-stat-value">60</span>
            </div>
            <div className="k-demo-stat">
              <span className="k-demo-stat-label">Particles</span>
              <span className="k-demo-stat-value">1,024</span>
            </div>
            <div className="k-demo-stat">
              <span className="k-demo-stat-label">Triangles</span>
              <span className="k-demo-stat-value">2,048</span>
            </div>
          </div>
        </motion.div>

        {/* Reset Button */}
        <button
          className="k-demo-reset"
          onClick={() => window.location.reload()}
          title="Reset Scene"
        >
          <RotateCcw size={20} />
        </button>
      </div>

      {/* Features */}
      <div className="k-demo-features">
        <h2>Demo Features</h2>
        <div className="k-demo-feature-grid">
          <div className="k-demo-feature">
            <Sparkles className="k-demo-feature-icon" />
            <h3>Real-time Rendering</h3>
            <p>60fps smooth animations with WebGL acceleration</p>
          </div>
          <div className="k-demo-feature">
            <Layers className="k-demo-feature-icon" />
            <h3>Interactive Controls</h3>
            <p>Mouse and keyboard controls for camera movement</p>
          </div>
          <div className="k-demo-feature">
            <Zap className="k-demo-feature-icon" />
            <h3>Particle Systems</h3>
            <p>Advanced particle effects and physics simulation</p>
          </div>
          <div className="k-demo-feature">
            <Palette className="k-demo-feature-icon" />
            <h3>Theme Integration</h3>
            <p>Seamless integration with Kilat.js theme system</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default DemoPage;
