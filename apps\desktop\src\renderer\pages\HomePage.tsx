import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Palette, 
  Layers, 
  Database, 
  Plug, 
  Shield,
  Monitor,
  Smartphone,
  Globe,
  Github,
  ExternalLink
} from 'lucide-react';

interface HomePageProps {
  theme: string;
}

/**
 * 🏠 Desktop Home Page
 */
export default function HomePage({ theme }: HomePageProps) {
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Animate stats on mount
    if (statsRef.current) {
      const statElements = statsRef.current.querySelectorAll('.stat-value');
      statElements.forEach((element, index) => {
        const target = parseInt(element.textContent?.replace(/\D/g, '') || '0');
        let current = 0;
        const increment = target / 50;
        
        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            current = target;
            clearInterval(timer);
          }
          element.textContent = Math.floor(current).toString() + (element.textContent?.includes('+') ? '+' : '');
        }, 40 + index * 10);
      });
    }
  }, []);

  return (
    <div className="k-desktop-page">
      {/* Hero Section */}
      <motion.section 
        className="k-hero-section"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="k-hero-content">
          <motion.h1 
            className="k-hero-title"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Zap className="k-hero-icon" />
            Kilat.js Desktop
          </motion.h1>
          
          <motion.p 
            className="k-hero-subtitle"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            Framework fullstack masa depan dari Nusantara
          </motion.p>
          
          <motion.p 
            className="k-hero-description"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            Cepat, modular, indah, dan tangguh - sekarang di desktop Anda
          </motion.p>

          <motion.div 
            className="k-hero-actions"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <button className="k-btn k-btn-primary k-btn-lg">
              <ExternalLink size={20} />
              Explore Demo
            </button>
            <button className="k-btn k-btn-outline k-btn-lg">
              <Github size={20} />
              View Source
            </button>
          </motion.div>
        </div>
      </motion.section>

      {/* Stats Section */}
      <motion.section 
        className="k-stats-section"
        ref={statsRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        <div className="k-stats-grid">
          <StatCard
            icon={<Layers />}
            value="50+"
            label="Components"
            description="Ready-to-use UI components"
          />
          <StatCard
            icon={<Palette />}
            value="15+"
            label="Themes"
            description="Beautiful themes including Cyberpunk & Nusantara"
          />
          <StatCard
            icon={<Plug />}
            value="25+"
            label="Plugins"
            description="Extensible plugin ecosystem"
          />
          <StatCard
            icon={<Database />}
            value="2"
            label="Databases"
            description="SQLite & MySQL support"
          />
        </div>
      </motion.section>

      {/* Features Section */}
      <motion.section 
        className="k-features-section"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
      >
        <h2 className="k-section-title">✨ Fitur Unggulan</h2>
        
        <div className="k-features-grid">
          <FeatureCard
            icon={<Palette />}
            title="UI Glow Futuristik"
            description="KilatCSS dengan efek neon dan tema cyberpunk yang memukau. Lebih dari 15 tema siap pakai."
            color="primary"
          />
          
          <FeatureCard
            icon={<Layers />}
            title="Animasi 3D Modular"
            description="KilatAnim.js dengan preset galaxy, matrix, dan neon tunnel. Dibangun dengan Three.js."
            color="secondary"
          />
          
          <FeatureCard
            icon={<Database />}
            title="Backend Internal"
            description="Server built-in dengan ORM adaptif untuk SQLite dan MySQL. File-based routing."
            color="accent"
          />
          
          <FeatureCard
            icon={<Plug />}
            title="Sistem Plugin"
            description="Ekosistem plugin untuk auth, CMS, payments, AI, dan monitoring real-time."
            color="primary"
          />
          
          <FeatureCard
            icon={<Shield />}
            title="Error Recovery"
            description="Crash recovery otomatis dengan monitoring, safe mode, dan kill switch."
            color="secondary"
          />
          
          <FeatureCard
            icon={<Globe />}
            title="Multi-Platform"
            description="Satu codebase untuk Web, Desktop (Electron), dan Mobile (React Native)."
            color="accent"
          />
        </div>
      </motion.section>

      {/* Platform Section */}
      <motion.section 
        className="k-platform-section"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.4 }}
      >
        <h2 className="k-section-title">🌐 Multi-Platform Support</h2>
        
        <div className="k-platform-grid">
          <PlatformCard
            icon={<Globe />}
            title="Web Application"
            description="Vite + React dengan SSR, PWA, dan static site generation"
            status="Ready"
          />
          
          <PlatformCard
            icon={<Monitor />}
            title="Desktop Application"
            description="Electron dengan native integrations dan auto-updater"
            status="Current"
          />
          
          <PlatformCard
            icon={<Smartphone />}
            title="Mobile Application"
            description="React Native + Expo untuk iOS dan Android"
            status="Ready"
          />
        </div>
      </motion.section>

      {/* Quick Start Section */}
      <motion.section 
        className="k-quickstart-section"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.6 }}
      >
        <div className="k-quickstart-card">
          <h2 className="k-section-title">🚀 Quick Start</h2>
          
          <div className="k-code-block">
            <div className="k-code-header">
              <span className="k-code-title">Terminal</span>
              <button className="k-code-copy">Copy</button>
            </div>
            <div className="k-code-content">
              <div className="k-code-line">
                <span className="k-code-comment"># Install Kilat.js</span>
              </div>
              <div className="k-code-line">
                <span className="k-code-command">bun create kilat-app my-app</span>
              </div>
              <div className="k-code-line">
                <span className="k-code-comment"># Start development</span>
              </div>
              <div className="k-code-line">
                <span className="k-code-command">cd my-app && bun dev</span>
              </div>
              <div className="k-code-line">
                <span className="k-code-comment"># Build for production</span>
              </div>
              <div className="k-code-line">
                <span className="k-code-command">bun run build</span>
              </div>
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
}

/**
 * 📊 Stat Card Component
 */
interface StatCardProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  description: string;
}

function StatCard({ icon, value, label, description }: StatCardProps) {
  return (
    <motion.div 
      className="k-stat-card"
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="k-stat-icon">{icon}</div>
      <div className="k-stat-value stat-value">{value}</div>
      <div className="k-stat-label">{label}</div>
      <div className="k-stat-description">{description}</div>
    </motion.div>
  );
}

/**
 * ✨ Feature Card Component
 */
interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: 'primary' | 'secondary' | 'accent';
}

function FeatureCard({ icon, title, description, color }: FeatureCardProps) {
  return (
    <motion.div 
      className={`k-feature-card k-feature-${color}`}
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="k-feature-icon">{icon}</div>
      <h3 className="k-feature-title">{title}</h3>
      <p className="k-feature-description">{description}</p>
    </motion.div>
  );
}

/**
 * 🌐 Platform Card Component
 */
interface PlatformCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  status: 'Ready' | 'Current' | 'Coming Soon';
}

function PlatformCard({ icon, title, description, status }: PlatformCardProps) {
  return (
    <motion.div 
      className="k-platform-card"
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="k-platform-header">
        <div className="k-platform-icon">{icon}</div>
        <div className={`k-platform-status k-status-${status.toLowerCase().replace(' ', '-')}`}>
          {status}
        </div>
      </div>
      <h3 className="k-platform-title">{title}</h3>
      <p className="k-platform-description">{description}</p>
    </motion.div>
  );
}
