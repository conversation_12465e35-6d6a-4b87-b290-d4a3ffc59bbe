import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Monitor, 
  Palette, 
  Volume2, 
  Bell,
  Shield,
  Database,
  Zap,
  RotateCcw,
  Download,
  Upload,
  Trash2
} from 'lucide-react';

interface SettingsPageProps {
  theme: string;
}

interface SettingsState {
  performance: {
    enableHardwareAcceleration: boolean;
    maxFPS: number;
    particleCount: number;
    enableBloom: boolean;
  };
  appearance: {
    enableAnimations: boolean;
    reducedMotion: boolean;
    fontSize: number;
    enableGlow: boolean;
  };
  audio: {
    enableSounds: boolean;
    volume: number;
    enableHaptics: boolean;
  };
  notifications: {
    enableNotifications: boolean;
    enableUpdateNotifications: boolean;
    enableErrorReporting: boolean;
  };
  privacy: {
    enableAnalytics: boolean;
    enableCrashReporting: boolean;
    enableUsageStats: boolean;
  };
  storage: {
    cacheSize: number;
    enableAutoCleanup: boolean;
    dataRetention: number;
  };
}

const defaultSettings: SettingsState = {
  performance: {
    enableHardwareAcceleration: true,
    maxFPS: 60,
    particleCount: 1000,
    enableBloom: true
  },
  appearance: {
    enableAnimations: true,
    reducedMotion: false,
    fontSize: 14,
    enableGlow: true
  },
  audio: {
    enableSounds: true,
    volume: 50,
    enableHaptics: true
  },
  notifications: {
    enableNotifications: true,
    enableUpdateNotifications: true,
    enableErrorReporting: true
  },
  privacy: {
    enableAnalytics: false,
    enableCrashReporting: true,
    enableUsageStats: false
  },
  storage: {
    cacheSize: 100,
    enableAutoCleanup: true,
    dataRetention: 30
  }
};

const SettingsPage: React.FC<SettingsPageProps> = ({ theme }) => {
  const [settings, setSettings] = useState<SettingsState>(defaultSettings);
  const [activeSection, setActiveSection] = useState('performance');

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('kilat-settings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  const saveSettings = (newSettings: SettingsState) => {
    setSettings(newSettings);
    localStorage.setItem('kilat-settings', JSON.stringify(newSettings));
  };

  const updateSetting = (section: keyof SettingsState, key: string, value: any) => {
    const newSettings = {
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    };
    saveSettings(newSettings);
  };

  const resetSettings = () => {
    saveSettings(defaultSettings);
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'kilat-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          saveSettings(importedSettings);
        } catch (error) {
          console.error('Failed to import settings:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  const sections = [
    { id: 'performance', label: 'Performance', icon: Zap },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'audio', label: 'Audio', icon: Volume2 },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'storage', label: 'Storage', icon: Database }
  ];

  const renderSection = () => {
    switch (activeSection) {
      case 'performance':
        return (
          <div className="k-settings-section">
            <h3>Performance Settings</h3>
            <div className="k-settings-group">
              <div className="k-setting-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.performance.enableHardwareAcceleration}
                    onChange={(e) => updateSetting('performance', 'enableHardwareAcceleration', e.target.checked)}
                  />
                  Enable Hardware Acceleration
                </label>
                <p>Use GPU acceleration for better performance</p>
              </div>
              <div className="k-setting-item">
                <label>Max FPS: {settings.performance.maxFPS}</label>
                <input
                  type="range"
                  min="30"
                  max="120"
                  value={settings.performance.maxFPS}
                  onChange={(e) => updateSetting('performance', 'maxFPS', parseInt(e.target.value))}
                />
              </div>
              <div className="k-setting-item">
                <label>Particle Count: {settings.performance.particleCount}</label>
                <input
                  type="range"
                  min="100"
                  max="5000"
                  step="100"
                  value={settings.performance.particleCount}
                  onChange={(e) => updateSetting('performance', 'particleCount', parseInt(e.target.value))}
                />
              </div>
              <div className="k-setting-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.performance.enableBloom}
                    onChange={(e) => updateSetting('performance', 'enableBloom', e.target.checked)}
                  />
                  Enable Bloom Effects
                </label>
              </div>
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div className="k-settings-section">
            <h3>Appearance Settings</h3>
            <div className="k-settings-group">
              <div className="k-setting-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.appearance.enableAnimations}
                    onChange={(e) => updateSetting('appearance', 'enableAnimations', e.target.checked)}
                  />
                  Enable Animations
                </label>
              </div>
              <div className="k-setting-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.appearance.reducedMotion}
                    onChange={(e) => updateSetting('appearance', 'reducedMotion', e.target.checked)}
                  />
                  Reduced Motion
                </label>
              </div>
              <div className="k-setting-item">
                <label>Font Size: {settings.appearance.fontSize}px</label>
                <input
                  type="range"
                  min="12"
                  max="20"
                  value={settings.appearance.fontSize}
                  onChange={(e) => updateSetting('appearance', 'fontSize', parseInt(e.target.value))}
                />
              </div>
              <div className="k-setting-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.appearance.enableGlow}
                    onChange={(e) => updateSetting('appearance', 'enableGlow', e.target.checked)}
                  />
                  Enable Glow Effects
                </label>
              </div>
            </div>
          </div>
        );

      // Add other sections...
      default:
        return (
          <div className="k-settings-section">
            <h3>{sections.find(s => s.id === activeSection)?.label} Settings</h3>
            <p>Settings for this section are coming soon...</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      className={`k-settings-page k-theme-${theme}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="k-settings-header">
        <div className="k-settings-title">
          <Settings className="k-settings-icon" />
          <h1>Settings</h1>
          <p>Customize your Kilat.js experience</p>
        </div>

        <div className="k-settings-actions">
          <button className="k-settings-btn" onClick={exportSettings}>
            <Download size={16} />
            Export
          </button>
          <label className="k-settings-btn">
            <Upload size={16} />
            Import
            <input
              type="file"
              accept=".json"
              onChange={importSettings}
              style={{ display: 'none' }}
            />
          </label>
          <button className="k-settings-btn k-settings-btn-danger" onClick={resetSettings}>
            <RotateCcw size={16} />
            Reset
          </button>
        </div>
      </div>

      {/* Settings Layout */}
      <div className="k-settings-layout">
        {/* Sidebar */}
        <div className="k-settings-sidebar">
          {sections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                className={`k-settings-nav-item ${activeSection === section.id ? 'k-settings-nav-item-active' : ''}`}
                onClick={() => setActiveSection(section.id)}
              >
                <Icon size={20} />
                <span>{section.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="k-settings-content">
          {renderSection()}
        </div>
      </div>
    </motion.div>
  );
};

export default SettingsPage;
