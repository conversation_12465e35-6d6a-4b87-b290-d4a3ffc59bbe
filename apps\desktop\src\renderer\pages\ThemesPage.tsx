import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Palette, 
  Check, 
  Download,
  <PERSON>,
  Settings,
  Moon,
  Sun,
  Zap,
  Sparkles
} from 'lucide-react';

interface ThemesPageProps {
  theme: string;
  onThemeChange: (theme: string) => void;
}

const themes = [
  {
    id: 'cyberpunk',
    name: 'Cyberpunk',
    description: 'Neon-lit futuristic interface with electric blue accents',
    colors: ['#000011', '#00ffff', '#ff00ff', '#ffff00'],
    icon: Zap,
    category: 'Dark'
  },
  {
    id: 'nusantara',
    name: 'Nusantara',
    description: 'Indonesian-inspired theme with warm golden tones',
    colors: ['#1a0f0a', '#d4af37', '#8b4513', '#ff6b35'],
    icon: Sparkles,
    category: 'Dark'
  },
  {
    id: 'minimalist',
    name: 'Minimalist',
    description: 'Clean and simple design with subtle shadows',
    colors: ['#ffffff', '#f8f9fa', '#6c757d', '#007bff'],
    icon: Sun,
    category: 'Light'
  },
  {
    id: 'retro',
    name: 'Retro Wave',
    description: 'Synthwave-inspired with purple and pink gradients',
    colors: ['#0f0f23', '#ff006e', '#8338ec', '#3a86ff'],
    icon: Moon,
    category: 'Dark'
  },
  {
    id: 'aurora',
    name: 'Aurora',
    description: 'Northern lights inspired with green and blue hues',
    colors: ['#001122', '#00ff88', '#0088ff', '#88ff00'],
    icon: Sparkles,
    category: 'Dark'
  }
];

const ThemesPage: React.FC<ThemesPageProps> = ({ theme, onThemeChange }) => {
  const [previewTheme, setPreviewTheme] = useState<string | null>(null);
  const [category, setCategory] = useState<'All' | 'Light' | 'Dark'>('All');

  const filteredThemes = themes.filter(t => 
    category === 'All' || t.category === category
  );

  const handlePreview = (themeId: string) => {
    setPreviewTheme(themeId);
    // Apply preview temporarily
    document.documentElement.setAttribute('data-kilat-theme', themeId);
  };

  const handleApply = (themeId: string) => {
    setPreviewTheme(null);
    onThemeChange(themeId);
  };

  const handleCancelPreview = () => {
    setPreviewTheme(null);
    // Restore original theme
    document.documentElement.setAttribute('data-kilat-theme', theme);
  };

  return (
    <motion.div
      className={`k-themes-page k-theme-${previewTheme || theme}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="k-themes-header">
        <div className="k-themes-title">
          <Palette className="k-themes-icon" />
          <h1>Theme Gallery</h1>
          <p>Customize your Kilat.js experience</p>
        </div>

        {previewTheme && (
          <motion.div
            className="k-themes-preview-controls"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <span>Previewing: {themes.find(t => t.id === previewTheme)?.name}</span>
            <button
              className="k-themes-btn k-themes-btn-primary"
              onClick={() => handleApply(previewTheme)}
            >
              <Check size={16} />
              Apply
            </button>
            <button
              className="k-themes-btn k-themes-btn-secondary"
              onClick={handleCancelPreview}
            >
              Cancel
            </button>
          </motion.div>
        )}
      </div>

      {/* Category Filter */}
      <div className="k-themes-filters">
        {(['All', 'Light', 'Dark'] as const).map((cat) => (
          <button
            key={cat}
            className={`k-themes-filter ${category === cat ? 'k-themes-filter-active' : ''}`}
            onClick={() => setCategory(cat)}
          >
            {cat}
          </button>
        ))}
      </div>

      {/* Theme Grid */}
      <div className="k-themes-grid">
        {filteredThemes.map((themeItem) => {
          const Icon = themeItem.icon;
          const isActive = theme === themeItem.id;
          const isPreviewing = previewTheme === themeItem.id;

          return (
            <motion.div
              key={themeItem.id}
              className={`k-theme-card ${isActive ? 'k-theme-card-active' : ''} ${isPreviewing ? 'k-theme-card-preview' : ''}`}
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              layout
            >
              {/* Theme Preview */}
              <div className="k-theme-preview">
                <div className="k-theme-colors">
                  {themeItem.colors.map((color, index) => (
                    <div
                      key={index}
                      className="k-theme-color"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <div className="k-theme-mockup">
                  <div className="k-theme-mockup-header" style={{ backgroundColor: themeItem.colors[0] }}>
                    <Icon size={16} style={{ color: themeItem.colors[1] }} />
                  </div>
                  <div className="k-theme-mockup-content" style={{ backgroundColor: themeItem.colors[0] }}>
                    <div className="k-theme-mockup-text" style={{ backgroundColor: themeItem.colors[1] }} />
                    <div className="k-theme-mockup-text" style={{ backgroundColor: themeItem.colors[2] }} />
                  </div>
                </div>
              </div>

              {/* Theme Info */}
              <div className="k-theme-info">
                <div className="k-theme-meta">
                  <h3>{themeItem.name}</h3>
                  <span className="k-theme-category">{themeItem.category}</span>
                </div>
                <p>{themeItem.description}</p>
              </div>

              {/* Theme Actions */}
              <div className="k-theme-actions">
                {!isActive && !isPreviewing && (
                  <>
                    <button
                      className="k-theme-btn k-theme-btn-preview"
                      onClick={() => handlePreview(themeItem.id)}
                    >
                      <Eye size={16} />
                      Preview
                    </button>
                    <button
                      className="k-theme-btn k-theme-btn-apply"
                      onClick={() => handleApply(themeItem.id)}
                    >
                      <Check size={16} />
                      Apply
                    </button>
                  </>
                )}

                {isActive && (
                  <div className="k-theme-active">
                    <Check size={16} />
                    Active
                  </div>
                )}

                {isPreviewing && (
                  <div className="k-theme-previewing">
                    <Eye size={16} />
                    Previewing
                  </div>
                )}
              </div>

              {/* Active Indicator */}
              {isActive && (
                <motion.div
                  className="k-theme-indicator"
                  layoutId="theme-indicator"
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                />
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Theme Customization */}
      <div className="k-themes-customization">
        <h2>Theme Customization</h2>
        <div className="k-themes-custom-grid">
          <div className="k-themes-custom-card">
            <Settings className="k-themes-custom-icon" />
            <h3>Custom Colors</h3>
            <p>Create your own color palette</p>
            <button className="k-themes-btn k-themes-btn-outline">
              Coming Soon
            </button>
          </div>
          <div className="k-themes-custom-card">
            <Download className="k-themes-custom-icon" />
            <h3>Import Theme</h3>
            <p>Load themes from files</p>
            <button className="k-themes-btn k-themes-btn-outline">
              Coming Soon
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ThemesPage;
