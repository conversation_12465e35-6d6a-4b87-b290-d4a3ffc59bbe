/* 🖥️ Kilat.js Desktop Styles */

/* Base Desktop App Styles */
.k-desktop-app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.k-desktop-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  opacity: 0.3;
}

/* Title Bar Styles */
.k-titlebar {
  display: flex;
  align-items: center;
  height: 32px;
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
  -webkit-app-region: drag;
  user-select: none;
  padding: 0 8px;
  position: relative;
  z-index: 1000;
}

.k-titlebar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  -webkit-app-region: no-drag;
}

.k-app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--k-primary);
}

.k-app-info {
  display: flex;
  flex-direction: column;
  line-height: 1;
}

.k-app-name {
  font-size: 11px;
  font-weight: 600;
  color: var(--k-text);
}

.k-app-version {
  font-size: 9px;
  color: var(--k-muted);
}

.k-titlebar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.k-title-text {
  font-size: 11px;
  color: var(--k-muted);
  font-weight: 500;
}

.k-titlebar-right {
  display: flex;
  align-items: center;
  gap: 4px;
  -webkit-app-region: no-drag;
}

.k-titlebar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--k-muted);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-titlebar-btn:hover {
  background: var(--k-hover);
  color: var(--k-text);
}

.k-close-btn:hover {
  background: #ff4757 !important;
  color: white !important;
}

/* Theme Selector */
.k-theme-selector {
  position: relative;
}

.k-theme-btn {
  position: relative;
}

.k-theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 8px;
  padding: 8px;
  min-width: 150px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.2s ease;
  z-index: 1001;
}

.k-theme-selector:hover .k-theme-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.k-theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 6px 8px;
  border: none;
  background: transparent;
  color: var(--k-text);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.k-theme-option:hover {
  background: var(--k-hover);
}

.k-theme-option.active {
  background: var(--k-primary);
  color: var(--k-background);
}

.k-theme-icon {
  font-size: 14px;
}

.k-theme-label {
  font-weight: 500;
}

/* Layout Styles */
.k-desktop-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.k-desktop-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.k-desktop-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* Page Styles */
.k-desktop-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.k-hero-section {
  text-align: center;
  padding: 60px 0;
  margin-bottom: 40px;
}

.k-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.k-hero-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--k-primary);
  margin-bottom: 16px;
  text-shadow: 0 0 20px var(--k-primary);
}

.k-hero-icon {
  width: 56px;
  height: 56px;
}

.k-hero-subtitle {
  font-size: 1.5rem;
  color: var(--k-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.k-hero-description {
  font-size: 1.1rem;
  color: var(--k-muted);
  margin-bottom: 32px;
}

.k-hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Stats Section */
.k-stats-section {
  margin-bottom: 60px;
}

.k-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.k-stat-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.k-stat-card:hover {
  border-color: var(--k-primary);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
}

.k-stat-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  color: var(--k-primary);
}

.k-stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--k-primary);
  margin-bottom: 8px;
  font-family: 'JetBrains Mono', monospace;
}

.k-stat-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 4px;
}

.k-stat-description {
  font-size: 0.9rem;
  color: var(--k-muted);
  line-height: 1.4;
}

/* Features Section */
.k-features-section {
  margin-bottom: 60px;
}

.k-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 40px;
  color: var(--k-text);
}

.k-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.k-feature-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.k-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--k-primary), var(--k-secondary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.k-feature-card:hover::before {
  opacity: 1;
}

.k-feature-card:hover {
  border-color: var(--k-primary);
  box-shadow: 0 8px 32px rgba(0, 255, 255, 0.1);
}

.k-feature-icon {
  width: 56px;
  height: 56px;
  margin-bottom: 20px;
  color: var(--k-primary);
}

.k-feature-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--k-text);
  margin-bottom: 12px;
}

.k-feature-description {
  color: var(--k-muted);
  line-height: 1.6;
  font-size: 1rem;
}

/* Platform Section */
.k-platform-section {
  margin-bottom: 60px;
}

.k-platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.k-platform-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.k-platform-card:hover {
  border-color: var(--k-primary);
  transform: translateY(-4px);
}

.k-platform-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.k-platform-icon {
  width: 40px;
  height: 40px;
  color: var(--k-primary);
}

.k-platform-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.k-status-ready {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.k-status-current {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.k-status-coming-soon {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.k-platform-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 8px;
}

.k-platform-description {
  color: var(--k-muted);
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Quick Start Section */
.k-quickstart-section {
  margin-bottom: 40px;
}

.k-quickstart-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 16px;
  padding: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.k-code-block {
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.k-code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
}

.k-code-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--k-text);
}

.k-code-copy {
  padding: 4px 8px;
  border: 1px solid var(--k-border);
  background: transparent;
  color: var(--k-muted);
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-code-copy:hover {
  color: var(--k-primary);
  border-color: var(--k-primary);
}

.k-code-content {
  padding: 16px;
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
}

.k-code-line {
  margin-bottom: 4px;
}

.k-code-comment {
  color: var(--k-muted);
}

.k-code-command {
  color: var(--k-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .k-desktop-content {
    padding: 16px;
  }
  
  .k-hero-title {
    font-size: 2.5rem;
  }
  
  .k-features-grid {
    grid-template-columns: 1fr;
  }
  
  .k-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Theme-specific overrides */
.k-theme-cyberpunk {
  --glow-color: #00ffff;
}

.k-theme-nusantara {
  --glow-color: #FFD700;
}

.k-theme-minimalist {
  --glow-color: #666666;
}

.k-theme-retro {
  --glow-color: #ff00ff;
}

.k-theme-aurora {
  --glow-color: #50C878;
}
