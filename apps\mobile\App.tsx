import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  Platform,
  Dimensions
} from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import { LinearGradient } from 'expo-linear-gradient';
// import { usePlatform } from 'kilat-core';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import DemoScreen from './src/screens/DemoScreen';
import ThemesScreen from './src/screens/ThemesScreen';
import SettingsScreen from './src/screens/SettingsScreen';

// Import components
import { KilatProvider } from './src/providers/KilatProvider';
import { ThemeProvider } from './src/providers/ThemeProvider';
import { TabBarIcon } from './src/components/TabBarIcon';
import { LoadingScreen } from './src/components/LoadingScreen';

// Navigation types
const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Keep splash screen visible while loading
SplashScreen.preventAutoHideAsync();

/**
 * 📱 Kilat.js Mobile App
 * React Native + Expo showcase application
 */
export default function App() {
  const [isReady, setIsReady] = useState(false);
  const [theme, setTheme] = useState('cyberpunk');
  // const platform = usePlatform();

  useEffect(() => {
    async function prepare() {
      try {
        // Load fonts (commented out for now - add actual font files later)
        // await Font.loadAsync({
        //   'SpaceMono': require('./assets/fonts/SpaceMono-Regular.ttf'),
        //   'Orbitron': require('./assets/fonts/Orbitron-Regular.ttf'),
        // });

        // Simulate loading time for demo
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.warn('Error loading app:', error);
      } finally {
        setIsReady(true);
        await SplashScreen.hideAsync();
      }
    }

    prepare();
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <KilatProvider>
      <ThemeProvider theme={theme} onThemeChange={setTheme}>
        <NavigationContainer theme={getNavigationTheme(theme)}>
          <SafeAreaView style={styles.container}>
            <StatusBar 
              style={theme === 'cyberpunk' ? 'light' : 'auto'} 
              backgroundColor="transparent"
              translucent
            />
            
            {/* Background Gradient */}
            <LinearGradient
              colors={getThemeGradient(theme)}
              style={StyleSheet.absoluteFillObject}
            />
            
            {/* Main Navigation */}
            <TabNavigator theme={theme} />
          </SafeAreaView>
        </NavigationContainer>
      </ThemeProvider>
    </KilatProvider>
  );
}

/**
 * 🧭 Tab Navigator Component
 */
function TabNavigator({ theme }: { theme: string }) {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => (
          <TabBarIcon
            name={getTabIconName(route.name)}
            focused={focused}
            color={color}
            size={size}
          />
        ),
        tabBarActiveTintColor: getThemeColors(theme).primary,
        tabBarInactiveTintColor: getThemeColors(theme).muted,
        tabBarStyle: {
          backgroundColor: getThemeColors(theme).surface,
          borderTopColor: getThemeColors(theme).border,
          borderTopWidth: 1,
          paddingBottom: Platform.OS === 'ios' ? 20 : 10,
          height: Platform.OS === 'ios' ? 90 : 70,
        },
        tabBarLabelStyle: {
          fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
          fontSize: 12,
          fontWeight: '600',
        },
        headerStyle: {
          backgroundColor: getThemeColors(theme).surface,
          borderBottomColor: getThemeColors(theme).border,
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTitleStyle: {
          fontFamily: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'sans-serif',
          fontSize: 18,
          fontWeight: 'bold',
          color: getThemeColors(theme).text,
        },
        headerTintColor: getThemeColors(theme).primary,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          title: '⚡ Kilat.js',
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen 
        name="Demo" 
        component={DemoScreen}
        options={{
          title: '🚀 Demo',
          tabBarLabel: 'Demo',
        }}
      />
      <Tab.Screen 
        name="Themes" 
        component={ThemesScreen}
        options={{
          title: '🎨 Themes',
          tabBarLabel: 'Themes',
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: '⚙️ Settings',
          tabBarLabel: 'Settings',
        }}
      />
    </Tab.Navigator>
  );
}

/**
 * 🎨 Theme Helper Functions
 */
function getTabIconName(routeName: string): string {
  switch (routeName) {
    case 'Home':
      return 'home';
    case 'Demo':
      return 'play-circle';
    case 'Themes':
      return 'palette';
    case 'Settings':
      return 'settings';
    default:
      return 'help-circle';
  }
}

function getThemeColors(theme: string) {
  const themes = {
    cyberpunk: {
      primary: '#00ffff',
      secondary: '#ff00ff',
      accent: '#ffff00',
      background: '#000011',
      surface: '#001122',
      text: '#ffffff',
      muted: '#888888',
      border: '#333333',
    },
    nusantara: {
      primary: '#8B4513',
      secondary: '#FFD700',
      accent: '#50C878',
      background: '#2F1B14',
      surface: '#3D2317',
      text: '#F5E6D3',
      muted: '#A0826D',
      border: '#5D4037',
    },
    minimalist: {
      primary: '#000000',
      secondary: '#666666',
      accent: '#333333',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#000000',
      muted: '#666666',
      border: '#e1e5e9',
    },
  };

  return themes[theme as keyof typeof themes] || themes.cyberpunk;
}

function getThemeGradient(theme: string): string[] {
  const gradients = {
    cyberpunk: ['#000011', '#001122', '#000033'],
    nusantara: ['#2F1B14', '#3D2317', '#4A2C20'],
    minimalist: ['#ffffff', '#f8f9fa', '#e9ecef'],
  };

  return gradients[theme as keyof typeof gradients] || gradients.cyberpunk;
}

function getNavigationTheme(theme: string) {
  const colors = getThemeColors(theme);
  
  return {
    dark: theme !== 'minimalist',
    colors: {
      primary: colors.primary,
      background: colors.background,
      card: colors.surface,
      text: colors.text,
      border: colors.border,
      notification: colors.accent,
    },
  };
}

/**
 * 🎨 Styles
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

/**
 * 📱 Platform-specific configurations
 */
const { width, height } = Dimensions.get('window');

// Export app configuration
export const appConfig = {
  name: 'Kilat.js Mobile',
  version: '1.0.0',
  platform: Platform.OS,
  dimensions: { width, height },
  isTablet: Platform.OS === 'ios' ? Platform.isPad : width > 768,
};
