import React from 'react';
import { Text, TextStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

interface GlowTextProps {
  children: React.ReactNode;
  style?: TextStyle;
  glowColor?: string;
  intensity?: number;
  animated?: boolean;
}

/**
 * ✨ Glow Text Component
 * Creates glowing text effect for mobile
 */
export function GlowText({ 
  children, 
  style, 
  glowColor = '#00ffff', 
  intensity = 1,
  animated = false 
}: GlowTextProps) {
  const shadowStyle = {
    textShadowColor: glowColor,
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10 * intensity,
  };

  // For more advanced glow effect, we can use MaskedView with gradient
  if (intensity > 1) {
    return (
      <MaskedView
        style={{ flexDirection: 'row', height: 'auto' }}
        maskElement={
          <Text style={[style, { backgroundColor: 'transparent' }]}>
            {children}
          </Text>
        }
      >
        <LinearGradient
          colors={[glowColor, '#ffffff', glowColor]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ flex: 1 }}
        />
      </MaskedView>
    );
  }

  return (
    <Text style={[style, shadowStyle]}>
      {children}
    </Text>
  );
}
