import React, { useEffect, useRef } from 'react';
import { View, Animated, Dimensions, StyleSheet } from 'react-native';

const { width, height } = Dimensions.get('window');

interface ParticleBackgroundProps {
  theme: string;
  particleCount?: number;
}

/**
 * 🌌 Particle Background Component
 * Animated floating particles for mobile background
 */
export function ParticleBackground({ theme, particleCount = 20 }: ParticleBackgroundProps) {
  const particles = useRef<Animated.Value[]>([]);
  const animationRefs = useRef<Animated.CompositeAnimation[]>([]);

  useEffect(() => {
    // Initialize particles
    particles.current = Array.from({ length: particleCount }, () => new Animated.Value(0));
    
    // Start animations
    particles.current.forEach((particle, index) => {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(particle, {
            toValue: 1,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
          Animated.timing(particle, {
            toValue: 0,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
        ]),
        { iterations: -1 }
      );
      
      // Stagger start times
      setTimeout(() => {
        animation.start();
      }, index * 200);
      
      animationRefs.current.push(animation);
    });

    return () => {
      // Cleanup animations
      animationRefs.current.forEach(animation => animation.stop());
    };
  }, [particleCount]);

  const getParticleColor = () => {
    switch (theme) {
      case 'cyberpunk':
        return '#00ffff';
      case 'nusantara':
        return '#FFD700';
      case 'minimalist':
        return '#666666';
      case 'retro':
        return '#ff00ff';
      case 'aurora':
        return '#50C878';
      default:
        return '#00ffff';
    }
  };

  const renderParticle = (index: number) => {
    const particle = particles.current[index];
    
    if (!particle) return null;

    const translateY = particle.interpolate({
      inputRange: [0, 1],
      outputRange: [height + 50, -50],
    });

    const opacity = particle.interpolate({
      inputRange: [0, 0.1, 0.9, 1],
      outputRange: [0, 1, 1, 0],
    });

    const scale = particle.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0, 1, 0],
    });

    const randomX = Math.random() * width;
    const randomSize = 2 + Math.random() * 4;

    return (
      <Animated.View
        key={index}
        style={[
          styles.particle,
          {
            left: randomX,
            width: randomSize,
            height: randomSize,
            backgroundColor: getParticleColor(),
            transform: [
              { translateY },
              { scale },
            ],
            opacity,
          },
        ]}
      />
    );
  };

  return (
    <View style={styles.container} pointerEvents="none">
      {Array.from({ length: particleCount }, (_, index) => renderParticle(index))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  particle: {
    position: 'absolute',
    borderRadius: 50,
    shadowColor: '#00ffff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 5,
  },
});
