import React, { useState, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Animated,
  Switch,
  Slider,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../providers/ThemeProvider';
import { GlowText } from '../components/GlowText';
import { AnimatedCard } from '../components/AnimatedCard';

/**
 * 🚀 Demo Screen - Interactive Showcase
 */
export default function DemoScreen() {
  const { theme, colors } = useTheme();
  const [glowIntensity, setGlowIntensity] = useState(1);
  const [animationEnabled, setAnimationEnabled] = useState(true);
  const [particleCount, setParticleCount] = useState(20);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  const startPulseAnimation = () => {
    Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1.1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleInteraction = (type: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    startPulseAnimation();
    console.log(`Demo interaction: ${type}`);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <GlowText 
            style={[styles.title, { color: colors.primary }]}
            glowColor={colors.primary}
            intensity={glowIntensity}
          >
            🚀 Interactive Demo
          </GlowText>
          <Text style={[styles.subtitle, { color: colors.muted }]}>
            Explore Kilat.js features in action
          </Text>
        </View>

        {/* Glow Controls */}
        <AnimatedCard style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            ✨ Glow Effects
          </Text>
          
          <View style={styles.controlRow}>
            <Text style={[styles.controlLabel, { color: colors.muted }]}>
              Intensity: {glowIntensity.toFixed(1)}
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={0.5}
              maximumValue={3}
              value={glowIntensity}
              onValueChange={setGlowIntensity}
              minimumTrackTintColor={colors.primary}
              maximumTrackTintColor={colors.border}
              thumbTintColor={colors.primary}
            />
          </View>

          <TouchableOpacity
            style={[styles.demoButton, { borderColor: colors.primary }]}
            onPress={() => handleInteraction('glow-test')}
            activeOpacity={0.8}
          >
            <GlowText
              style={[styles.buttonText, { color: colors.primary }]}
              glowColor={colors.primary}
              intensity={glowIntensity}
            >
              Test Glow Effect
            </GlowText>
          </TouchableOpacity>
        </AnimatedCard>

        {/* Animation Controls */}
        <AnimatedCard style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🌌 Animations
          </Text>
          
          <View style={styles.controlRow}>
            <Text style={[styles.controlLabel, { color: colors.muted }]}>
              Enable Animations
            </Text>
            <Switch
              value={animationEnabled}
              onValueChange={setAnimationEnabled}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={animationEnabled ? colors.background : colors.muted}
            />
          </View>

          <View style={styles.controlRow}>
            <Text style={[styles.controlLabel, { color: colors.muted }]}>
              Particles: {Math.round(particleCount)}
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={5}
              maximumValue={50}
              value={particleCount}
              onValueChange={setParticleCount}
              minimumTrackTintColor={colors.primary}
              maximumTrackTintColor={colors.border}
              thumbTintColor={colors.primary}
            />
          </View>

          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <TouchableOpacity
              style={[styles.demoButton, { borderColor: colors.secondary }]}
              onPress={() => handleInteraction('animation-test')}
              activeOpacity={0.8}
            >
              <Text style={[styles.buttonText, { color: colors.secondary }]}>
                Trigger Animation
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </AnimatedCard>

        {/* Theme Showcase */}
        <AnimatedCard style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🎨 Theme Showcase
          </Text>
          
          <View style={styles.themeGrid}>
            <ThemePreview
              name="Cyberpunk"
              colors={['#000011', '#00ffff', '#ff00ff']}
              onPress={() => handleInteraction('theme-cyberpunk')}
            />
            <ThemePreview
              name="Nusantara"
              colors={['#2F1B14', '#8B4513', '#FFD700']}
              onPress={() => handleInteraction('theme-nusantara')}
            />
            <ThemePreview
              name="Minimalist"
              colors={['#ffffff', '#000000', '#666666']}
              onPress={() => handleInteraction('theme-minimalist')}
            />
            <ThemePreview
              name="Aurora"
              colors={['#001122', '#50C878', '#87CEEB']}
              onPress={() => handleInteraction('theme-aurora')}
            />
          </View>
        </AnimatedCard>

        {/* Component Showcase */}
        <AnimatedCard style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🧩 Components
          </Text>
          
          <View style={styles.componentGrid}>
            <ComponentDemo
              title="Buttons"
              description="Interactive buttons with glow effects"
              colors={colors}
              onPress={() => handleInteraction('component-buttons')}
            />
            <ComponentDemo
              title="Cards"
              description="Animated cards with blur backgrounds"
              colors={colors}
              onPress={() => handleInteraction('component-cards')}
            />
            <ComponentDemo
              title="Forms"
              description="Futuristic form inputs and controls"
              colors={colors}
              onPress={() => handleInteraction('component-forms')}
            />
            <ComponentDemo
              title="Navigation"
              description="Smooth navigation with transitions"
              colors={colors}
              onPress={() => handleInteraction('component-navigation')}
            />
          </View>
        </AnimatedCard>

        {/* Performance Metrics */}
        <AnimatedCard style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            📊 Performance
          </Text>
          
          <View style={styles.metricsGrid}>
            <MetricCard
              label="FPS"
              value="60"
              unit="fps"
              colors={colors}
            />
            <MetricCard
              label="Memory"
              value="45"
              unit="MB"
              colors={colors}
            />
            <MetricCard
              label="Load Time"
              value="1.2"
              unit="s"
              colors={colors}
            />
            <MetricCard
              label="Bundle Size"
              value="2.1"
              unit="MB"
              colors={colors}
            />
          </View>
        </AnimatedCard>
      </ScrollView>
    </View>
  );
}

/**
 * 🎨 Theme Preview Component
 */
interface ThemePreviewProps {
  name: string;
  colors: string[];
  onPress: () => void;
}

function ThemePreview({ name, colors, onPress }: ThemePreviewProps) {
  return (
    <TouchableOpacity style={styles.themePreview} onPress={onPress} activeOpacity={0.8}>
      <LinearGradient
        colors={colors}
        style={styles.themeGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      <Text style={styles.themeName}>{name}</Text>
    </TouchableOpacity>
  );
}

/**
 * 🧩 Component Demo Card
 */
interface ComponentDemoProps {
  title: string;
  description: string;
  colors: any;
  onPress: () => void;
}

function ComponentDemo({ title, description, colors, onPress }: ComponentDemoProps) {
  return (
    <TouchableOpacity
      style={[styles.componentCard, { borderColor: colors.border }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Text style={[styles.componentTitle, { color: colors.text }]}>{title}</Text>
      <Text style={[styles.componentDescription, { color: colors.muted }]}>
        {description}
      </Text>
    </TouchableOpacity>
  );
}

/**
 * 📊 Metric Card Component
 */
interface MetricCardProps {
  label: string;
  value: string;
  unit: string;
  colors: any;
}

function MetricCard({ label, value, unit, colors }: MetricCardProps) {
  return (
    <View style={[styles.metricCard, { borderColor: colors.border }]}>
      <Text style={[styles.metricValue, { color: colors.primary }]}>
        {value}
        <Text style={[styles.metricUnit, { color: colors.muted }]}>{unit}</Text>
      </Text>
      <Text style={[styles.metricLabel, { color: colors.muted }]}>{label}</Text>
    </View>
  );
}

/**
 * 🎨 Styles
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    fontFamily: 'Orbitron',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 20,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  controlLabel: {
    fontSize: 14,
    flex: 1,
  },
  slider: {
    flex: 2,
    marginLeft: 16,
  },
  demoButton: {
    borderWidth: 2,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  themeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  themePreview: {
    width: '48%',
    aspectRatio: 1.5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  themeGradient: {
    flex: 1,
    justifyContent: 'flex-end',
    padding: 12,
  },
  themeName: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  componentGrid: {
    gap: 12,
  },
  componentCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
  },
  componentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  componentDescription: {
    fontSize: 14,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricCard: {
    width: '48%',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: 'SpaceMono',
  },
  metricUnit: {
    fontSize: 14,
    fontWeight: 'normal',
  },
  metricLabel: {
    fontSize: 12,
    marginTop: 4,
  },
});
