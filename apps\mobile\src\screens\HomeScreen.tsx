import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../providers/ThemeProvider';
import { GlowText } from '../components/GlowText';
import { AnimatedCard } from '../components/AnimatedCard';
import { ParticleBackground } from '../components/ParticleBackground';
import { FeatureCard } from '../components/FeatureCard';

const { width, height } = Dimensions.get('window');

/**
 * 🏠 Home Screen - Kilat.js Mobile Showcase
 */
export default function HomeScreen() {
  const { theme, colors } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleFeaturePress = (feature: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log(`Feature pressed: ${feature}`);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Particle Background */}
      <ParticleBackground theme={theme} />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Hero Section */}
        <Animated.View 
          style={[
            styles.heroSection,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          <GlowText 
            style={[styles.heroTitle, { color: colors.primary }]}
            glowColor={colors.primary}
          >
            Kilat.js ⚡
          </GlowText>
          
          <Text style={[styles.heroSubtitle, { color: colors.text }]}>
            Framework masa depan dari Nusantara
          </Text>
          
          <Text style={[styles.heroDescription, { color: colors.muted }]}>
            Cepat, modular, indah, dan tangguh
          </Text>

          {/* Stats Cards */}
          <View style={styles.statsContainer}>
            <StatCard
              icon="🧩"
              value="50+"
              label="Components"
              colors={colors}
              onPress={() => handleFeaturePress('components')}
            />
            <StatCard
              icon="🎨"
              value="15+"
              label="Themes"
              colors={colors}
              onPress={() => handleFeaturePress('themes')}
            />
            <StatCard
              icon="🔌"
              value="25+"
              label="Plugins"
              colors={colors}
              onPress={() => handleFeaturePress('plugins')}
            />
          </View>
        </Animated.View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            ✨ Fitur Unggulan
          </Text>
          
          <View style={styles.featuresGrid}>
            <FeatureCard
              icon="🎨"
              title="UI Glow Futuristik"
              description="KilatCSS dengan efek neon dan tema cyberpunk yang memukau"
              colors={colors}
              onPress={() => handleFeaturePress('ui')}
            />
            
            <FeatureCard
              icon="🌌"
              title="Animasi 3D Modular"
              description="KilatAnim.js dengan preset galaxy, matrix, dan neon tunnel"
              colors={colors}
              onPress={() => handleFeaturePress('animations')}
            />
            
            <FeatureCard
              icon="🏝️"
              title="Tema Nusantara"
              description="Desain yang terinspirasi budaya Indonesia dengan sentuhan modern"
              colors={colors}
              onPress={() => handleFeaturePress('nusantara')}
            />
            
            <FeatureCard
              icon="⚙️"
              title="Backend Internal"
              description="Server built-in dengan ORM adaptif SQLite/MySQL"
              colors={colors}
              onPress={() => handleFeaturePress('backend')}
            />
            
            <FeatureCard
              icon="🔌"
              title="Sistem Plugin"
              description="Ekosistem plugin untuk auth, CMS, payments, dan AI"
              colors={colors}
              onPress={() => handleFeaturePress('plugins')}
            />
            
            <FeatureCard
              icon="🛡️"
              title="Error Recovery"
              description="Crash recovery otomatis dengan monitoring real-time"
              colors={colors}
              onPress={() => handleFeaturePress('recovery')}
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🚀 Quick Actions
          </Text>
          
          <View style={styles.actionsGrid}>
            <ActionButton
              icon="🎮"
              title="Try Demo"
              subtitle="Interactive showcase"
              colors={colors}
              onPress={() => handleFeaturePress('demo')}
            />
            
            <ActionButton
              icon="🎨"
              title="Switch Theme"
              subtitle="15+ themes available"
              colors={colors}
              onPress={() => handleFeaturePress('theme-switch')}
            />
            
            <ActionButton
              icon="📚"
              title="Documentation"
              subtitle="Learn more"
              colors={colors}
              onPress={() => handleFeaturePress('docs')}
            />
            
            <ActionButton
              icon="⭐"
              title="GitHub"
              subtitle="Star the project"
              colors={colors}
              onPress={() => handleFeaturePress('github')}
            />
          </View>
        </View>

        {/* Platform Info */}
        <View style={styles.platformSection}>
          <BlurView intensity={20} style={styles.platformCard}>
            <Text style={[styles.platformTitle, { color: colors.text }]}>
              📱 Mobile Platform
            </Text>
            <Text style={[styles.platformInfo, { color: colors.muted }]}>
              Running on {Platform.OS} • React Native + Expo
            </Text>
            <Text style={[styles.platformInfo, { color: colors.muted }]}>
              Screen: {width}x{height}
            </Text>
          </BlurView>
        </View>
      </ScrollView>
    </View>
  );
}

/**
 * 📊 Stat Card Component
 */
interface StatCardProps {
  icon: string;
  value: string;
  label: string;
  colors: any;
  onPress: () => void;
}

function StatCard({ icon, value, label, colors, onPress }: StatCardProps) {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <AnimatedCard style={[styles.statCard, { backgroundColor: colors.surface }]}>
        <Text style={styles.statIcon}>{icon}</Text>
        <Text style={[styles.statValue, { color: colors.primary }]}>{value}</Text>
        <Text style={[styles.statLabel, { color: colors.muted }]}>{label}</Text>
      </AnimatedCard>
    </TouchableOpacity>
  );
}

/**
 * 🎯 Action Button Component
 */
interface ActionButtonProps {
  icon: string;
  title: string;
  subtitle: string;
  colors: any;
  onPress: () => void;
}

function ActionButton({ icon, title, subtitle, colors, onPress }: ActionButtonProps) {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <AnimatedCard style={[styles.actionButton, { backgroundColor: colors.surface }]}>
        <Text style={styles.actionIcon}>{icon}</Text>
        <View style={styles.actionContent}>
          <Text style={[styles.actionTitle, { color: colors.text }]}>{title}</Text>
          <Text style={[styles.actionSubtitle, { color: colors.muted }]}>{subtitle}</Text>
        </View>
      </AnimatedCard>
    </TouchableOpacity>
  );
}

/**
 * 🎨 Styles
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  heroSection: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 30,
  },
  heroTitle: {
    fontSize: 48,
    fontWeight: 'bold',
    fontFamily: 'Orbitron',
    textAlign: 'center',
    marginBottom: 10,
  },
  heroSubtitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 30,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 10,
  },
  statCard: {
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    minWidth: 80,
  },
  statIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'SpaceMono',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  featuresSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: 'Orbitron',
    marginBottom: 20,
    textAlign: 'center',
  },
  featuresGrid: {
    gap: 15,
  },
  actionsSection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  actionsGrid: {
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
  },
  actionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
  },
  platformSection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  platformCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  platformTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  platformInfo: {
    fontSize: 14,
    marginBottom: 4,
  },
});
