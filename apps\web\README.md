# 🌐 Kilat.js Web Demo Application

Aplikasi web demo yang menampilkan semua fitur dan kemampuan framework Kilat.js. Dibangun dengan React, TypeScript, dan Vite untuk performa optimal.

## ✨ Fitur

- **🎨 Interactive Demo** - Showcase semua komponen dan tema
- **🎮 Live Playground** - Code editor dengan preview real-time
- **📚 Documentation** - Dokumentasi lengkap dengan contoh
- **🌌 3D Animations** - Background animasi dengan KilatAnim.js
- **📱 Responsive Design** - Optimized untuk semua device
- **⚡ Fast Performance** - Built dengan Vite dan optimasi modern
- **🎯 SEO Optimized** - Meta tags dan structured data
- **♿ Accessible** - WCAG compliant dengan keyboard navigation

## 🚀 Quick Start

### Prerequisites

- Node.js >= 18.0.0 atau Bun >= 1.0.0
- Git

### Installation

```bash
# Clone repository
git clone https://github.com/kangpcode/kilat.js.git
cd kilat.js/apps/web

# Install dependencies
bun install
# atau
npm install

# Start development server
bun dev
# atau
npm run dev
```

Aplikasi akan ber<PERSON><PERSON> di `http://localhost:3000`

## 📁 Struktur Project

```
apps/web/
├── 📄 index.html              → HTML template
├── 📦 package.json            → Dependencies & scripts
├── ⚙️ vite.config.ts          → Vite configuration
├── 📂 public/                 → Static assets
│   ├── favicon.svg            → App icon
│   ├── manifest.json          → PWA manifest
│   └── sw.js                  → Service worker
├── 📂 src/                    → Source code
│   ├── 🎯 main.tsx            → App entry point
│   ├── 📱 App.tsx             → Main app component
│   ├── 📂 components/         → Reusable components
│   │   ├── Navigation.tsx     → Navigation bar
│   │   ├── Footer.tsx         → Footer component
│   │   ├── ErrorBoundary.tsx  → Error handling
│   │   └── LoadingSpinner.tsx → Loading states
│   ├── 📂 pages/              → Page components
│   │   ├── HomePage.tsx       → Landing page
│   │   ├── DemoPage.tsx       → Interactive demo
│   │   ├── PlaygroundPage.tsx → Code playground
│   │   ├── AboutPage.tsx      → About page
│   │   ├── DocsPage.tsx       → Documentation
│   │   └── NotFoundPage.tsx   → 404 page
│   ├── 📂 styles/             → CSS files
│   │   ├── global.css         → Global styles
│   │   ├── navigation.css     → Navigation styles
│   │   ├── components.css     → Component styles
│   │   ├── demo.css           → Demo page styles
│   │   ├── playground.css     → Playground styles
│   │   └── pages.css          → Page-specific styles
│   └── 📂 assets/             → Images & media
└── 📂 dist/                   → Build output
```

## 🛠️ Development

### Available Scripts

```bash
# Development
bun dev                    # Start dev server
bun build                  # Build for production
bun preview                # Preview production build

# Code Quality
bun lint                   # Run ESLint
bun lint:fix               # Fix ESLint errors
bun type-check             # TypeScript type checking
bun format                 # Format with Prettier
bun format:check           # Check formatting

# Testing
bun test                   # Run unit tests
bun test:ui                # Run tests with UI
bun test:coverage          # Generate coverage report
bun e2e                    # Run E2E tests
bun e2e:ui                 # Run E2E tests with UI

# Analysis
bun analyze                # Bundle size analysis
bun lighthouse             # Performance audit

# Deployment
bun deploy:vercel          # Deploy to Vercel
bun deploy:netlify         # Deploy to Netlify
bun deploy:surge           # Deploy to Surge
```

### Development Server

Development server berjalan dengan:
- **Hot Module Replacement (HMR)** untuk fast refresh
- **TypeScript** type checking
- **ESLint** untuk code quality
- **Prettier** untuk code formatting
- **Source maps** untuk debugging

### Build Process

Build production menggunakan:
- **Vite** untuk bundling yang cepat
- **ESBuild** untuk minification
- **Code splitting** untuk optimal loading
- **Asset optimization** untuk performa
- **Source maps** untuk debugging production

## 🎨 Theming

Aplikasi mendukung multiple themes:

```tsx
import { useTheme } from 'kilat-core';

function ThemeSelector() {
  const { theme, setTheme, availableThemes } = useTheme();
  
  return (
    <select value={theme} onChange={(e) => setTheme(e.target.value)}>
      {availableThemes.map(theme => (
        <option key={theme} value={theme}>{theme}</option>
      ))}
    </select>
  );
}
```

### Available Themes

- **cyberpunk** - Futuristik dengan neon effects
- **nusantara** - Traditional Indonesia modern
- **minimalist** - Clean dan simple
- **retro** - 80s nostalgia
- **aurora** - Northern lights
- **material** - Google Material Design
- Dan 10+ tema lainnya...

## 🌌 Animations

Menggunakan KilatAnim.js untuk 3D animations:

```tsx
import { KilatScene } from 'kilatanim.js';

function AnimatedBackground() {
  return (
    <KilatScene
      preset="galaxy"
      autoRotate={true}
      particleCount={100}
      className="background-scene"
    />
  );
}
```

### Animation Presets

- **galaxy** - Rotating galaxy dengan stars
- **matrix** - Digital rain effect
- **neonTunnel** - Cyberpunk tunnel
- **cyberwave** - Retro wave animation
- **particles** - Floating particles
- **hologram** - Holographic effect

## 📱 Responsive Design

Aplikasi fully responsive dengan breakpoints:

```css
/* Mobile First */
.component {
  /* Mobile styles */
}

@media (min-width: 768px) {
  .component {
    /* Tablet styles */
  }
}

@media (min-width: 1024px) {
  .component {
    /* Desktop styles */
  }
}
```

## ♿ Accessibility

Fitur accessibility yang diimplementasi:

- **Keyboard Navigation** - Semua interaksi dapat diakses via keyboard
- **Screen Reader Support** - ARIA labels dan semantic HTML
- **Focus Management** - Focus indicators yang jelas
- **Color Contrast** - WCAG AA compliant
- **Reduced Motion** - Respect user preferences

## 🚀 Performance

Optimasi performa yang diterapkan:

- **Code Splitting** - Lazy loading untuk routes
- **Asset Optimization** - Image compression dan format modern
- **Bundle Analysis** - Monitoring ukuran bundle
- **Caching Strategy** - Service worker untuk offline support
- **Preloading** - Critical resources preloading

### Performance Metrics

Target performance:
- **First Contentful Paint** < 1.5s
- **Largest Contentful Paint** < 2.5s
- **Cumulative Layout Shift** < 0.1
- **First Input Delay** < 100ms
- **Lighthouse Score** > 90

## 🔧 Configuration

### Environment Variables

```bash
# .env.local
VITE_API_URL=https://api.kilat-js.pcode.my.id
VITE_ANALYTICS_ID=your-analytics-id
VITE_SENTRY_DSN=your-sentry-dsn
```

### Vite Configuration

Konfigurasi Vite di `vite.config.ts`:

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  },
  build: {
    target: 'es2020',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'kilat-core': ['kilat-core']
        }
      }
    }
  }
});
```

## 🚀 Deployment

### Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
bun deploy:vercel
```

### Netlify

```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy
bun deploy:netlify
```

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

MIT License - lihat [LICENSE](../../LICENSE) untuk detail.

## 🆘 Support

- 📖 **Documentation**: [docs.kilat-js.pcode.my.id](https://docs.kilat-js.pcode.my.id)
- 💬 **Discord**: [discord.gg/kilatjs](https://discord.gg/kilatjs)
- 🐛 **Issues**: [GitHub Issues](https://github.com/kangpcode/kilat.js/issues)
- 📧 **Email**: <EMAIL>

---

**Dibuat dengan ❤️ menggunakan Kilat.js Framework**
