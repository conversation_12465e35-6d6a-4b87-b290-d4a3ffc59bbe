<!DOCTYPE html>
<html lang="en" data-theme="cyberpunk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Primary Meta Tags -->
  <title>Kilat.js - Framework Masa Depan dari Nusantara</title>
  <meta name="title" content="Kilat.js - Framework Masa Depan dari Nusantara" />
  <meta name="description" content="Framework fullstack yang cepat, modular, indah, dan tangguh. Menggabungkan React, TypeScript, dan Bun dengan tema cyberpunk dan nusantara." />
  <meta name="keywords" content="kilat.js, framework, fullstack, react, typescript, bun, cyberpunk, nusantara, indonesia, ui, animation, 3d" />
  <meta name="author" content="KangPCode" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://kilat-js.pcode.my.id/" />
  <meta property="og:title" content="Kilat.js - Framework Masa Depan dari Nusantara" />
  <meta property="og:description" content="Framework fullstack yang cepat, modular, indah, dan tangguh. Menggabungkan React, TypeScript, dan Bun dengan tema cyberpunk dan nusantara." />
  <meta property="og:image" content="https://kilat-js.pcode.my.id/og-image.png" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://kilat-js.pcode.my.id/" />
  <meta property="twitter:title" content="Kilat.js - Framework Masa Depan dari Nusantara" />
  <meta property="twitter:description" content="Framework fullstack yang cepat, modular, indah, dan tangguh. Menggabungkan React, TypeScript, dan Bun dengan tema cyberpunk dan nusantara." />
  <meta property="twitter:image" content="https://kilat-js.pcode.my.id/og-image.png" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- Manifest -->
  <link rel="manifest" href="/manifest.json" />
  
  <!-- Theme Color -->
  <meta name="theme-color" content="#00ffff" />
  <meta name="msapplication-TileColor" content="#00ffff" />
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
  
  <!-- CSS Variables for themes -->
  <style>
    :root {
      /* Cyberpunk Theme (Default) */
      --k-primary: #00ffff;
      --k-primary-rgb: 0, 255, 255;
      --k-primary-foreground: #000000;
      --k-primary-hover: #00e6e6;
      --k-primary-glow: rgba(0, 255, 255, 0.5);
      
      --k-accent: #ff0080;
      --k-accent-rgb: 255, 0, 128;
      --k-accent-foreground: #ffffff;
      
      --k-background: #0a0a0a;
      --k-background-rgb: 10, 10, 10;
      --k-surface: #1a1a1a;
      --k-surface-rgb: 26, 26, 26;
      
      --k-text: #ffffff;
      --k-text-muted: #a0a0a0;
      --k-text-rgb: 255, 255, 255;
      
      --k-border: #333333;
      --k-border-rgb: 51, 51, 51;
      
      --k-destructive: #ff4444;
      --k-warning: #ffaa00;
      --k-success: #00ff88;
      --k-info: #0088ff;
      
      /* Fonts */
      --k-font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --k-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
      
      /* Spacing */
      --k-spacing-xs: 0.25rem;
      --k-spacing-sm: 0.5rem;
      --k-spacing-md: 1rem;
      --k-spacing-lg: 1.5rem;
      --k-spacing-xl: 2rem;
      --k-spacing-2xl: 3rem;
      
      /* Border Radius */
      --k-radius-sm: 0.25rem;
      --k-radius-md: 0.5rem;
      --k-radius-lg: 1rem;
      --k-radius-xl: 1.5rem;
      
      /* Shadows */
      --k-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
      --k-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
      --k-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
      --k-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
      
      /* Transitions */
      --k-transition-fast: 150ms ease;
      --k-transition-normal: 300ms ease;
      --k-transition-slow: 500ms ease;
    }
    
    /* Base styles */
    * {
      box-sizing: border-box;
    }
    
    html {
      font-family: var(--k-font-sans);
      line-height: 1.5;
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    body {
      margin: 0;
      padding: 0;
      background: var(--k-background);
      color: var(--k-text);
      font-family: var(--k-font-sans);
      overflow-x: hidden;
    }
    
    #root {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Loading screen */
    .k-loading-screen {
      position: fixed;
      inset: 0;
      background: var(--k-background);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }
    
    .k-loading-screen.hidden {
      opacity: 0;
      pointer-events: none;
    }
    
    .k-loading-content {
      text-align: center;
    }
    
    .k-loading-logo {
      font-size: 3rem;
      color: var(--k-primary);
      margin-bottom: 1rem;
      filter: drop-shadow(0 0 20px var(--k-primary-glow));
      animation: pulse 2s ease-in-out infinite;
    }
    
    .k-loading-text {
      color: var(--k-text-muted);
      font-size: 1rem;
      margin-bottom: 2rem;
    }
    
    .k-loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--k-border);
      border-top: 3px solid var(--k-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--k-surface);
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--k-border);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: var(--k-primary);
    }
    
    /* Selection styling */
    ::selection {
      background: var(--k-primary);
      color: var(--k-primary-foreground);
    }
    
    ::-moz-selection {
      background: var(--k-primary);
      color: var(--k-primary-foreground);
    }
  </style>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/src/main.tsx" as="script" />
  
  <!-- Analytics (if needed) -->
  <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="k-loading-screen">
    <div class="k-loading-content">
      <div class="k-loading-logo">⚡</div>
      <div class="k-loading-text">Loading Kilat.js...</div>
      <div class="k-loading-spinner"></div>
    </div>
  </div>
  
  <!-- App Root -->
  <div id="root"></div>
  
  <!-- Scripts -->
  <script type="module" src="/src/main.tsx"></script>
  
  <!-- Service Worker Registration -->
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
    
    // Hide loading screen when app is ready
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.classList.add('hidden');
          setTimeout(() => {
            loadingScreen.remove();
          }, 500);
        }
      }, 1000);
    });
    
    // Theme detection and application
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const savedTheme = localStorage.getItem('kilat-theme');
    const theme = savedTheme || (prefersDark ? 'cyberpunk' : 'minimalist');
    
    document.documentElement.setAttribute('data-theme', theme);
    
    // Analytics (if needed)
    /*
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID');
    */
  </script>
</body>
</html>
