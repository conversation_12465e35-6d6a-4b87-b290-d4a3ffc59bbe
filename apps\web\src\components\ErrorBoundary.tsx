import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * 🛡️ Error Boundary Component
 * Catches JavaScript errors anywhere in the child component tree
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service
    if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
      this.reportError(error, errorInfo);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Send error to monitoring service
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Example: Send to your error reporting service
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorData)
    }).catch(console.error);
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleToggleDetails = () => {
    const details = document.getElementById('error-details');
    if (details) {
      details.style.display = details.style.display === 'none' ? 'block' : 'none';
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="k-error-boundary">
          <div className="k-error-container">
            <div className="k-error-icon">
              <AlertTriangle size={64} />
            </div>
            
            <h1 className="k-error-title">
              Oops! Something went wrong
            </h1>
            
            <p className="k-error-message">
              We're sorry, but something unexpected happened. 
              The error has been reported and we'll fix it as soon as possible.
            </p>

            <div className="k-error-actions">
              <button 
                onClick={this.handleReload}
                className="k-btn k-btn-primary"
              >
                <RefreshCw size={20} />
                Reload Page
              </button>
              
              <button 
                onClick={this.handleGoHome}
                className="k-btn k-btn-outline"
              >
                <Home size={20} />
                Go Home
              </button>
              
              <button 
                onClick={this.handleToggleDetails}
                className="k-btn k-btn-ghost"
              >
                <Bug size={20} />
                Show Details
              </button>
            </div>

            {/* Error Details (Hidden by default) */}
            <div id="error-details" className="k-error-details" style={{ display: 'none' }}>
              <h3>Error Details:</h3>
              <div className="k-error-code">
                <strong>Error:</strong> {this.state.error?.message}
              </div>
              
              {this.state.error?.stack && (
                <div className="k-error-stack">
                  <strong>Stack Trace:</strong>
                  <pre>{this.state.error.stack}</pre>
                </div>
              )}
              
              {this.state.errorInfo?.componentStack && (
                <div className="k-error-component-stack">
                  <strong>Component Stack:</strong>
                  <pre>{this.state.errorInfo.componentStack}</pre>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
