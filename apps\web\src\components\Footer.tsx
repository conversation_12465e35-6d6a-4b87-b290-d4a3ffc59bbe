import React from 'react';
import { <PERSON> } from 'kilat-router';
import { 
  Github, 
  Twitter, 
  Discord, 
  Youtube, 
  Mail, 
  Heart,
  Zap,
  ExternalLink,
  Book,
  Code,
  Users,
  Star
} from 'lucide-react';

/**
 * 🦶 Footer Component
 */
export function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    framework: [
      { label: 'Getting Started', href: '/docs/getting-started' },
      { label: 'API Reference', href: '/docs/api' },
      { label: 'Examples', href: '/examples' },
      { label: 'Changelog', href: '/changelog' }
    ],
    community: [
      { label: 'Discord', href: 'https://discord.gg/kilatjs', external: true },
      { label: 'GitHub', href: 'https://github.com/kangpcode/kilat.js', external: true },
      { label: 'Twitter', href: 'https://twitter.com/kilatjs', external: true },
      { label: 'YouTube', href: 'https://youtube.com/@kilatjs', external: true }
    ],
    resources: [
      { label: 'Blog', href: '/blog' },
      { label: 'Tutorials', href: '/tutorials' },
      { label: 'Showcase', href: '/showcase' },
      { label: 'Templates', href: '/templates' }
    ],
    support: [
      { label: 'Documentation', href: '/docs' },
      { label: 'Help Center', href: '/help' },
      { label: 'Bug Reports', href: 'https://github.com/kangpcode/kilat.js/issues', external: true },
      { label: 'Feature Requests', href: 'https://github.com/kangpcode/kilat.js/discussions', external: true }
    ]
  };

  const socialLinks = [
    { icon: Github, href: 'https://github.com/kangpcode/kilat.js', label: 'GitHub' },
    { icon: Discord, href: 'https://discord.gg/kilatjs', label: 'Discord' },
    { icon: Twitter, href: 'https://twitter.com/kilatjs', label: 'Twitter' },
    { icon: Youtube, href: 'https://youtube.com/@kilatjs', label: 'YouTube' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' }
  ];

  return (
    <footer className="k-footer">
      <div className="k-footer-container">
        {/* Main Footer Content */}
        <div className="k-footer-main">
          {/* Brand Section */}
          <div className="k-footer-brand">
            <div className="k-footer-logo">
              <Zap className="k-footer-logo-icon" />
              <span className="k-footer-logo-text">Kilat.js</span>
            </div>
            
            <p className="k-footer-description">
              Framework fullstack masa depan dari Nusantara. 
              Cepat, modular, indah, dan tangguh.
            </p>
            
            <div className="k-footer-social">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="k-footer-social-link"
                    title={social.label}
                  >
                    <Icon size={20} />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Links Sections */}
          <div className="k-footer-links">
            <div className="k-footer-section">
              <h3 className="k-footer-section-title">
                <Book size={16} />
                Framework
              </h3>
              <ul className="k-footer-section-links">
                {footerLinks.framework.map((link) => (
                  <li key={link.label}>
                    <Link to={link.href} className="k-footer-link">
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="k-footer-section">
              <h3 className="k-footer-section-title">
                <Users size={16} />
                Community
              </h3>
              <ul className="k-footer-section-links">
                {footerLinks.community.map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="k-footer-link k-footer-link-external"
                    >
                      {link.label}
                      <ExternalLink size={12} />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div className="k-footer-section">
              <h3 className="k-footer-section-title">
                <Code size={16} />
                Resources
              </h3>
              <ul className="k-footer-section-links">
                {footerLinks.resources.map((link) => (
                  <li key={link.label}>
                    <Link to={link.href} className="k-footer-link">
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="k-footer-section">
              <h3 className="k-footer-section-title">
                <Star size={16} />
                Support
              </h3>
              <ul className="k-footer-section-links">
                {footerLinks.support.map((link) => (
                  <li key={link.label}>
                    {link.external ? (
                      <a
                        href={link.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="k-footer-link k-footer-link-external"
                      >
                        {link.label}
                        <ExternalLink size={12} />
                      </a>
                    ) : (
                      <Link to={link.href} className="k-footer-link">
                        {link.label}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="k-footer-bottom">
          <div className="k-footer-bottom-left">
            <p className="k-footer-copyright">
              © {currentYear} Kilat.js. All rights reserved.
            </p>
            <div className="k-footer-legal">
              <Link to="/privacy" className="k-footer-legal-link">
                Privacy Policy
              </Link>
              <Link to="/terms" className="k-footer-legal-link">
                Terms of Service
              </Link>
              <Link to="/license" className="k-footer-legal-link">
                MIT License
              </Link>
            </div>
          </div>

          <div className="k-footer-bottom-right">
            <p className="k-footer-made-with">
              Made with <Heart size={14} className="k-footer-heart" /> in Indonesia
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
