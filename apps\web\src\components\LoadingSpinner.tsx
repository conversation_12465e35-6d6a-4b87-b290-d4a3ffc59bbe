import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, Zap } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glow' | 'pulse' | 'dots';
  message?: string;
  fullscreen?: boolean;
}

/**
 * ⏳ Loading Spinner Component
 */
export function LoadingSpinner({ 
  size = 'md', 
  variant = 'default',
  message = 'Loading...',
  fullscreen = false 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'k-spinner-sm',
    md: 'k-spinner-md',
    lg: 'k-spinner-lg'
  };

  const containerClass = fullscreen 
    ? 'k-loading-fullscreen' 
    : 'k-loading-container';

  const renderSpinner = () => {
    switch (variant) {
      case 'glow':
        return (
          <motion.div
            className={`k-spinner-glow ${sizeClasses[size]}`}
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <Zap />
          </motion.div>
        );

      case 'pulse':
        return (
          <motion.div
            className={`k-spinner-pulse ${sizeClasses[size]}`}
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <Zap />
          </motion.div>
        );

      case 'dots':
        return (
          <div className={`k-spinner-dots ${sizeClasses[size]}`}>
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="k-spinner-dot"
                animate={{ y: [0, -10, 0] }}
                transition={{
                  duration: 0.6,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        );

      default:
        return (
          <motion.div
            className={`k-spinner-default ${sizeClasses[size]}`}
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <Loader2 />
          </motion.div>
        );
    }
  };

  return (
    <div className={containerClass}>
      <div className="k-loading-content">
        {renderSpinner()}
        {message && (
          <motion.p
            className="k-loading-message"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {message}
          </motion.p>
        )}
      </div>
    </div>
  );
}

/**
 * 🔄 Simple Spinner (for inline use)
 */
export function Spinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      style={{ display: 'inline-block' }}
    >
      <Loader2 size={sizeMap[size]} />
    </motion.div>
  );
}

export default LoadingSpinner;
