import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'kilat-router';
import { useTheme, usePlatform } from 'kilat-core';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Menu, 
  X, 
  Home, 
  Play, 
  Code, 
  Book, 
  Github, 
  Zap,
  Palette,
  Monitor,
  Smartphone,
  Sun,
  Moon,
  Settings
} from 'lucide-react';

/**
 * 🧭 Navigation Component
 */
export function Navigation() {
  const { theme, setTheme, mode, setMode, availableThemes } = useTheme();
  const { platform, isMobile } = usePlatform();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isThemeMenuOpen, setIsThemeMenuOpen] = useState(false);

  const navItems = [
    { path: '/', label: 'Home', icon: Home },
    { path: '/demo', label: 'Demo', icon: Play },
    { path: '/playground', label: 'Playground', icon: Code },
    { path: '/docs', label: 'Docs', icon: Book }
  ];

  const popularThemes = [
    'cyberpunk',
    'nusantara', 
    'minimalist',
    'retro',
    'aurora'
  ];

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setIsThemeMenuOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const toggleTheme = () => {
    setMode(mode === 'dark' ? 'light' : 'dark');
  };

  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme);
    setIsThemeMenuOpen(false);
  };

  return (
    <nav className="k-navigation">
      <div className="k-nav-container">
        {/* Logo */}
        <Link to="/" className="k-nav-logo">
          <Zap className="k-nav-logo-icon" />
          <span className="k-nav-logo-text">Kilat.js</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="k-nav-menu k-nav-menu-desktop">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`k-nav-item ${isActive ? 'active' : ''}`}
              >
                <Icon size={18} />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </div>

        {/* Actions */}
        <div className="k-nav-actions">
          {/* Theme Selector */}
          <div className="k-nav-theme-selector">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsThemeMenuOpen(!isThemeMenuOpen);
              }}
              className="k-nav-action-btn"
              title="Change Theme"
            >
              <Palette size={18} />
            </button>

            <AnimatePresence>
              {isThemeMenuOpen && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: -10 }}
                  className="k-theme-menu"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="k-theme-menu-header">
                    <Palette size={16} />
                    <span>Choose Theme</span>
                  </div>
                  
                  <div className="k-theme-grid">
                    {popularThemes.map((themeName) => (
                      <button
                        key={themeName}
                        onClick={() => handleThemeChange(themeName)}
                        className={`k-theme-option ${theme === themeName ? 'active' : ''}`}
                        data-theme={themeName}
                      >
                        <div className="k-theme-preview">
                          <div className="k-theme-color k-theme-primary"></div>
                          <div className="k-theme-color k-theme-accent"></div>
                          <div className="k-theme-color k-theme-background"></div>
                        </div>
                        <span className="k-theme-name">
                          {themeName.charAt(0).toUpperCase() + themeName.slice(1)}
                        </span>
                      </button>
                    ))}
                  </div>

                  <div className="k-theme-menu-footer">
                    <select
                      value={theme}
                      onChange={(e) => handleThemeChange(e.target.value)}
                      className="k-theme-select"
                    >
                      {availableThemes.map(themeName => (
                        <option key={themeName} value={themeName}>
                          {themeName.charAt(0).toUpperCase() + themeName.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Mode Toggle */}
          <button
            onClick={toggleTheme}
            className="k-nav-action-btn"
            title={`Switch to ${mode === 'dark' ? 'light' : 'dark'} mode`}
          >
            {mode === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
          </button>

          {/* Platform Indicator */}
          <div className="k-nav-platform" title={`Platform: ${platform}`}>
            {isMobile ? <Smartphone size={18} /> : <Monitor size={18} />}
          </div>

          {/* GitHub Link */}
          <a
            href="https://github.com/kangpcode/kilat.js"
            target="_blank"
            rel="noopener noreferrer"
            className="k-nav-action-btn"
            title="View on GitHub"
          >
            <Github size={18} />
          </a>

          {/* Mobile Menu Toggle */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="k-nav-mobile-toggle"
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="k-nav-menu k-nav-menu-mobile"
          >
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`k-nav-item ${isActive ? 'active' : ''}`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Icon size={18} />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}

export default Navigation;
