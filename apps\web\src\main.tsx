import React from 'react';
import ReactDOM from 'react-dom/client';
// import { KilatProvider } from 'kilat-core';
// import { KilatRouter } from 'kilat-router';
// import { AuthPlugin } from 'kilat-plugins';
// import { MonitoringPlugin } from 'kilat-plugins';
import App from './App';

// Import KilatCSS
// import 'kilatcss/kilat.css';
import './styles/global.css';

/**
 * 🚀 Kilat.js Web Demo Application
 * Showcasing the power of the Kilat.js framework
 */

// 🔧 Kilat.js Configuration
const kilatConfig = {
  // 🎨 Theme configuration
  theme: 'cyberpunk',
  mode: 'dark',
  
  // 🧭 Router configuration
  router: {
    basePath: '/',
    middleware: ['auth', 'analytics'],
    transitions: {
      type: 'fade',
      duration: 300
    }
  },
  
  // 🌌 Animation configuration
  animation: {
    presetScene: 'galaxy',
    autoRotate: true,
    background: 'transparent'
  },
  
  // 🔌 Plugin configuration
  plugins: {
    auth: {
      enabled: true,
      providers: ['local', 'google', 'github'],
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      multiFactorAuth: false
    },
    monitoring: {
      enabled: true,
      metrics: [
        { name: 'page_views', type: 'counter' },
        { name: 'user_interactions', type: 'counter' },
        { name: 'api_response_time', type: 'histogram' }
      ],
      alerts: [
        {
          name: 'high_error_rate',
          condition: 'error_rate > 0.05',
          severity: 'high',
          channels: ['console']
        }
      ]
    }
  },
  
  // 🛡️ Error handling
  errorHandling: {
    killSwitch: true,
    safeMode: true,
    crashReport: {
      enabled: true,
      includeLogs: true
    }
  }
};

// 🔌 Initialize plugins
// const authPlugin = new AuthPlugin(kilatConfig.plugins.auth);
// const monitoringPlugin = new MonitoringPlugin(kilatConfig.plugins.monitoring);

// 🚀 Initialize Kilat.js application
async function initializeApp() {
  try {
    // Get root element
    const rootElement = document.getElementById('root');
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // Create React root
    const root = ReactDOM.createRoot(rootElement);

    // Render application with Kilat.js providers
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );

    // 📊 Track application start
    // if (monitoringPlugin) {
    //   monitoringPlugin.recordMetric('app_starts', 1);
    // }

    console.log('⚡ Kilat.js Web Demo initialized successfully!');

  } catch (error) {
    console.error('❌ Failed to initialize Kilat.js application:', error);
    
    // Show error page
    document.body.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: #000011;
        color: #00ffff;
        font-family: 'Courier New', monospace;
        text-align: center;
      ">
        <h1 style="font-size: 3rem; margin-bottom: 1rem;">⚡ Kilat.js</h1>
        <h2 style="color: #ff0040; margin-bottom: 2rem;">Application Error</h2>
        <p style="max-width: 600px; line-height: 1.6;">
          Failed to initialize the application. Please check the console for more details.
        </p>
        <button 
          onclick="window.location.reload()" 
          style="
            margin-top: 2rem;
            padding: 12px 24px;
            background: #00ffff;
            color: #000011;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
          "
        >
          Reload Application
        </button>
      </div>
    `;
  }
}

// 🎯 Handle uncaught errors
window.addEventListener('error', (event) => {
  console.error('Uncaught error:', event.error);
  
  // Report to monitoring if available
  // if (monitoringPlugin) {
  //   monitoringPlugin.recordMetric('uncaught_errors', 1);
  // }
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  
  // Report to monitoring if available
  // if (monitoringPlugin) {
  //   monitoringPlugin.recordMetric('unhandled_rejections', 1);
  // }
});

// 🚀 Start the application
initializeApp();

// 🔥 Hot Module Replacement (HMR) support
if (import.meta.hot) {
  import.meta.hot.accept('./App', () => {
    console.log('🔥 Hot reloading App component...');
  });
}

// 🌐 Service Worker registration for PWA
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// 🎨 Apply theme on load
document.documentElement.setAttribute('data-kilat-theme', kilatConfig.theme);
document.documentElement.setAttribute('data-kilat-mode', kilatConfig.mode);

// 📱 Responsive design helper
function updateViewportClass() {
  const width = window.innerWidth;
  const classes = ['k-mobile', 'k-tablet', 'k-desktop'];
  
  // Remove existing classes
  document.documentElement.classList.remove(...classes);
  
  // Add appropriate class
  if (width < 768) {
    document.documentElement.classList.add('k-mobile');
  } else if (width < 1024) {
    document.documentElement.classList.add('k-tablet');
  } else {
    document.documentElement.classList.add('k-desktop');
  }
}

// Update on load and resize
updateViewportClass();
window.addEventListener('resize', updateViewportClass);

// 🎯 Performance monitoring
if (typeof window !== 'undefined' && 'performance' in window) {
  window.addEventListener('load', () => {
    // Measure page load time
    const loadTime = performance.now();
    console.log(`⚡ Page loaded in ${loadTime.toFixed(2)}ms`);
    
    // Report to monitoring
    if (monitoringPlugin) {
      monitoringPlugin.recordMetric('page_load_time', loadTime);
    }
  });
}

// 🎮 Keyboard shortcuts
document.addEventListener('keydown', (event) => {
  // Ctrl/Cmd + K for command palette
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault();
    console.log('⚡ Command palette shortcut triggered');
    // Implementation would show command palette
  }
  
  // Ctrl/Cmd + Shift + D for debug mode
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
    event.preventDefault();
    console.log('🐛 Debug mode toggled');
    document.documentElement.classList.toggle('k-debug');
  }
});

export default kilatConfig;
