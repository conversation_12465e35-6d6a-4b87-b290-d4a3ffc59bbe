import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import { 
  Zap, 
  Heart, 
  Users, 
  Globe, 
  Code, 
  Rocket,
  Star,
  Award,
  Target,
  Lightbulb,
  Shield,
  Cpu
} from 'lucide-react';

/**
 * ℹ️ About Page - Learn about Kilat.js
 */
export default function AboutPage() {
  const { theme } = useTheme();

  const stats = [
    { icon: Code, value: '50+', label: 'Components' },
    { icon: Zap, value: '15+', label: 'Themes' },
    { icon: Users, value: '1K+', label: 'Developers' },
    { icon: Star, value: '2.8K', label: 'GitHub Stars' }
  ];

  const features = [
    {
      icon: Rocket,
      title: 'Performance First',
      description: 'Built with Bun and optimized for speed. Lightning-fast development and runtime performance.'
    },
    {
      icon: Shield,
      title: 'Type Safe',
      description: 'Full TypeScript support with comprehensive type definitions for better developer experience.'
    },
    {
      icon: Cpu,
      title: 'Modern Architecture',
      description: 'Leverages the latest web technologies and best practices for scalable applications.'
    },
    {
      icon: Globe,
      title: 'Multi-Platform',
      description: 'Build for web, mobile, and desktop from a single codebase with platform-specific optimizations.'
    },
    {
      icon: Lightbulb,
      title: 'Developer Experience',
      description: 'Intuitive APIs, excellent documentation, and powerful CLI tools for productive development.'
    },
    {
      icon: Heart,
      title: 'Community Driven',
      description: 'Open source with an active community contributing themes, plugins, and improvements.'
    }
  ];

  const team = [
    {
      name: 'KangPCode',
      role: 'Creator & Lead Developer',
      avatar: '👨‍💻',
      bio: 'Passionate about creating developer tools that make building applications more enjoyable.',
      github: 'https://github.com/kangpcode'
    },
    {
      name: 'Community',
      role: 'Contributors',
      avatar: '👥',
      bio: 'Amazing developers from around the world contributing to make Kilat.js better.',
      github: 'https://github.com/kangpcode/kilat.js/contributors'
    }
  ];

  const timeline = [
    {
      year: '2024',
      title: 'Project Genesis',
      description: 'Kilat.js was born from the vision of creating a truly Indonesian framework that combines modern web technologies with beautiful design.'
    },
    {
      year: '2024 Q2',
      title: 'Core Development',
      description: 'Built the foundation with React, TypeScript, and Bun. Developed the core packages and plugin system.'
    },
    {
      year: '2024 Q3',
      title: 'UI Framework',
      description: 'Created KilatCSS with cyberpunk and nusantara themes. Added 3D animation engine KilatAnim.js.'
    },
    {
      year: '2024 Q4',
      title: 'Public Release',
      description: 'Released Kilat.js v1.0 with full documentation, examples, and community support.'
    }
  ];

  return (
    <div className="k-about-page">
      {/* Hero Section */}
      <section className="k-about-hero">
        <div className="k-container">
          <div className="k-about-hero-content">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="k-about-hero-text"
            >
              <h1 className="k-about-title">
                About <span className="k-glow">Kilat.js</span>
              </h1>
              
              <p className="k-about-subtitle">
                Framework fullstack masa depan dari Nusantara yang menggabungkan 
                kekuatan teknologi modern dengan keindahan desain Indonesia.
              </p>
              
              <div className="k-about-mission">
                <Target className="k-mission-icon" />
                <div>
                  <h3>Our Mission</h3>
                  <p>
                    Memberdayakan developer Indonesia untuk membangun aplikasi web 
                    yang cepat, indah, dan powerful dengan tools yang mudah digunakan.
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="k-about-hero-visual"
            >
              <KilatScene
                preset="nusantara"
                autoRotate={true}
                className="k-about-scene"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="k-about-stats">
        <div className="k-container">
          <div className="k-stats-grid">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="k-stat-card"
                >
                  <Icon className="k-stat-icon" />
                  <div className="k-stat-value">{stat.value}</div>
                  <div className="k-stat-label">{stat.label}</div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="k-about-features">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="k-section-header"
          >
            <h2 className="k-section-title">Why Choose Kilat.js?</h2>
            <p className="k-section-description">
              Built with modern technologies and Indonesian creativity
            </p>
          </motion.div>

          <div className="k-features-grid">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="k-feature-card"
                >
                  <Icon className="k-feature-icon" />
                  <h3 className="k-feature-title">{feature.title}</h3>
                  <p className="k-feature-description">{feature.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="k-about-timeline">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="k-section-header"
          >
            <h2 className="k-section-title">Our Journey</h2>
            <p className="k-section-description">
              From concept to reality
            </p>
          </motion.div>

          <div className="k-timeline">
            {timeline.map((item, index) => (
              <motion.div
                key={item.year}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="k-timeline-item"
              >
                <div className="k-timeline-marker">
                  <div className="k-timeline-dot" />
                </div>
                <div className="k-timeline-content">
                  <div className="k-timeline-year">{item.year}</div>
                  <h3 className="k-timeline-title">{item.title}</h3>
                  <p className="k-timeline-description">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="k-about-team">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="k-section-header"
          >
            <h2 className="k-section-title">Meet the Team</h2>
            <p className="k-section-description">
              The people behind Kilat.js
            </p>
          </motion.div>

          <div className="k-team-grid">
            {team.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="k-team-card"
              >
                <div className="k-team-avatar">{member.avatar}</div>
                <h3 className="k-team-name">{member.name}</h3>
                <div className="k-team-role">{member.role}</div>
                <p className="k-team-bio">{member.bio}</p>
                <a
                  href={member.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="k-team-link"
                >
                  View on GitHub
                </a>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="k-about-cta">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="k-cta-content"
          >
            <h2 className="k-cta-title">Ready to Build Something Amazing?</h2>
            <p className="k-cta-description">
              Join thousands of developers who are already building the future with Kilat.js
            </p>
            <div className="k-cta-actions">
              <a href="/docs" className="k-btn k-btn-primary k-btn-lg">
                <Rocket size={20} />
                Get Started
              </a>
              <a 
                href="https://github.com/kangpcode/kilat.js" 
                target="_blank"
                rel="noopener noreferrer"
                className="k-btn k-btn-outline k-btn-lg"
              >
                <Star size={20} />
                Star on GitHub
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
