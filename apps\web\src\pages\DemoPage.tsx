import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme, usePlatform } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import { 
  Palette, 
  Play, 
  Pause, 
  RotateCcw, 
  Settings, 
  Monitor,
  Smartphone,
  Tablet,
  Zap,
  Sparkles,
  Eye,
  Code,
  Download,
  Share2
} from 'lucide-react';

/**
 * 🎮 Demo Page - Interactive Kilat.js Showcase
 */
export default function DemoPage() {
  const { theme, setTheme, availableThemes, colors } = useTheme();
  const { platform, isMobile } = usePlatform();
  
  const [activeDemo, setActiveDemo] = useState('themes');
  const [isPlaying, setIsPlaying] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState(theme);
  const [animationPreset, setAnimationPreset] = useState('galaxy');
  const [glowIntensity, setGlowIntensity] = useState(1);
  const [particleCount, setParticleCount] = useState(100);
  
  const sceneRef = useRef<any>(null);

  const demos = [
    { id: 'themes', label: 'Themes', icon: Palette },
    { id: 'animations', label: 'Animations', icon: Sparkles },
    { id: 'components', label: 'Components', icon: Monitor },
    { id: 'responsive', label: 'Responsive', icon: Smartphone }
  ];

  const animationPresets = [
    { id: 'galaxy', name: 'Galaxy', description: 'Rotating galaxy with stars' },
    { id: 'matrix', name: 'Matrix', description: 'Digital rain effect' },
    { id: 'neonTunnel', name: 'Neon Tunnel', description: 'Cyberpunk tunnel' },
    { id: 'cyberwave', name: 'Cyberwave', description: 'Retro wave animation' },
    { id: 'particles', name: 'Particles', description: 'Floating particles' },
    { id: 'hologram', name: 'Hologram', description: 'Holographic effect' }
  ];

  const handleThemeChange = (newTheme: string) => {
    setSelectedTheme(newTheme);
    setTheme(newTheme);
  };

  const handleAnimationChange = (preset: string) => {
    setAnimationPreset(preset);
    if (sceneRef.current) {
      sceneRef.current.changePreset(preset);
    }
  };

  const toggleAnimation = () => {
    setIsPlaying(!isPlaying);
    if (sceneRef.current) {
      isPlaying ? sceneRef.current.pause() : sceneRef.current.play();
    }
  };

  const resetDemo = () => {
    setSelectedTheme('cyberpunk');
    setTheme('cyberpunk');
    setAnimationPreset('galaxy');
    setGlowIntensity(1);
    setParticleCount(100);
    setIsPlaying(true);
  };

  return (
    <div className="k-demo-page">
      {/* Demo Header */}
      <div className="k-demo-header">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="k-demo-title-section"
          >
            <h1 className="k-demo-title">
              <Zap className="k-demo-icon" />
              Interactive Demo
            </h1>
            <p className="k-demo-subtitle">
              Explore Kilat.js features in real-time
            </p>
          </motion.div>

          {/* Demo Navigation */}
          <div className="k-demo-nav">
            {demos.map((demo) => {
              const Icon = demo.icon;
              return (
                <button
                  key={demo.id}
                  onClick={() => setActiveDemo(demo.id)}
                  className={`k-demo-nav-btn ${activeDemo === demo.id ? 'active' : ''}`}
                >
                  <Icon size={20} />
                  <span>{demo.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Demo Content */}
      <div className="k-demo-content">
        <div className="k-container">
          <div className="k-demo-layout">
            {/* Demo Viewport */}
            <div className="k-demo-viewport">
              <div className="k-demo-viewport-header">
                <div className="k-demo-controls">
                  <button
                    onClick={toggleAnimation}
                    className="k-demo-control-btn"
                    title={isPlaying ? 'Pause' : 'Play'}
                  >
                    {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                  </button>
                  <button
                    onClick={resetDemo}
                    className="k-demo-control-btn"
                    title="Reset"
                  >
                    <RotateCcw size={16} />
                  </button>
                </div>
                <div className="k-demo-viewport-title">
                  Live Preview
                </div>
                <div className="k-demo-actions">
                  <button className="k-demo-action-btn">
                    <Code size={16} />
                  </button>
                  <button className="k-demo-action-btn">
                    <Download size={16} />
                  </button>
                  <button className="k-demo-action-btn">
                    <Share2 size={16} />
                  </button>
                </div>
              </div>

              <div className="k-demo-viewport-content">
                {/* Background Scene */}
                <div className="k-demo-scene">
                  <KilatScene
                    ref={sceneRef}
                    preset={animationPreset}
                    autoRotate={isPlaying}
                    particleCount={particleCount}
                    className="k-demo-scene-canvas"
                  />
                </div>

                {/* Demo Content Overlay */}
                <div className="k-demo-overlay">
                  <AnimatePresence mode="wait">
                    {activeDemo === 'themes' && (
                      <ThemesDemo
                        selectedTheme={selectedTheme}
                        onThemeChange={handleThemeChange}
                        glowIntensity={glowIntensity}
                      />
                    )}
                    {activeDemo === 'animations' && (
                      <AnimationsDemo
                        preset={animationPreset}
                        onPresetChange={handleAnimationChange}
                        isPlaying={isPlaying}
                        particleCount={particleCount}
                        onParticleCountChange={setParticleCount}
                      />
                    )}
                    {activeDemo === 'components' && (
                      <ComponentsDemo theme={selectedTheme} />
                    )}
                    {activeDemo === 'responsive' && (
                      <ResponsiveDemo />
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>

            {/* Demo Sidebar */}
            <div className="k-demo-sidebar">
              <div className="k-demo-panel">
                <h3 className="k-demo-panel-title">
                  <Settings size={20} />
                  Settings
                </h3>

                {activeDemo === 'themes' && (
                  <ThemeSettings
                    availableThemes={availableThemes}
                    selectedTheme={selectedTheme}
                    onThemeChange={handleThemeChange}
                    glowIntensity={glowIntensity}
                    onGlowIntensityChange={setGlowIntensity}
                  />
                )}

                {activeDemo === 'animations' && (
                  <AnimationSettings
                    presets={animationPresets}
                    selectedPreset={animationPreset}
                    onPresetChange={handleAnimationChange}
                    particleCount={particleCount}
                    onParticleCountChange={setParticleCount}
                    isPlaying={isPlaying}
                    onTogglePlay={toggleAnimation}
                  />
                )}

                {activeDemo === 'components' && (
                  <ComponentSettings theme={selectedTheme} />
                )}

                {activeDemo === 'responsive' && (
                  <ResponsiveSettings />
                )}
              </div>

              {/* Code Preview */}
              <div className="k-demo-panel">
                <h3 className="k-demo-panel-title">
                  <Code size={20} />
                  Code Preview
                </h3>
                <CodePreview
                  activeDemo={activeDemo}
                  theme={selectedTheme}
                  animationPreset={animationPreset}
                  glowIntensity={glowIntensity}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 🎨 Themes Demo Component
 */
interface ThemesDemoProps {
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  glowIntensity: number;
}

function ThemesDemo({ selectedTheme, onThemeChange, glowIntensity }: ThemesDemoProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="k-themes-demo"
    >
      <div className="k-demo-card">
        <h2 className="k-demo-card-title k-glow" style={{ '--glow-intensity': glowIntensity }}>
          Welcome to Kilat.js ⚡
        </h2>
        <p className="k-demo-card-text">
          Experience the power of dynamic theming with real-time glow effects
        </p>
        <div className="k-demo-card-actions">
          <button className="k-btn k-btn-primary k-btn-glow">
            Primary Button
          </button>
          <button className="k-btn k-btn-outline">
            Outline Button
          </button>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * 🌌 Animations Demo Component
 */
interface AnimationsDemoProps {
  preset: string;
  onPresetChange: (preset: string) => void;
  isPlaying: boolean;
  particleCount: number;
  onParticleCountChange: (count: number) => void;
}

function AnimationsDemo({ 
  preset, 
  onPresetChange, 
  isPlaying, 
  particleCount, 
  onParticleCountChange 
}: AnimationsDemoProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="k-animations-demo"
    >
      <div className="k-demo-card">
        <h2 className="k-demo-card-title">
          3D Animation Engine
        </h2>
        <p className="k-demo-card-text">
          Current preset: <strong>{preset}</strong>
        </p>
        <p className="k-demo-card-text">
          Particles: <strong>{particleCount}</strong>
        </p>
        <p className="k-demo-card-text">
          Status: <strong>{isPlaying ? 'Playing' : 'Paused'}</strong>
        </p>
      </div>
    </motion.div>
  );
}

/**
 * 🧩 Components Demo Component
 */
interface ComponentsDemoProps {
  theme: string;
}

function ComponentsDemo({ theme }: ComponentsDemoProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="k-components-demo"
    >
      <div className="k-demo-card">
        <h2 className="k-demo-card-title">
          UI Components
        </h2>
        <div className="k-component-showcase">
          <input 
            type="text" 
            placeholder="Input field"
            className="k-input"
          />
          <select className="k-select">
            <option>Select option</option>
            <option>Option 1</option>
            <option>Option 2</option>
          </select>
          <div className="k-checkbox-group">
            <label className="k-checkbox">
              <input type="checkbox" />
              <span>Checkbox option</span>
            </label>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * 📱 Responsive Demo Component
 */
function ResponsiveDemo() {
  const [viewportSize, setViewportSize] = useState('desktop');

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="k-responsive-demo"
    >
      <div className="k-demo-card">
        <h2 className="k-demo-card-title">
          Responsive Design
        </h2>
        <div className="k-viewport-controls">
          <button
            onClick={() => setViewportSize('mobile')}
            className={`k-viewport-btn ${viewportSize === 'mobile' ? 'active' : ''}`}
          >
            <Smartphone size={16} />
            Mobile
          </button>
          <button
            onClick={() => setViewportSize('tablet')}
            className={`k-viewport-btn ${viewportSize === 'tablet' ? 'active' : ''}`}
          >
            <Tablet size={16} />
            Tablet
          </button>
          <button
            onClick={() => setViewportSize('desktop')}
            className={`k-viewport-btn ${viewportSize === 'desktop' ? 'active' : ''}`}
          >
            <Monitor size={16} />
            Desktop
          </button>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * 🎨 Theme Settings Component
 */
interface ThemeSettingsProps {
  availableThemes: string[];
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  glowIntensity: number;
  onGlowIntensityChange: (intensity: number) => void;
}

function ThemeSettings({
  availableThemes,
  selectedTheme,
  onThemeChange,
  glowIntensity,
  onGlowIntensityChange
}: ThemeSettingsProps) {
  return (
    <div className="k-settings-group">
      <div className="k-setting">
        <label className="k-setting-label">Theme</label>
        <select
          value={selectedTheme}
          onChange={(e) => onThemeChange(e.target.value)}
          className="k-select k-select-sm"
        >
          {availableThemes.map(theme => (
            <option key={theme} value={theme}>
              {theme.charAt(0).toUpperCase() + theme.slice(1)}
            </option>
          ))}
        </select>
      </div>

      <div className="k-setting">
        <label className="k-setting-label">
          Glow Intensity: {glowIntensity.toFixed(1)}
        </label>
        <input
          type="range"
          min="0"
          max="3"
          step="0.1"
          value={glowIntensity}
          onChange={(e) => onGlowIntensityChange(parseFloat(e.target.value))}
          className="k-slider"
        />
      </div>
    </div>
  );
}

/**
 * 🌌 Animation Settings Component
 */
interface AnimationSettingsProps {
  presets: Array<{ id: string; name: string; description: string }>;
  selectedPreset: string;
  onPresetChange: (preset: string) => void;
  particleCount: number;
  onParticleCountChange: (count: number) => void;
  isPlaying: boolean;
  onTogglePlay: () => void;
}

function AnimationSettings({
  presets,
  selectedPreset,
  onPresetChange,
  particleCount,
  onParticleCountChange,
  isPlaying,
  onTogglePlay
}: AnimationSettingsProps) {
  return (
    <div className="k-settings-group">
      <div className="k-setting">
        <label className="k-setting-label">Animation Preset</label>
        <select
          value={selectedPreset}
          onChange={(e) => onPresetChange(e.target.value)}
          className="k-select k-select-sm"
        >
          {presets.map(preset => (
            <option key={preset.id} value={preset.id}>
              {preset.name}
            </option>
          ))}
        </select>
        <div className="k-setting-description">
          {presets.find(p => p.id === selectedPreset)?.description}
        </div>
      </div>

      <div className="k-setting">
        <label className="k-setting-label">
          Particle Count: {particleCount}
        </label>
        <input
          type="range"
          min="10"
          max="500"
          step="10"
          value={particleCount}
          onChange={(e) => onParticleCountChange(parseInt(e.target.value))}
          className="k-slider"
        />
      </div>

      <div className="k-setting">
        <button
          onClick={onTogglePlay}
          className={`k-btn k-btn-sm ${isPlaying ? 'k-btn-primary' : 'k-btn-outline'}`}
        >
          {isPlaying ? <Pause size={16} /> : <Play size={16} />}
          {isPlaying ? 'Pause' : 'Play'}
        </button>
      </div>
    </div>
  );
}

/**
 * 🧩 Component Settings Component
 */
interface ComponentSettingsProps {
  theme: string;
}

function ComponentSettings({ theme }: ComponentSettingsProps) {
  return (
    <div className="k-settings-group">
      <div className="k-setting">
        <label className="k-setting-label">Current Theme</label>
        <div className="k-setting-value">{theme}</div>
      </div>

      <div className="k-setting">
        <label className="k-setting-label">Component Library</label>
        <div className="k-setting-description">
          Explore interactive UI components with live theming
        </div>
      </div>
    </div>
  );
}

/**
 * 📱 Responsive Settings Component
 */
function ResponsiveSettings() {
  return (
    <div className="k-settings-group">
      <div className="k-setting">
        <label className="k-setting-label">Breakpoints</label>
        <div className="k-setting-description">
          Mobile: 0-768px<br />
          Tablet: 768-1024px<br />
          Desktop: 1024px+
        </div>
      </div>
    </div>
  );
}

/**
 * 💻 Code Preview Component
 */
interface CodePreviewProps {
  activeDemo: string;
  theme: string;
  animationPreset: string;
  glowIntensity: number;
}

function CodePreview({ activeDemo, theme, animationPreset, glowIntensity }: CodePreviewProps) {
  const getCodeExample = () => {
    switch (activeDemo) {
      case 'themes':
        return `import { useTheme } from 'kilat-core';

function MyComponent() {
  const { setTheme } = useTheme();

  return (
    <div className="k-card">
      <h1 className="k-glow"
          style={{ '--glow-intensity': ${glowIntensity} }}>
        Hello Kilat.js!
      </h1>
      <button onClick={() => setTheme('${theme}')}>
        Switch to ${theme}
      </button>
    </div>
  );
}`;

      case 'animations':
        return `import { KilatScene } from 'kilatanim.js';

function AnimatedBackground() {
  return (
    <KilatScene
      preset="${animationPreset}"
      autoRotate={true}
      particleCount={${particleCount}}
      className="k-background"
    />
  );
}`;

      case 'components':
        return `import 'kilatcss/kilat.css';

function UIShowcase() {
  return (
    <div className="k-space-y-4">
      <button className="k-btn k-btn-primary k-btn-glow">
        Primary Button
      </button>
      <input
        type="text"
        placeholder="Input field"
        className="k-input"
      />
      <select className="k-select">
        <option>Select option</option>
      </select>
    </div>
  );
}`;

      case 'responsive':
        return `// Responsive utilities
<div className="k-grid k-grid-cols-1 md:k-grid-cols-2 lg:k-grid-cols-3">
  <div className="k-col-span-1">Mobile: 1 column</div>
  <div className="k-col-span-1">Tablet: 2 columns</div>
  <div className="k-col-span-1">Desktop: 3 columns</div>
</div>

// Responsive text
<h1 className="k-text-2xl md:k-text-4xl lg:k-text-6xl">
  Responsive heading
</h1>`;

      default:
        return '// Select a demo to see code example';
    }
  };

  return (
    <div className="k-code-preview">
      <pre className="k-code">
        <code>{getCodeExample()}</code>
      </pre>
    </div>
  );
}
