import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Book, 
  Search, 
  ChevronRight, 
  ExternalLink,
  Download,
  Play,
  Code,
  Palette,
  Database,
  Plug,
  Terminal,
  Smartphone,
  Monitor,
  Globe
} from 'lucide-react';

/**
 * 📚 Documentation Page
 */
export default function DocsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('getting-started');

  const docCategories = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: Play,
      description: 'Quick start guide and installation',
      docs: [
        { title: 'Installation', href: '/docs/installation' },
        { title: 'Your First App', href: '/docs/first-app' },
        { title: 'Project Structure', href: '/docs/project-structure' },
        { title: 'Configuration', href: '/docs/configuration' }
      ]
    },
    {
      id: 'core',
      title: 'Core Concepts',
      icon: Code,
      description: 'Framework fundamentals',
      docs: [
        { title: 'Components', href: '/docs/components' },
        { title: 'Hooks', href: '/docs/hooks' },
        { title: 'Context', href: '/docs/context' },
        { title: 'Error Handling', href: '/docs/error-handling' }
      ]
    },
    {
      id: 'theming',
      title: 'Theming & UI',
      icon: Palette,
      description: 'Styling and themes',
      docs: [
        { title: 'Theme System', href: '/docs/theme-system' },
        { title: 'KilatCSS', href: '/docs/kilatcss' },
        { title: 'Custom Themes', href: '/docs/custom-themes' },
        { title: 'Animations', href: '/docs/animations' }
      ]
    },
    {
      id: 'routing',
      title: 'Routing',
      icon: Globe,
      description: 'Navigation and routing',
      docs: [
        { title: 'File-based Routing', href: '/docs/routing' },
        { title: 'Dynamic Routes', href: '/docs/dynamic-routes' },
        { title: 'Middleware', href: '/docs/middleware' },
        { title: 'Navigation', href: '/docs/navigation' }
      ]
    },
    {
      id: 'database',
      title: 'Database & ORM',
      icon: Database,
      description: 'Data management',
      docs: [
        { title: 'Models', href: '/docs/models' },
        { title: 'Migrations', href: '/docs/migrations' },
        { title: 'Relationships', href: '/docs/relationships' },
        { title: 'Query Builder', href: '/docs/query-builder' }
      ]
    },
    {
      id: 'plugins',
      title: 'Plugins',
      icon: Plug,
      description: 'Extend functionality',
      docs: [
        { title: 'Using Plugins', href: '/docs/using-plugins' },
        { title: 'Creating Plugins', href: '/docs/creating-plugins' },
        { title: 'Official Plugins', href: '/docs/official-plugins' },
        { title: 'Plugin API', href: '/docs/plugin-api' }
      ]
    },
    {
      id: 'cli',
      title: 'CLI Tools',
      icon: Terminal,
      description: 'Command line interface',
      docs: [
        { title: 'CLI Overview', href: '/docs/cli' },
        { title: 'Project Commands', href: '/docs/cli-project' },
        { title: 'Database Commands', href: '/docs/cli-database' },
        { title: 'Plugin Commands', href: '/docs/cli-plugins' }
      ]
    },
    {
      id: 'deployment',
      title: 'Deployment',
      icon: Monitor,
      description: 'Production deployment',
      docs: [
        { title: 'Build Process', href: '/docs/build' },
        { title: 'Vercel Deployment', href: '/docs/deploy-vercel' },
        { title: 'Docker Deployment', href: '/docs/deploy-docker' },
        { title: 'Performance', href: '/docs/performance' }
      ]
    }
  ];

  const quickLinks = [
    { title: 'API Reference', href: '/docs/api', icon: Code },
    { title: 'Examples', href: '/examples', icon: Play },
    { title: 'GitHub', href: 'https://github.com/kangpcode/kilat.js', icon: ExternalLink },
    { title: 'Discord', href: 'https://discord.gg/kilatjs', icon: ExternalLink }
  ];

  const filteredCategories = docCategories.filter(category =>
    category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.docs.some(doc => 
      doc.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <div className="k-docs-page">
      {/* Header */}
      <div className="k-docs-header">
        <div className="k-container">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="k-docs-title-section"
          >
            <h1 className="k-docs-title">
              <Book className="k-docs-icon" />
              Documentation
            </h1>
            <p className="k-docs-subtitle">
              Everything you need to build amazing applications with Kilat.js
            </p>
          </motion.div>

          {/* Search */}
          <div className="k-docs-search">
            <div className="k-search-input-wrapper">
              <Search className="k-search-icon" />
              <input
                type="text"
                placeholder="Search documentation..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="k-search-input"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="k-docs-content">
        <div className="k-container">
          <div className="k-docs-layout">
            {/* Sidebar */}
            <aside className="k-docs-sidebar">
              <div className="k-docs-quick-links">
                <h3 className="k-sidebar-title">Quick Links</h3>
                <div className="k-quick-links-grid">
                  {quickLinks.map((link) => {
                    const Icon = link.icon;
                    return (
                      <a
                        key={link.title}
                        href={link.href}
                        target={link.href.startsWith('http') ? '_blank' : undefined}
                        rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                        className="k-quick-link"
                      >
                        <Icon size={16} />
                        {link.title}
                      </a>
                    );
                  })}
                </div>
              </div>

              <div className="k-docs-nav">
                <h3 className="k-sidebar-title">Categories</h3>
                <nav className="k-docs-nav-list">
                  {docCategories.map((category) => {
                    const Icon = category.icon;
                    return (
                      <button
                        key={category.id}
                        onClick={() => setActiveCategory(category.id)}
                        className={`k-docs-nav-item ${activeCategory === category.id ? 'active' : ''}`}
                      >
                        <Icon size={18} />
                        <span>{category.title}</span>
                        <ChevronRight size={14} className="k-nav-arrow" />
                      </button>
                    );
                  })}
                </nav>
              </div>
            </aside>

            {/* Main Content */}
            <main className="k-docs-main">
              {searchQuery ? (
                // Search Results
                <div className="k-docs-search-results">
                  <h2 className="k-search-results-title">
                    Search Results for "{searchQuery}"
                  </h2>
                  
                  {filteredCategories.length > 0 ? (
                    <div className="k-search-results-grid">
                      {filteredCategories.map((category) => {
                        const Icon = category.icon;
                        return (
                          <div key={category.id} className="k-search-result-category">
                            <div className="k-search-result-header">
                              <Icon size={20} />
                              <h3>{category.title}</h3>
                            </div>
                            <p className="k-search-result-description">
                              {category.description}
                            </p>
                            <div className="k-search-result-docs">
                              {category.docs
                                .filter(doc => 
                                  doc.title.toLowerCase().includes(searchQuery.toLowerCase())
                                )
                                .map((doc) => (
                                  <a
                                    key={doc.href}
                                    href={doc.href}
                                    className="k-search-result-doc"
                                  >
                                    {doc.title}
                                  </a>
                                ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="k-no-results">
                      <p>No results found for "{searchQuery}"</p>
                      <button
                        onClick={() => setSearchQuery('')}
                        className="k-btn k-btn-outline k-btn-sm"
                      >
                        Clear Search
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                // Category Content
                <div className="k-docs-category-content">
                  {(() => {
                    const category = docCategories.find(cat => cat.id === activeCategory);
                    if (!category) return null;

                    const Icon = category.icon;
                    return (
                      <motion.div
                        key={activeCategory}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="k-category-header">
                          <Icon size={32} className="k-category-icon" />
                          <div>
                            <h2 className="k-category-title">{category.title}</h2>
                            <p className="k-category-description">{category.description}</p>
                          </div>
                        </div>

                        <div className="k-docs-grid">
                          {category.docs.map((doc, index) => (
                            <motion.a
                              key={doc.href}
                              href={doc.href}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                              className="k-doc-card"
                            >
                              <h3 className="k-doc-title">{doc.title}</h3>
                              <ChevronRight size={16} className="k-doc-arrow" />
                            </motion.a>
                          ))}
                        </div>
                      </motion.div>
                    );
                  })()}
                </div>
              )}
            </main>
          </div>
        </div>
      </div>
    </div>
  );
}
