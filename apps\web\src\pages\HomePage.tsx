import React, { useEffect, useState } from 'react';
import { Link } from 'kilat-router';
import { useKilat, useTheme } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';

/**
 * 🏠 Home Page - Kilat.js Framework Showcase
 */
export default function HomePage() {
  const { config } = useKilat();
  const { theme } = useTheme();
  const [stats, setStats] = useState({
    components: 50,
    themes: 15,
    plugins: 25,
    downloads: 1337
  });

  useEffect(() => {
    // Animate stats on load
    const animateStats = () => {
      const targets = { components: 50, themes: 15, plugins: 25, downloads: 1337 };
      const duration = 2000;
      const steps = 60;
      const stepDuration = duration / steps;
      
      let step = 0;
      const interval = setInterval(() => {
        step++;
        const progress = step / steps;
        
        setStats({
          components: Math.floor(targets.components * progress),
          themes: Math.floor(targets.themes * progress),
          plugins: Math.floor(targets.plugins * progress),
          downloads: Math.floor(targets.downloads * progress)
        });
        
        if (step >= steps) {
          clearInterval(interval);
          setStats(targets);
        }
      }, stepDuration);
      
      return () => clearInterval(interval);
    };

    const cleanup = animateStats();
    return cleanup;
  }, []);

  return (
    <div className="k-space-y-16">
      {/* 🚀 Hero Section */}
      <section className="k-text-center k-py-20">
        <div className="k-relative">
          {/* Hero Animation */}
          <div className="k-absolute k-inset-0 k-flex k-items-center k-justify-center k-opacity-20">
            <KilatScene
              preset="galaxy"
              autoRotate={true}
              className="k-w-96 k-h-96"
            />
          </div>
          
          {/* Hero Content */}
          <div className="k-relative k-z-10">
            <h1 className="k-text-6xl k-font-bold k-mb-6 k-text-glow-lg">
              <span className="k-text-primary">Kilat.js</span> ⚡
            </h1>
            
            <p className="k-text-2xl k-text-muted k-mb-8 k-max-w-3xl k-mx-auto">
              Framework fullstack masa depan dari Nusantara. 
              <br />
              <span className="k-text-accent">Cepat, modular, indah, dan tangguh.</span>
            </p>
            
            <div className="k-flex k-gap-4 k-justify-center k-flex-wrap">
              <Link
                to="/demo"
                className="k-btn k-btn-primary k-btn-lg k-animate-glow"
              >
                🚀 Lihat Demo
              </Link>
              
              <Link
                to="/docs"
                className="k-btn k-btn-outline k-btn-lg"
              >
                📚 Dokumentasi
              </Link>
              
              <a
                href="https://github.com/kangpcode/kilat.js"
                target="_blank"
                rel="noopener noreferrer"
                className="k-btn k-btn-ghost k-btn-lg"
              >
                ⭐ GitHub
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* 📊 Stats Section */}
      <section className="k-py-16">
        <div className="k-grid k-grid-cols-2 md:k-grid-cols-4 k-gap-8">
          <StatCard
            icon="🧩"
            value={stats.components}
            label="Komponen"
            suffix="+"
          />
          <StatCard
            icon="🎨"
            value={stats.themes}
            label="Tema UI"
            suffix="+"
          />
          <StatCard
            icon="🔌"
            value={stats.plugins}
            label="Plugin"
            suffix="+"
          />
          <StatCard
            icon="⬇️"
            value={stats.downloads}
            label="Download"
            suffix="+"
          />
        </div>
      </section>

      {/* ✨ Features Section */}
      <section className="k-py-16">
        <h2 className="k-text-4xl k-font-bold k-text-center k-mb-12">
          Fitur Unggulan ⚡
        </h2>
        
        <div className="k-grid md:k-grid-cols-2 lg:k-grid-cols-3 k-gap-8">
          <FeatureCard
            icon="🎨"
            title="UI Glow Futuristik"
            description="KilatCSS dengan efek neon dan tema cyberpunk yang memukau"
            theme="cyberpunk"
          />
          
          <FeatureCard
            icon="🌌"
            title="Animasi 3D Modular"
            description="KilatAnim.js dengan preset galaxy, matrix, dan neon tunnel"
            theme="galaxy"
          />
          
          <FeatureCard
            icon="🏝️"
            title="Tema Nusantara"
            description="Desain yang terinspirasi budaya Indonesia dengan sentuhan modern"
            theme="nusantara"
          />
          
          <FeatureCard
            icon="⚙️"
            title="Backend Internal"
            description="Server built-in dengan ORM adaptif SQLite/MySQL"
            theme="backend"
          />
          
          <FeatureCard
            icon="🔌"
            title="Sistem Plugin"
            description="Ekosistem plugin untuk auth, CMS, payments, dan AI"
            theme="plugins"
          />
          
          <FeatureCard
            icon="🛡️"
            title="Error Recovery"
            description="Crash recovery otomatis dengan monitoring real-time"
            theme="monitoring"
          />
        </div>
      </section>

      {/* 🚀 Quick Start Section */}
      <section className="k-py-16 k-bg-surface k-rounded-2xl k-p-8">
        <h2 className="k-text-3xl k-font-bold k-text-center k-mb-8">
          Mulai dalam 30 Detik 🚀
        </h2>
        
        <div className="k-max-w-2xl k-mx-auto">
          <div className="k-bg-background k-rounded-lg k-p-6 k-font-mono k-text-sm">
            <div className="k-text-muted k-mb-2"># Install Kilat.js</div>
            <div className="k-text-primary">bun create kilat-app my-app</div>
            
            <div className="k-text-muted k-mb-2 k-mt-4"># Start development</div>
            <div className="k-text-primary">cd my-app && bun dev</div>
            
            <div className="k-text-muted k-mb-2 k-mt-4"># Deploy to production</div>
            <div className="k-text-primary">bun run build && kilat deploy</div>
          </div>
          
          <div className="k-text-center k-mt-6">
            <Link
              to="/playground"
              className="k-btn k-btn-accent k-btn-lg"
            >
              🎮 Coba di Playground
            </Link>
          </div>
        </div>
      </section>

      {/* 🌟 Testimonials Section */}
      <section className="k-py-16">
        <h2 className="k-text-3xl k-font-bold k-text-center k-mb-12">
          Kata Mereka 💬
        </h2>
        
        <div className="k-grid md:k-grid-cols-3 k-gap-8">
          <TestimonialCard
            name="Budi Santoso"
            role="Full Stack Developer"
            avatar="👨‍💻"
            quote="Kilat.js mengubah cara saya membangun aplikasi. Framework yang benar-benar dari masa depan!"
          />
          
          <TestimonialCard
            name="Sari Dewi"
            role="UI/UX Designer"
            avatar="👩‍🎨"
            quote="Tema Nusantara dan efek glow-nya luar biasa! Sangat mudah membuat UI yang memukau."
          />
          
          <TestimonialCard
            name="Ahmad Rizki"
            role="Startup Founder"
            avatar="👨‍💼"
            quote="Dengan Kilat.js, kami bisa launch MVP dalam seminggu. Plugin system-nya sangat powerful!"
          />
        </div>
      </section>
    </div>
  );
}

/**
 * 📊 Stat Card Component
 */
interface StatCardProps {
  icon: string;
  value: number;
  label: string;
  suffix?: string;
}

function StatCard({ icon, value, label, suffix = '' }: StatCardProps) {
  return (
    <div className="k-text-center k-p-6 k-bg-surface k-rounded-lg k-border k-border-border hover:k-border-primary k-transition-colors">
      <div className="k-text-3xl k-mb-2">{icon}</div>
      <div className="k-text-3xl k-font-bold k-text-primary k-mb-1">
        {value.toLocaleString()}{suffix}
      </div>
      <div className="k-text-muted">{label}</div>
    </div>
  );
}

/**
 * ✨ Feature Card Component
 */
interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
  theme: string;
}

function FeatureCard({ icon, title, description, theme }: FeatureCardProps) {
  return (
    <div className="k-p-6 k-bg-surface k-rounded-lg k-border k-border-border hover:k-border-primary k-transition-all k-duration-300 hover:k-transform hover:k-scale-105">
      <div className="k-text-4xl k-mb-4">{icon}</div>
      <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-primary">
        {title}
      </h3>
      <p className="k-text-muted k-leading-relaxed">
        {description}
      </p>
    </div>
  );
}

/**
 * 💬 Testimonial Card Component
 */
interface TestimonialCardProps {
  name: string;
  role: string;
  avatar: string;
  quote: string;
}

function TestimonialCard({ name, role, avatar, quote }: TestimonialCardProps) {
  return (
    <div className="k-p-6 k-bg-surface k-rounded-lg k-border k-border-border">
      <div className="k-flex k-items-center k-mb-4">
        <div className="k-text-3xl k-mr-3">{avatar}</div>
        <div>
          <div className="k-font-semibold k-text-primary">{name}</div>
          <div className="k-text-sm k-text-muted">{role}</div>
        </div>
      </div>
      <blockquote className="k-text-muted k-italic">
        "{quote}"
      </blockquote>
    </div>
  );
}
