import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'kilat-router';
import { KilatScene } from 'kilatanim.js';
import { 
  Home, 
  Search, 
  ArrowLeft, 
  Zap,
  AlertTriangle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

/**
 * 🔍 404 Not Found Page
 */
export default function NotFoundPage() {
  const [countdown, setCountdown] = useState(10);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Auto redirect countdown
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setIsRedirecting(true);
          window.location.href = '/';
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const suggestions = [
    { title: 'Home', href: '/', icon: Home },
    { title: 'Documentation', href: '/docs', icon: Search },
    { title: 'Demo', href: '/demo', icon: Zap },
    { title: 'Playground', href: '/playground', icon: ExternalLink }
  ];

  const handleStopRedirect = () => {
    setCountdown(0);
    setIsRedirecting(false);
  };

  return (
    <div className="k-404-page">
      {/* Background Animation */}
      <div className="k-404-background">
        <KilatScene
          preset="matrix"
          autoRotate={true}
          className="k-404-scene"
        />
        <div className="k-404-overlay" />
      </div>

      {/* Content */}
      <div className="k-404-content">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="k-404-container"
        >
          {/* 404 Icon */}
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="k-404-icon-container"
          >
            <AlertTriangle className="k-404-icon" />
            <div className="k-404-number">404</div>
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="k-404-title"
          >
            Page Not Found
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="k-404-description"
          >
            Oops! The page you're looking for doesn't exist. 
            It might have been moved, deleted, or you entered the wrong URL.
          </motion.p>

          {/* Auto Redirect Notice */}
          {countdown > 0 && !isRedirecting && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="k-404-redirect-notice"
            >
              <RefreshCw className="k-redirect-icon" />
              <span>
                Redirecting to home page in <strong>{countdown}</strong> seconds
              </span>
              <button
                onClick={handleStopRedirect}
                className="k-stop-redirect-btn"
              >
                Cancel
              </button>
            </motion.div>
          )}

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="k-404-actions"
          >
            <Link to="/" className="k-btn k-btn-primary k-btn-lg">
              <Home size={20} />
              Go Home
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="k-btn k-btn-outline k-btn-lg"
            >
              <ArrowLeft size={20} />
              Go Back
            </button>
          </motion.div>

          {/* Suggestions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="k-404-suggestions"
          >
            <h3 className="k-suggestions-title">
              Maybe you're looking for:
            </h3>
            
            <div className="k-suggestions-grid">
              {suggestions.map((suggestion, index) => {
                const Icon = suggestion.icon;
                return (
                  <motion.div
                    key={suggestion.href}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: 1.4 + index * 0.1 }}
                  >
                    <Link
                      to={suggestion.href}
                      className="k-suggestion-card"
                    >
                      <Icon size={24} className="k-suggestion-icon" />
                      <span className="k-suggestion-title">
                        {suggestion.title}
                      </span>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.6 }}
            className="k-404-help"
          >
            <p>
              If you think this is a mistake, please{' '}
              <a
                href="https://github.com/kangpcode/kilat.js/issues"
                target="_blank"
                rel="noopener noreferrer"
                className="k-404-link"
              >
                report it on GitHub
              </a>
              {' '}or{' '}
              <a
                href="mailto:<EMAIL>"
                className="k-404-link"
              >
                contact us
              </a>
              .
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
