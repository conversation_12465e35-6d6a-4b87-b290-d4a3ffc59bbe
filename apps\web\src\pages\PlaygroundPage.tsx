import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import { 
  Play, 
  Save, 
  Download, 
  Share2, 
  RotateCcw, 
  Eye,
  Code,
  Settings,
  Palette,
  Layers,
  Zap,
  Terminal,
  FileText,
  Copy,
  Check
} from 'lucide-react';

/**
 * 🎮 Playground Page - Interactive Code Editor
 */
export default function PlaygroundPage() {
  const { theme, setTheme, availableThemes } = useTheme();
  const [activeTab, setActiveTab] = useState('jsx');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [code, setCode] = useState({
    jsx: defaultJSXCode,
    css: defaultCSSCode,
    config: defaultConfigCode
  });
  const [copied, setCopied] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  
  const previewRef = useRef<HTMLIFrameElement>(null);
  const editorRef = useRef<HTMLTextAreaElement>(null);

  const tabs = [
    { id: 'jsx', label: 'Component.tsx', icon: Code },
    { id: 'css', label: 'styles.css', icon: Palette },
    { id: 'config', label: 'kilat.config.ts', icon: Settings }
  ];

  const runCode = async () => {
    setIsRunning(true);
    setErrors([]);
    
    try {
      // Simulate code execution
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update preview iframe
      if (previewRef.current) {
        const html = generatePreviewHTML();
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        previewRef.current.src = url;
      }
      
      setOutput('✅ Code executed successfully!');
    } catch (error) {
      setErrors([`❌ Error: ${(error as Error).message}`]);
    } finally {
      setIsRunning(false);
    }
  };

  const saveCode = () => {
    const codeData = {
      jsx: code.jsx,
      css: code.css,
      config: code.config,
      theme,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('kilat-playground-code', JSON.stringify(codeData));
    setOutput('💾 Code saved to local storage');
  };

  const loadCode = () => {
    const saved = localStorage.getItem('kilat-playground-code');
    if (saved) {
      const codeData = JSON.parse(saved);
      setCode({
        jsx: codeData.jsx || defaultJSXCode,
        css: codeData.css || defaultCSSCode,
        config: codeData.config || defaultConfigCode
      });
      if (codeData.theme) {
        setTheme(codeData.theme);
      }
      setOutput('📂 Code loaded from local storage');
    }
  };

  const resetCode = () => {
    setCode({
      jsx: defaultJSXCode,
      css: defaultCSSCode,
      config: defaultConfigCode
    });
    setOutput('🔄 Code reset to default');
  };

  const copyCode = async () => {
    const fullCode = `// Component.tsx\n${code.jsx}\n\n// styles.css\n${code.css}\n\n// kilat.config.ts\n${code.config}`;
    
    try {
      await navigator.clipboard.writeText(fullCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      setOutput('📋 Code copied to clipboard');
    } catch (error) {
      setOutput('❌ Failed to copy code');
    }
  };

  const downloadCode = () => {
    const files = [
      { name: 'Component.tsx', content: code.jsx },
      { name: 'styles.css', content: code.css },
      { name: 'kilat.config.ts', content: code.config }
    ];
    
    files.forEach(file => {
      const blob = new Blob([file.content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      a.click();
      URL.revokeObjectURL(url);
    });
    
    setOutput('⬇️ Files downloaded');
  };

  const generatePreviewHTML = () => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kilat.js Preview</title>
  <link href="https://cdn.jsdelivr.net/npm/kilatcss@latest/dist/kilat.css" rel="stylesheet">
  <style>
    ${code.css}
    body { 
      margin: 0; 
      padding: 20px; 
      background: var(--k-background);
      color: var(--k-text);
      font-family: var(--k-font-sans);
    }
  </style>
</head>
<body>
  <div id="root"></div>
  
  <script type="module">
    // Simulate React component rendering
    const root = document.getElementById('root');
    
    // Parse JSX-like syntax (simplified)
    const componentHTML = \`${code.jsx.replace(/className=/g, 'class=').replace(/\{[^}]*\}/g, '')}\`;
    
    root.innerHTML = componentHTML;
    
    // Apply theme
    document.documentElement.setAttribute('data-theme', '${theme}');
  </script>
</body>
</html>
    `;
  };

  useEffect(() => {
    // Auto-run code when it changes
    const timer = setTimeout(() => {
      if (code.jsx || code.css) {
        runCode();
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [code, theme]);

  useEffect(() => {
    // Load saved code on mount
    loadCode();
  }, []);

  return (
    <div className="k-playground-page">
      {/* Header */}
      <div className="k-playground-header">
        <div className="k-container">
          <div className="k-playground-title-section">
            <h1 className="k-playground-title">
              <Terminal className="k-playground-icon" />
              Kilat.js Playground
            </h1>
            <p className="k-playground-subtitle">
              Experiment with Kilat.js components in real-time
            </p>
          </div>

          <div className="k-playground-toolbar">
            <button
              onClick={runCode}
              disabled={isRunning}
              className="k-btn k-btn-primary k-btn-sm"
            >
              <Play size={16} />
              {isRunning ? 'Running...' : 'Run'}
            </button>
            
            <button
              onClick={saveCode}
              className="k-btn k-btn-outline k-btn-sm"
            >
              <Save size={16} />
              Save
            </button>
            
            <button
              onClick={copyCode}
              className="k-btn k-btn-ghost k-btn-sm"
            >
              {copied ? <Check size={16} /> : <Copy size={16} />}
              {copied ? 'Copied!' : 'Copy'}
            </button>
            
            <button
              onClick={downloadCode}
              className="k-btn k-btn-ghost k-btn-sm"
            >
              <Download size={16} />
              Download
            </button>
            
            <button
              onClick={resetCode}
              className="k-btn k-btn-ghost k-btn-sm"
            >
              <RotateCcw size={16} />
              Reset
            </button>

            <div className="k-playground-divider" />

            <select
              value={theme}
              onChange={(e) => setTheme(e.target.value)}
              className="k-select k-select-sm"
            >
              {availableThemes.map(themeName => (
                <option key={themeName} value={themeName}>
                  {themeName.charAt(0).toUpperCase() + themeName.slice(1)}
                </option>
              ))}
            </select>

            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className={`k-btn k-btn-sm ${isPreviewMode ? 'k-btn-primary' : 'k-btn-outline'}`}
            >
              <Eye size={16} />
              {isPreviewMode ? 'Code' : 'Preview'}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="k-playground-content">
        <div className="k-container">
          <div className="k-playground-layout">
            {/* Code Editor */}
            <div className={`k-playground-editor ${isPreviewMode ? 'hidden' : ''}`}>
              {/* Editor Tabs */}
              <div className="k-editor-tabs">
                {tabs.map(tab => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`k-editor-tab ${activeTab === tab.id ? 'active' : ''}`}
                    >
                      <Icon size={16} />
                      {tab.label}
                    </button>
                  );
                })}
              </div>

              {/* Editor Content */}
              <div className="k-editor-content">
                <textarea
                  ref={editorRef}
                  value={code[activeTab as keyof typeof code]}
                  onChange={(e) => setCode(prev => ({
                    ...prev,
                    [activeTab]: e.target.value
                  }))}
                  className="k-editor-textarea"
                  placeholder={`Enter your ${activeTab.toUpperCase()} code here...`}
                  spellCheck={false}
                />
              </div>
            </div>

            {/* Preview */}
            <div className={`k-playground-preview ${isPreviewMode ? 'fullscreen' : ''}`}>
              <div className="k-preview-header">
                <div className="k-preview-title">
                  <Eye size={16} />
                  Live Preview
                </div>
                <div className="k-preview-controls">
                  <button
                    onClick={runCode}
                    className="k-preview-control-btn"
                    title="Refresh Preview"
                  >
                    <RotateCcw size={14} />
                  </button>
                </div>
              </div>
              
              <div className="k-preview-content">
                <iframe
                  ref={previewRef}
                  className="k-preview-iframe"
                  title="Preview"
                  sandbox="allow-scripts allow-same-origin"
                />
              </div>
            </div>
          </div>

          {/* Output Console */}
          <div className="k-playground-console">
            <div className="k-console-header">
              <Terminal size={16} />
              Console Output
            </div>
            <div className="k-console-content">
              {output && (
                <div className="k-console-message success">
                  {output}
                </div>
              )}
              {errors.map((error, index) => (
                <div key={index} className="k-console-message error">
                  {error}
                </div>
              ))}
              {!output && errors.length === 0 && (
                <div className="k-console-message muted">
                  Console output will appear here...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Default code templates
const defaultJSXCode = `import React from 'react';
import { KilatScene } from 'kilatanim.js';
import { useTheme } from 'kilat-core';

export default function MyComponent() {
  const { colors } = useTheme();
  
  return (
    <div className="k-demo-container">
      {/* Background Animation */}
      <div className="k-background">
        <KilatScene 
          preset="galaxy" 
          autoRotate={true}
          className="k-scene"
        />
      </div>
      
      {/* Content */}
      <div className="k-content">
        <h1 className="k-title k-glow">
          Hello Kilat.js! ⚡
        </h1>
        
        <p className="k-description">
          Welcome to the future of web development
        </p>
        
        <div className="k-actions">
          <button className="k-btn k-btn-primary k-btn-glow">
            Get Started
          </button>
          <button className="k-btn k-btn-outline">
            Learn More
          </button>
        </div>
      </div>
    </div>
  );
}`;

const defaultCSSCode = `.k-demo-container {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--k-background);
  border-radius: 1rem;
  overflow: hidden;
}

.k-background {
  position: absolute;
  inset: 0;
  opacity: 0.3;
}

.k-scene {
  width: 100%;
  height: 100%;
}

.k-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 2rem;
}

.k-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--k-primary);
  margin-bottom: 1rem;
}

.k-description {
  font-size: 1.125rem;
  color: var(--k-text-muted);
  margin-bottom: 2rem;
  max-width: 400px;
}

.k-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}`;

const defaultConfigCode = `import { defineConfig } from 'kilat-core';

export default defineConfig({
  theme: 'cyberpunk',
  mode: 'dark',
  
  router: {
    basePath: '/',
    middleware: ['auth']
  },
  
  database: {
    driver: 'sqlite',
    connection: {
      sqlite: { file: './data/app.db' }
    }
  },
  
  plugins: {
    auth: { enabled: true },
    cms: { enabled: true }
  }
});`;
