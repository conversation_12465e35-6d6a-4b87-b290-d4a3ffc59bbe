/* 🧩 Component Styles */

/* Error Boundary */
.k-error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--k-background);
  color: var(--k-text);
  padding: 2rem;
}

.k-error-container {
  text-align: center;
  max-width: 600px;
}

.k-error-icon {
  color: var(--k-destructive);
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.k-error-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-error-message {
  font-size: 1.125rem;
  color: var(--k-text-muted);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.k-error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.k-error-details {
  text-align: left;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 2rem;
}

.k-error-details h3 {
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-error-code,
.k-error-stack,
.k-error-component-stack {
  margin-bottom: 1rem;
}

.k-error-code strong,
.k-error-stack strong,
.k-error-component-stack strong {
  color: var(--k-text);
  display: block;
  margin-bottom: 0.5rem;
}

.k-error-stack pre,
.k-error-component-stack pre {
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.25rem;
  padding: 1rem;
  font-size: 0.875rem;
  overflow-x: auto;
  color: var(--k-text-muted);
}

/* Loading Spinner */
.k-loading-fullscreen {
  position: fixed;
  inset: 0;
  background: rgba(var(--k-background-rgb), 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.k-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.k-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.k-loading-message {
  color: var(--k-text-muted);
  font-size: 0.875rem;
  text-align: center;
}

/* Spinner Variants */
.k-spinner-default,
.k-spinner-glow,
.k-spinner-pulse {
  color: var(--k-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.k-spinner-glow {
  filter: drop-shadow(0 0 10px var(--k-primary));
}

.k-spinner-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.k-spinner-dot {
  width: 8px;
  height: 8px;
  background: var(--k-primary);
  border-radius: 50%;
}

/* Spinner Sizes */
.k-spinner-sm {
  width: 1rem;
  height: 1rem;
}

.k-spinner-md {
  width: 1.5rem;
  height: 1.5rem;
}

.k-spinner-lg {
  width: 2rem;
  height: 2rem;
}

.k-spinner-dots.k-spinner-sm .k-spinner-dot {
  width: 4px;
  height: 4px;
}

.k-spinner-dots.k-spinner-md .k-spinner-dot {
  width: 6px;
  height: 6px;
}

.k-spinner-dots.k-spinner-lg .k-spinner-dot {
  width: 8px;
  height: 8px;
}

/* Footer */
.k-footer {
  background: var(--k-surface);
  border-top: 1px solid var(--k-border);
  margin-top: auto;
}

.k-footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

.k-footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .k-footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

/* Footer Brand */
.k-footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.k-footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--k-primary);
  font-weight: bold;
  font-size: 1.25rem;
}

.k-footer-logo-icon {
  filter: drop-shadow(0 0 10px var(--k-primary));
}

.k-footer-logo-text {
  background: linear-gradient(45deg, var(--k-primary), var(--k-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.k-footer-description {
  color: var(--k-text-muted);
  line-height: 1.6;
  max-width: 300px;
}

.k-footer-social {
  display: flex;
  gap: 0.5rem;
}

.k-footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text-muted);
  transition: all 0.2s ease;
}

.k-footer-social-link:hover {
  color: var(--k-primary);
  border-color: var(--k-primary);
  transform: translateY(-2px);
}

/* Footer Links */
.k-footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

@media (max-width: 768px) {
  .k-footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .k-footer-links {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.k-footer-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-footer-section-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-footer-link {
  color: var(--k-text-muted);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.k-footer-link:hover {
  color: var(--k-primary);
}

.k-footer-link-external {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Footer Bottom */
.k-footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 2rem;
  border-top: 1px solid var(--k-border);
  flex-wrap: wrap;
  gap: 1rem;
}

@media (max-width: 768px) {
  .k-footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

.k-footer-bottom-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-footer-copyright {
  color: var(--k-text-muted);
  font-size: 0.875rem;
  margin: 0;
}

.k-footer-legal {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.k-footer-legal-link {
  color: var(--k-text-muted);
  text-decoration: none;
  font-size: 0.75rem;
  transition: color 0.2s ease;
}

.k-footer-legal-link:hover {
  color: var(--k-primary);
}

.k-footer-bottom-right {
  display: flex;
  align-items: center;
}

.k-footer-made-with {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--k-text-muted);
  font-size: 0.875rem;
  margin: 0;
}

.k-footer-heart {
  color: var(--k-destructive);
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 50%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.1);
  }
}
