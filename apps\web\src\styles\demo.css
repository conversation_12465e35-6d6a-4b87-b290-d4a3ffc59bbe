/* 🎮 Demo Page Styles */

.k-demo-page {
  min-height: 100vh;
  background: var(--k-background);
  color: var(--k-text);
  position: relative;
  overflow-x: hidden;
}

/* Demo Header */
.k-demo-header {
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
  padding: 2rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.k-demo-title-section {
  text-align: center;
  margin-bottom: 2rem;
}

.k-demo-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--k-primary);
  margin-bottom: 0.5rem;
}

.k-demo-icon {
  color: var(--k-accent);
  filter: drop-shadow(0 0 10px var(--k-accent));
}

.k-demo-subtitle {
  font-size: 1.125rem;
  color: var(--k-text-muted);
}

/* Demo Navigation */
.k-demo-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.k-demo-nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.k-demo-nav-btn:hover {
  background: var(--k-surface);
  border-color: var(--k-primary);
  transform: translateY(-2px);
}

.k-demo-nav-btn.active {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  border-color: var(--k-primary);
  box-shadow: 0 0 20px var(--k-primary-glow);
}

/* Demo Content */
.k-demo-content {
  padding: 2rem 0;
}

.k-demo-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  min-height: 80vh;
}

@media (max-width: 1024px) {
  .k-demo-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Demo Viewport */
.k-demo-viewport {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

.k-demo-viewport-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--k-background);
  border-bottom: 1px solid var(--k-border);
}

.k-demo-controls {
  display: flex;
  gap: 0.5rem;
}

.k-demo-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.25rem;
  color: var(--k-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-demo-control-btn:hover {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
}

.k-demo-viewport-title {
  font-weight: 600;
  color: var(--k-text);
}

.k-demo-actions {
  display: flex;
  gap: 0.5rem;
}

.k-demo-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: transparent;
  border: 1px solid var(--k-border);
  border-radius: 0.25rem;
  color: var(--k-text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-demo-action-btn:hover {
  color: var(--k-text);
  border-color: var(--k-primary);
}

/* Demo Viewport Content */
.k-demo-viewport-content {
  position: relative;
  height: 600px;
  overflow: hidden;
}

.k-demo-scene {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.k-demo-scene-canvas {
  width: 100%;
  height: 100%;
}

.k-demo-overlay {
  position: absolute;
  inset: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Demo Cards */
.k-demo-card {
  background: rgba(var(--k-surface-rgb), 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.k-demo-card-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--k-primary);
  margin-bottom: 1rem;
}

.k-demo-card-text {
  color: var(--k-text-muted);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.k-demo-card-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Component Showcase */
.k-component-showcase {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

/* Viewport Controls */
.k-viewport-controls {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

.k-viewport-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-viewport-btn:hover {
  background: var(--k-surface);
  border-color: var(--k-primary);
}

.k-viewport-btn.active {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  border-color: var(--k-primary);
}

/* Demo Sidebar */
.k-demo-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.k-demo-panel {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  padding: 1.5rem;
}

.k-demo-panel-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 1rem;
}

/* Settings */
.k-settings-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.k-setting {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-setting-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--k-text);
}

.k-setting-value {
  font-size: 0.875rem;
  color: var(--k-text-muted);
  font-family: var(--k-font-mono);
}

.k-setting-description {
  font-size: 0.75rem;
  color: var(--k-text-muted);
  line-height: 1.4;
}

/* Slider */
.k-slider {
  width: 100%;
  height: 4px;
  background: var(--k-border);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  appearance: none;
}

.k-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--k-primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px var(--k-primary-glow);
}

.k-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--k-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px var(--k-primary-glow);
}

/* Code Preview */
.k-code-preview {
  max-height: 300px;
  overflow-y: auto;
}

.k-code {
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  padding: 1rem;
  font-family: var(--k-font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--k-text);
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Responsive */
@media (max-width: 768px) {
  .k-demo-header {
    padding: 1rem 0;
  }
  
  .k-demo-title {
    font-size: 2rem;
  }
  
  .k-demo-nav {
    gap: 0.5rem;
  }
  
  .k-demo-nav-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .k-demo-viewport-content {
    height: 400px;
  }
  
  .k-demo-card {
    padding: 1.5rem;
  }
  
  .k-demo-sidebar {
    order: -1;
  }
}
