/* 🌐 Global Styles for Kilat.js Web App */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--k-font-sans);
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  background: var(--k-background);
  color: var(--k-text);
  font-family: var(--k-font-sans);
  overflow-x: hidden;
  min-height: 100vh;
}

/* App container */
.k-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.k-main {
  flex: 1;
  position: relative;
  z-index: 10;
}

/* Container utility */
.k-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .k-container {
    padding: 0 2rem;
  }
}

/* Section utilities */
.k-section {
  padding: 4rem 0;
}

.k-section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.k-section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-section-description {
  font-size: 1.125rem;
  color: var(--k-text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--k-text);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  line-height: 1.6;
  color: var(--k-text);
}

a {
  color: var(--k-primary);
  text-decoration: none;
  transition: color var(--k-transition-fast);
}

a:hover {
  color: var(--k-primary-hover);
}

/* Button base styles */
.k-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: var(--k-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--k-transition-fast);
  white-space: nowrap;
  user-select: none;
}

.k-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Button variants */
.k-btn-primary {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  border-color: var(--k-primary);
}

.k-btn-primary:hover:not(:disabled) {
  background: var(--k-primary-hover);
  transform: translateY(-1px);
}

.k-btn-outline {
  background: transparent;
  color: var(--k-primary);
  border-color: var(--k-primary);
}

.k-btn-outline:hover:not(:disabled) {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  transform: translateY(-1px);
}

.k-btn-ghost {
  background: transparent;
  color: var(--k-text);
  border-color: var(--k-border);
}

.k-btn-ghost:hover:not(:disabled) {
  background: var(--k-surface);
  border-color: var(--k-primary);
  transform: translateY(-1px);
}

/* Button sizes */
.k-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.k-btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Button effects */
.k-btn-glow {
  box-shadow: 0 0 20px var(--k-primary-glow);
}

.k-btn-glow:hover:not(:disabled) {
  box-shadow: 0 0 30px var(--k-primary-glow);
}

/* Form elements */
.k-input,
.k-textarea,
.k-select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: var(--k-radius-md);
  color: var(--k-text);
  font-size: 0.875rem;
  transition: all var(--k-transition-fast);
}

.k-input:focus,
.k-textarea:focus,
.k-select:focus {
  outline: none;
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(var(--k-primary-rgb), 0.1);
}

.k-input::placeholder,
.k-textarea::placeholder {
  color: var(--k-text-muted);
}

.k-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Checkbox and radio */
.k-checkbox,
.k-radio {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.k-checkbox input,
.k-radio input {
  width: 1rem;
  height: 1rem;
  accent-color: var(--k-primary);
}

/* Card component */
.k-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: var(--k-radius-lg);
  padding: 1.5rem;
  transition: all var(--k-transition-fast);
}

.k-card:hover {
  border-color: var(--k-primary);
  transform: translateY(-2px);
}

/* Badge component */
.k-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.k-badge-outline {
  background: transparent;
  color: var(--k-primary);
  border: 1px solid var(--k-primary);
}

/* Alert component */
.k-alert {
  padding: 1rem;
  border-radius: var(--k-radius-md);
  border: 1px solid;
  margin-bottom: 1rem;
}

.k-alert-info {
  background: rgba(var(--k-info), 0.1);
  border-color: var(--k-info);
  color: var(--k-info);
}

.k-alert-success {
  background: rgba(var(--k-success), 0.1);
  border-color: var(--k-success);
  color: var(--k-success);
}

.k-alert-warning {
  background: rgba(var(--k-warning), 0.1);
  border-color: var(--k-warning);
  color: var(--k-warning);
}

.k-alert-error {
  background: rgba(var(--k-destructive), 0.1);
  border-color: var(--k-destructive);
  color: var(--k-destructive);
}

/* Utility classes */
.k-text-center { text-align: center; }
.k-text-left { text-align: left; }
.k-text-right { text-align: right; }

.k-font-mono { font-family: var(--k-font-mono); }

.k-text-muted { color: var(--k-text-muted); }
.k-text-primary { color: var(--k-primary); }
.k-text-accent { color: var(--k-accent); }

.k-bg-surface { background: var(--k-surface); }
.k-bg-primary { background: var(--k-primary); }

.k-border { border: 1px solid var(--k-border); }
.k-border-primary { border: 1px solid var(--k-primary); }

.k-rounded { border-radius: var(--k-radius-md); }
.k-rounded-lg { border-radius: var(--k-radius-lg); }
.k-rounded-full { border-radius: 9999px; }

.k-shadow { box-shadow: var(--k-shadow-md); }
.k-shadow-lg { box-shadow: var(--k-shadow-lg); }

/* Spacing utilities */
.k-m-0 { margin: 0; }
.k-m-1 { margin: 0.25rem; }
.k-m-2 { margin: 0.5rem; }
.k-m-4 { margin: 1rem; }
.k-m-8 { margin: 2rem; }

.k-p-0 { padding: 0; }
.k-p-1 { padding: 0.25rem; }
.k-p-2 { padding: 0.5rem; }
.k-p-4 { padding: 1rem; }
.k-p-8 { padding: 2rem; }

.k-mb-2 { margin-bottom: 0.5rem; }
.k-mb-4 { margin-bottom: 1rem; }
.k-mb-8 { margin-bottom: 2rem; }

.k-mt-2 { margin-top: 0.5rem; }
.k-mt-4 { margin-top: 1rem; }
.k-mt-8 { margin-top: 2rem; }

/* Flexbox utilities */
.k-flex { display: flex; }
.k-flex-col { flex-direction: column; }
.k-flex-row { flex-direction: row; }
.k-items-center { align-items: center; }
.k-justify-center { justify-content: center; }
.k-justify-between { justify-content: space-between; }
.k-gap-2 { gap: 0.5rem; }
.k-gap-4 { gap: 1rem; }
.k-gap-8 { gap: 2rem; }

/* Grid utilities */
.k-grid { display: grid; }
.k-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.k-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.k-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.k-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:k-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:k-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
  .md\:k-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:k-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:k-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:k-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:k-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:k-grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* Animation utilities */
.k-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.k-slide-up {
  animation: slideUp 0.5s ease-out;
}

.k-glow {
  filter: drop-shadow(0 0 10px var(--k-primary-glow));
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--k-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--k-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--k-primary);
}

/* Selection styling */
::selection {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
}

::-moz-selection {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
}

/* Focus visible for accessibility */
:focus-visible {
  outline: 2px solid var(--k-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .k-no-print {
    display: none !important;
  }
  
  .k-app {
    background: white !important;
    color: black !important;
  }
}
