/* 🧭 Navigation Styles */

.k-navigation {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(var(--k-surface-rgb), 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--k-border);
}

.k-nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Logo */
.k-nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--k-primary);
  font-weight: bold;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.k-nav-logo:hover {
  transform: scale(1.05);
}

.k-nav-logo-icon {
  filter: drop-shadow(0 0 10px var(--k-primary));
}

.k-nav-logo-text {
  background: linear-gradient(45deg, var(--k-primary), var(--k-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Navigation Menu */
.k-nav-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.k-nav-menu-desktop {
  display: none;
}

@media (min-width: 768px) {
  .k-nav-menu-desktop {
    display: flex;
  }
}

.k-nav-menu-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
  flex-direction: column;
  padding: 1rem;
  gap: 0.5rem;
}

/* Navigation Items */
.k-nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  color: var(--k-text-muted);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.k-nav-item:hover {
  color: var(--k-text);
  background: var(--k-background);
  transform: translateY(-1px);
}

.k-nav-item.active {
  color: var(--k-primary);
  background: rgba(var(--k-primary-rgb), 0.1);
}

.k-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--k-primary);
  border-radius: 1px;
}

/* Actions */
.k-nav-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.k-nav-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-nav-action-btn:hover {
  color: var(--k-text);
  background: var(--k-background);
  border-color: var(--k-primary);
  transform: translateY(-1px);
}

/* Platform Indicator */
.k-nav-platform {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--k-text-muted);
  cursor: help;
}

/* Mobile Toggle */
.k-nav-mobile-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

@media (min-width: 768px) {
  .k-nav-mobile-toggle {
    display: none;
  }
}

.k-nav-mobile-toggle:hover {
  background: var(--k-background);
  border-color: var(--k-primary);
}

/* Theme Selector */
.k-nav-theme-selector {
  position: relative;
}

.k-theme-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 300px;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1001;
}

.k-theme-menu-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--k-background);
  border-bottom: 1px solid var(--k-border);
  font-weight: 600;
  color: var(--k-text);
}

.k-theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  padding: 1rem;
}

.k-theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-theme-option:hover {
  border-color: var(--k-primary);
  transform: translateY(-1px);
}

.k-theme-option.active {
  border-color: var(--k-primary);
  background: rgba(var(--k-primary-rgb), 0.1);
}

.k-theme-preview {
  display: flex;
  gap: 2px;
  align-items: center;
}

.k-theme-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Theme-specific colors */
.k-theme-option[data-theme="cyberpunk"] .k-theme-primary { background: #00ffff; }
.k-theme-option[data-theme="cyberpunk"] .k-theme-accent { background: #ff0080; }
.k-theme-option[data-theme="cyberpunk"] .k-theme-background { background: #0a0a0a; }

.k-theme-option[data-theme="nusantara"] .k-theme-primary { background: #d4af37; }
.k-theme-option[data-theme="nusantara"] .k-theme-accent { background: #8b4513; }
.k-theme-option[data-theme="nusantara"] .k-theme-background { background: #2d1810; }

.k-theme-option[data-theme="minimalist"] .k-theme-primary { background: #6366f1; }
.k-theme-option[data-theme="minimalist"] .k-theme-accent { background: #8b5cf6; }
.k-theme-option[data-theme="minimalist"] .k-theme-background { background: #f8fafc; }

.k-theme-option[data-theme="retro"] .k-theme-primary { background: #ff6b9d; }
.k-theme-option[data-theme="retro"] .k-theme-accent { background: #4ecdc4; }
.k-theme-option[data-theme="retro"] .k-theme-background { background: #1a1a2e; }

.k-theme-option[data-theme="aurora"] .k-theme-primary { background: #00d4aa; }
.k-theme-option[data-theme="aurora"] .k-theme-accent { background: #7c3aed; }
.k-theme-option[data-theme="aurora"] .k-theme-background { background: #0f172a; }

.k-theme-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--k-text);
}

.k-theme-menu-footer {
  padding: 1rem;
  border-top: 1px solid var(--k-border);
  background: var(--k-background);
}

.k-theme-select {
  width: 100%;
  padding: 0.5rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  font-size: 0.875rem;
}

.k-theme-select:focus {
  outline: none;
  border-color: var(--k-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .k-nav-container {
    padding: 1rem;
  }
  
  .k-nav-actions {
    gap: 0.25rem;
  }
  
  .k-nav-action-btn,
  .k-nav-platform {
    width: 2rem;
    height: 2rem;
  }
  
  .k-nav-action-btn svg,
  .k-nav-platform svg {
    width: 16px;
    height: 16px;
  }
  
  .k-theme-menu {
    width: 280px;
    right: -1rem;
  }
  
  .k-theme-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .k-nav-logo-text {
    display: none;
  }
  
  .k-theme-menu {
    width: calc(100vw - 2rem);
    right: -1rem;
  }
}
