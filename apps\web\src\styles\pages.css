/* 📄 Page Styles */

/* About Page */
.k-about-page {
  min-height: 100vh;
  background: var(--k-background);
  color: var(--k-text);
}

.k-about-hero {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--k-surface) 0%, var(--k-background) 100%);
}

.k-about-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

@media (max-width: 768px) {
  .k-about-hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.k-about-title {
  font-size: 3rem;
  font-weight: bold;
  color: var(--k-text);
  margin-bottom: 1.5rem;
}

.k-about-subtitle {
  font-size: 1.25rem;
  color: var(--k-text-muted);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.k-about-mission {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
}

.k-mission-icon {
  color: var(--k-primary);
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.k-about-mission h3 {
  color: var(--k-text);
  margin-bottom: 0.5rem;
}

.k-about-mission p {
  color: var(--k-text-muted);
  line-height: 1.5;
}

.k-about-hero-visual {
  position: relative;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
}

.k-about-scene {
  width: 100%;
  height: 100%;
}

/* Stats Section */
.k-about-stats {
  padding: 3rem 0;
  background: var(--k-surface);
  border-top: 1px solid var(--k-border);
  border-bottom: 1px solid var(--k-border);
}

.k-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

@media (max-width: 768px) {
  .k-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

.k-stat-card {
  text-align: center;
  padding: 1.5rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  transition: transform 0.2s ease;
}

.k-stat-card:hover {
  transform: translateY(-2px);
}

.k-stat-icon {
  color: var(--k-primary);
  margin-bottom: 1rem;
}

.k-stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--k-text);
  margin-bottom: 0.5rem;
}

.k-stat-label {
  color: var(--k-text-muted);
  font-size: 0.875rem;
}

/* Features Section */
.k-about-features {
  padding: 4rem 0;
}

.k-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .k-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.k-feature-card {
  padding: 2rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.k-feature-card:hover {
  transform: translateY(-4px);
  border-color: var(--k-primary);
  box-shadow: 0 10px 30px rgba(var(--k-primary-rgb), 0.1);
}

.k-feature-icon {
  color: var(--k-primary);
  margin-bottom: 1rem;
}

.k-feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-feature-description {
  color: var(--k-text-muted);
  line-height: 1.6;
}

/* Timeline Section */
.k-about-timeline {
  padding: 4rem 0;
  background: var(--k-surface);
}

.k-timeline {
  position: relative;
  max-width: 800px;
  margin: 3rem auto 0;
}

.k-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--k-border);
  transform: translateX(-50%);
}

.k-timeline-item {
  position: relative;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
}

.k-timeline-item:nth-child(odd) {
  flex-direction: row-reverse;
}

.k-timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.k-timeline-dot {
  width: 16px;
  height: 16px;
  background: var(--k-primary);
  border: 4px solid var(--k-background);
  border-radius: 50%;
  box-shadow: 0 0 20px var(--k-primary-glow);
}

.k-timeline-content {
  flex: 1;
  max-width: 45%;
  padding: 1.5rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
}

.k-timeline-year {
  color: var(--k-primary);
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.k-timeline-title {
  color: var(--k-text);
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.k-timeline-description {
  color: var(--k-text-muted);
  line-height: 1.5;
}

/* Team Section */
.k-about-team {
  padding: 4rem 0;
}

.k-team-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .k-team-grid {
    grid-template-columns: 1fr;
  }
}

.k-team-card {
  padding: 2rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.k-team-card:hover {
  transform: translateY(-4px);
}

.k-team-avatar {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.k-team-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--k-text);
  margin-bottom: 0.5rem;
}

.k-team-role {
  color: var(--k-primary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.k-team-bio {
  color: var(--k-text-muted);
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.k-team-link {
  color: var(--k-primary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.k-team-link:hover {
  text-decoration: underline;
}

/* CTA Section */
.k-about-cta {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--k-primary) 0%, var(--k-accent) 100%);
  color: white;
}

.k-cta-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.k-cta-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.k-cta-description {
  font-size: 1.125rem;
  opacity: 0.9;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.k-cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Docs Page */
.k-docs-page {
  min-height: 100vh;
  background: var(--k-background);
  color: var(--k-text);
}

.k-docs-header {
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
  padding: 2rem 0;
}

.k-docs-title-section {
  text-align: center;
  margin-bottom: 2rem;
}

.k-docs-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--k-primary);
  margin-bottom: 0.5rem;
}

.k-docs-icon {
  filter: drop-shadow(0 0 10px var(--k-primary));
}

.k-docs-subtitle {
  font-size: 1.125rem;
  color: var(--k-text-muted);
}

.k-docs-search {
  max-width: 500px;
  margin: 0 auto;
}

.k-search-input-wrapper {
  position: relative;
}

.k-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--k-text-muted);
}

.k-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  font-size: 1rem;
}

.k-search-input:focus {
  outline: none;
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(var(--k-primary-rgb), 0.1);
}

/* 404 Page */
.k-404-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--k-background);
  color: var(--k-text);
}

.k-404-background {
  position: absolute;
  inset: 0;
  opacity: 0.3;
}

.k-404-scene {
  width: 100%;
  height: 100%;
}

.k-404-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 0%, var(--k-background) 100%);
}

.k-404-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 2rem;
}

.k-404-container {
  max-width: 600px;
  margin: 0 auto;
}

.k-404-icon-container {
  position: relative;
  margin-bottom: 2rem;
}

.k-404-icon {
  color: var(--k-destructive);
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
}

.k-404-number {
  font-size: 6rem;
  font-weight: bold;
  color: var(--k-primary);
  text-shadow: 0 0 30px var(--k-primary-glow);
  margin-bottom: 1rem;
}

.k-404-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-404-description {
  font-size: 1.125rem;
  color: var(--k-text-muted);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.k-404-redirect-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.875rem;
}

.k-redirect-icon {
  color: var(--k-primary);
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.k-stop-redirect-btn {
  background: transparent;
  border: 1px solid var(--k-border);
  border-radius: 0.25rem;
  color: var(--k-text);
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  margin-left: 0.5rem;
}

.k-404-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.k-404-suggestions {
  margin-bottom: 2rem;
}

.k-suggestions-title {
  color: var(--k-text);
  margin-bottom: 1rem;
}

.k-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

@media (max-width: 768px) {
  .k-suggestions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.k-suggestion-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  text-decoration: none;
  transition: all 0.2s ease;
}

.k-suggestion-card:hover {
  border-color: var(--k-primary);
  transform: translateY(-2px);
}

.k-suggestion-icon {
  color: var(--k-primary);
}

.k-suggestion-title {
  font-size: 0.875rem;
  font-weight: 500;
}

.k-404-help {
  color: var(--k-text-muted);
  font-size: 0.875rem;
}

.k-404-link {
  color: var(--k-primary);
  text-decoration: none;
}

.k-404-link:hover {
  text-decoration: underline;
}
