/* 🎮 Playground Page Styles */

.k-playground-page {
  min-height: 100vh;
  background: var(--k-background);
  color: var(--k-text);
}

/* Playground Header */
.k-playground-header {
  background: var(--k-surface);
  border-bottom: 1px solid var(--k-border);
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.k-playground-title-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.k-playground-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 2rem;
  font-weight: bold;
  color: var(--k-primary);
  margin-bottom: 0.5rem;
}

.k-playground-icon {
  color: var(--k-accent);
  filter: drop-shadow(0 0 10px var(--k-accent));
}

.k-playground-subtitle {
  font-size: 1rem;
  color: var(--k-text-muted);
}

/* Playground Toolbar */
.k-playground-toolbar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.k-playground-divider {
  width: 1px;
  height: 2rem;
  background: var(--k-border);
  margin: 0 0.5rem;
}

/* Playground Content */
.k-playground-content {
  padding: 1rem 0;
}

.k-playground-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  min-height: 70vh;
}

.k-playground-layout .k-playground-preview.fullscreen {
  grid-column: 1 / -1;
}

.k-playground-layout .k-playground-editor.hidden {
  display: none;
}

@media (max-width: 1024px) {
  .k-playground-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Code Editor */
.k-playground-editor {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.k-editor-tabs {
  display: flex;
  background: var(--k-background);
  border-bottom: 1px solid var(--k-border);
}

.k-editor-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-right: 1px solid var(--k-border);
  color: var(--k-text-muted);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-editor-tab:hover {
  background: var(--k-surface);
  color: var(--k-text);
}

.k-editor-tab.active {
  background: var(--k-surface);
  color: var(--k-primary);
  border-bottom: 2px solid var(--k-primary);
}

.k-editor-content {
  flex: 1;
  position: relative;
}

.k-editor-textarea {
  width: 100%;
  height: 100%;
  min-height: 400px;
  padding: 1rem;
  background: var(--k-background);
  border: none;
  color: var(--k-text);
  font-family: var(--k-font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  outline: none;
  tab-size: 2;
}

.k-editor-textarea::placeholder {
  color: var(--k-text-muted);
}

/* Preview */
.k-playground-preview {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.k-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: var(--k-background);
  border-bottom: 1px solid var(--k-border);
}

.k-preview-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--k-text);
}

.k-preview-controls {
  display: flex;
  gap: 0.5rem;
}

.k-preview-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 0.25rem;
  color: var(--k-text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-preview-control-btn:hover {
  color: var(--k-text);
  border-color: var(--k-primary);
}

.k-preview-content {
  flex: 1;
  position: relative;
  background: #fff;
}

.k-preview-iframe {
  width: 100%;
  height: 100%;
  min-height: 400px;
  border: none;
  background: #fff;
}

/* Console */
.k-playground-console {
  margin-top: 1rem;
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: 1rem;
  overflow: hidden;
}

.k-console-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--k-background);
  border-bottom: 1px solid var(--k-border);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--k-text);
}

.k-console-content {
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
  font-family: var(--k-font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

.k-console-message {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.k-console-message.success {
  background: rgba(34, 197, 94, 0.1);
  color: rgb(34, 197, 94);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.k-console-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.k-console-message.muted {
  color: var(--k-text-muted);
  font-style: italic;
}

/* Button Styles */
.k-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  height: auto;
}

.k-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--k-primary);
  color: var(--k-primary-foreground);
  border: 1px solid var(--k-primary);
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-btn:hover {
  background: var(--k-primary-hover);
  transform: translateY(-1px);
}

.k-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.k-btn-outline {
  background: transparent;
  color: var(--k-primary);
  border-color: var(--k-primary);
}

.k-btn-outline:hover {
  background: var(--k-primary);
  color: var(--k-primary-foreground);
}

.k-btn-ghost {
  background: transparent;
  color: var(--k-text);
  border-color: var(--k-border);
}

.k-btn-ghost:hover {
  background: var(--k-surface);
  border-color: var(--k-primary);
}

/* Select Styles */
.k-select {
  padding: 0.5rem 1rem;
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: 0.5rem;
  color: var(--k-text);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.k-select:hover {
  border-color: var(--k-primary);
}

.k-select:focus {
  outline: none;
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(var(--k-primary-rgb), 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .k-playground-header {
    padding: 1rem 0;
  }
  
  .k-playground-title {
    font-size: 1.5rem;
  }
  
  .k-playground-toolbar {
    gap: 0.25rem;
  }
  
  .k-btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .k-playground-divider {
    display: none;
  }
  
  .k-editor-textarea {
    min-height: 300px;
    font-size: 0.75rem;
  }
  
  .k-preview-iframe {
    min-height: 300px;
  }
  
  .k-console-content {
    max-height: 150px;
    font-size: 0.75rem;
  }
}
