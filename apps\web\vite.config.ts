import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react()
  ],
  
  // Development server configuration
  server: {
    port: 3000,
    host: true,
    open: true,
    cors: true,
    hmr: {
      overlay: true
    }
  },
  
  // Preview server configuration
  preview: {
    port: 4173,
    host: true,
    open: true
  },
  
  // Build configuration
  build: {
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    minify: 'esbuild',
    
    // Chunk splitting for better caching
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'animation-vendor': ['framer-motion'],
          'ui-vendor': ['lucide-react'],
          
          // Kilat.js chunks
          'kilat-core': ['kilat-core'],
          'kilat-router': ['kilat-router'],
          'kilat-plugins': ['kilat-plugins'],
          'kilatcss': ['kilatcss'],
          'kilatanim': ['kilatanim.js']
        },
        
        // Asset naming
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          
          if (/\.(png|jpe?g|gif|svg|webp|avif)$/i.test(assetInfo.name || '')) {
            return 'assets/images/[name]-[hash].[ext]';
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return 'assets/fonts/[name]-[hash].[ext]';
          }
          
          if (/\.css$/i.test(assetInfo.name || '')) {
            return 'assets/css/[name]-[hash].[ext]';
          }
          
          return `assets/${ext}/[name]-[hash].[ext]`;
        }
      }
    },
    
    // Bundle size warnings
    chunkSizeWarningLimit: 1000,
    
    // Asset inlining threshold
    assetsInlineLimit: 4096
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@components': resolve(__dirname, './src/components'),
      '@pages': resolve(__dirname, './src/pages'),
      '@styles': resolve(__dirname, './src/styles'),
      '@utils': resolve(__dirname, './src/utils'),
      '@assets': resolve(__dirname, './src/assets'),
      '@types': resolve(__dirname, './src/types')
    }
  },
  
  // CSS configuration
  css: {
    modules: {
      localsConvention: 'camelCase'
    },
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    },
    devSourcemap: true
  },
  
  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __COMMIT_HASH__: JSON.stringify(process.env.VITE_COMMIT_HASH || 'dev')
  },
  
  // Optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react'
    ],
    exclude: [
      'kilat-core',
      'kilat-router',
      'kilat-plugins',
      'kilatcss',
      'kilatanim.js'
    ]
  },
  
  // Worker configuration
  worker: {
    format: 'es'
  },
  
  // JSON configuration
  json: {
    namedExports: true,
    stringify: false
  },
  
  // Asset handling
  assetsInclude: [
    '**/*.gltf',
    '**/*.glb',
    '**/*.hdr',
    '**/*.exr'
  ],
  
  // Experimental features
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `window.__kilatAssetUrl("${filename}")` };
      } else {
        return { relative: true };
      }
    }
  },
  
  // ESBuild configuration
  esbuild: {
    target: 'es2020',
    format: 'esm',
    platform: 'browser',
    
    // Remove console.log in production
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    
    // JSX configuration
    jsx: 'automatic',
    jsxDev: process.env.NODE_ENV === 'development'
  },
  
  // Base URL for deployment
  base: process.env.VITE_BASE_URL || '/',
  
  // Public directory
  publicDir: 'public',
  
  // Environment directory
  envDir: '.',
  
  // Environment prefix
  envPrefix: ['VITE_', 'KILAT_'],
  
  // Clear screen on rebuild
  clearScreen: false,
  
  // Log level
  logLevel: 'info'
});
