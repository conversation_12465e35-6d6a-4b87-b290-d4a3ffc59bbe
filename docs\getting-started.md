# 🚀 Getting Started with <PERSON><PERSON>.js

Selamat datang di Kilat.js! Framework fullstack masa depan dari Nusantara yang menggabungkan kekuatan React, TypeScript, dan Bun dalam satu ekosistem terintegrasi.

## 📋 Prerequisites

Sebelum memulai, pastikan Anda memiliki:

- **Bun** >= 1.0.0 (Recommended) atau **Node.js** >= 18.0.0
- **Git** untuk version control
- **Code Editor** (VS Code recommended dengan Kilat.js extension)

## ⚡ Quick Start

### 1. Instalasi

```bash
# Menggunakan Bun (Recommended)
bun create kilat-app my-app

# Atau menggunakan npm
npm create kilat-app my-app

# Atau menggunakan yarn
yarn create kilat-app my-app
```

### 2. Masuk ke direktori project

```bash
cd my-app
```

### 3. Install dependencies

```bash
bun install
```

### 4. Jalankan development server

```bash
bun dev
```

Aplikasi akan be<PERSON><PERSON><PERSON> di `http://localhost:3000` 🎉

## 📁 Struktur Project

```
my-app/
├── 📱 pages/                  → File-based routing
│   ├── index.tsx             → Homepage (/)
│   ├── about.tsx             → About page (/about)
│   └── api/                  → API routes
│       └── users.ts          → /api/users
├── 🧩 components/            → React components
│   ├── ui/                   → UI components
│   └── layout/               → Layout components
├── 🎨 styles/                → CSS files
│   ├── globals.css           → Global styles
│   └── components/           → Component styles
├── 📂 public/                → Static assets
│   ├── images/               → Images
│   └── icons/                → Icons
├── 🗃️ database/              → Database files
│   ├── migrations/           → Database migrations
│   ├── seeds/                → Database seeds
│   └── models/               → Database models
├── 🔧 kilat.config.ts        → Kilat.js configuration
├── 📦 package.json           → Dependencies
└── 🔒 .env                   → Environment variables
```

## ⚙️ Konfigurasi Dasar

Buat file `kilat.config.ts` di root project:

```typescript
import { defineConfig } from 'kilat-core';

export default defineConfig({
  // 🎨 Theme Configuration
  theme: 'cyberpunk',
  mode: 'dark',
  
  // 🧭 Router Configuration
  router: {
    basePath: '/',
    middleware: ['auth', 'i18n'],
    transitions: {
      type: 'fade',
      duration: 300
    }
  },
  
  // 🗃️ Database Configuration
  database: {
    driver: 'sqlite',
    connection: {
      sqlite: { 
        file: './data/app.db' 
      }
    },
    migrations: {
      directory: './database/migrations'
    }
  },
  
  // 🖥️ Backend Configuration
  backend: {
    port: 8080,
    cors: { enabled: true },
    compression: { enabled: true },
    rateLimit: { 
      enabled: true,
      max: 100,
      windowMs: 15 * 60 * 1000 // 15 minutes
    }
  },
  
  // 🔌 Plugin Configuration
  plugins: {
    auth: { 
      enabled: true,
      providers: ['local', 'google', 'github']
    },
    cms: { 
      enabled: true,
      contentTypes: ['posts', 'pages']
    },
    analytics: {
      enabled: true,
      provider: 'google-analytics',
      trackingId: 'GA_TRACKING_ID'
    }
  },
  
  // 🛡️ Error Handling
  errorHandling: {
    enabled: true,
    crashReport: {
      enabled: true,
      webhookURL: process.env.CRASH_WEBHOOK_URL
    },
    safeMode: true
  },
  
  // 🚀 Build Configuration
  build: {
    target: 'es2020',
    minify: true,
    sourcemap: true,
    splitting: true
  }
});
```

## 🎨 Menggunakan Tema

Kilat.js menyediakan 15+ tema siap pakai:

```tsx
import { useTheme } from 'kilat-core';

function ThemeSwitcher() {
  const { theme, setTheme, availableThemes } = useTheme();
  
  return (
    <select 
      value={theme} 
      onChange={(e) => setTheme(e.target.value)}
      className="k-select k-select-primary"
    >
      {availableThemes.map(themeName => (
        <option key={themeName} value={themeName}>
          {themeName}
        </option>
      ))}
    </select>
  );
}
```

### Tema Tersedia:
- **cyberpunk** - Futuristik dengan efek neon
- **nusantara** - Tradisional Indonesia modern
- **minimalist** - Bersih dan sederhana
- **retro** - Nostalgia 80s
- **aurora** - Cahaya utara
- **material** - Google Material Design
- **neumorphism** - Soft UI
- Dan masih banyak lagi...

## 🌌 Animasi 3D

Tambahkan animasi 3D dengan mudah:

```tsx
import { KilatScene } from 'kilatanim.js';

function HomePage() {
  return (
    <div className="k-page">
      {/* Background 3D Scene */}
      <KilatScene 
        preset="galaxy" 
        autoRotate={true}
        className="k-scene-background"
      />
      
      <div className="k-content">
        <h1 className="k-title k-glow">
          Welcome to Kilat.js ⚡
        </h1>
      </div>
    </div>
  );
}
```

### Preset Animasi Tersedia:
- **galaxy** - Rotating galaxy dengan stars
- **matrix** - Digital rain effect
- **neonTunnel** - Cyberpunk tunnel
- **cyberwave** - Retro wave animation
- **glowParticles** - Floating particles
- **hologram** - Holographic effect
- **plasma** - Plasma energy field
- **nusantara** - Floating islands dengan batik

## 🧭 Routing

Kilat.js menggunakan file-based routing:

```
pages/
├── index.tsx              → /
├── about.tsx              → /about
├── blog/
│   ├── index.tsx          → /blog
│   ├── [slug].tsx         → /blog/:slug
│   └── category/
│       └── [id].tsx       → /blog/category/:id
└── api/
    ├── users.ts           → /api/users
    └── auth/
        └── login.ts       → /api/auth/login
```

### Dynamic Routes

```tsx
// pages/blog/[slug].tsx
import { useRouter } from 'kilat-router';

export default function BlogPost() {
  const { params, query } = useRouter();
  
  return (
    <article>
      <h1>Post: {params.slug}</h1>
      <p>Category: {query.category}</p>
    </article>
  );
}
```

### API Routes

```typescript
// pages/api/users.ts
import { NextApiRequest, NextApiResponse } from 'kilat-backend';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    const users = await User.all();
    res.json(users);
  } else if (req.method === 'POST') {
    const user = await User.create(req.body);
    res.status(201).json(user);
  }
}
```

## 🗃️ Database & ORM

### Model Definition

```typescript
// database/models/User.ts
import { Model } from 'kilat-db';

export class User extends Model {
  static tableName = 'users';
  static fillable = ['name', 'email', 'password'];
  static hidden = ['password'];
  
  static validationRules = {
    name: 'required|string|min:2',
    email: 'required|email|unique:users',
    password: 'required|string|min:8'
  };
  
  // Relationships
  posts() {
    return this.hasMany(Post);
  }
  
  profile() {
    return this.hasOne(Profile);
  }
}
```

### Query Examples

```typescript
// Create
const user = await User.create({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'hashedpassword'
});

// Find
const users = await User.where('active', true).limit(10).get();
const user = await User.find(1);

// Update
await User.where('id', 1).update({ name: 'Jane Doe' });

// Delete
await User.where('id', 1).delete();

// Relations
const userWithPosts = await User.with('posts').find(1);
```

### Migrations

```bash
# Create migration
kilat db create add_users_table

# Run migrations
kilat db migrate

# Rollback
kilat db rollback

# Reset database
kilat db reset
```

## 🔌 Plugin System

### Menggunakan Plugin

```typescript
// kilat.config.ts
export default defineConfig({
  plugins: {
    auth: {
      enabled: true,
      providers: ['local', 'google', 'github'],
      sessionTimeout: 24 * 60 * 60 * 1000
    },
    cms: {
      enabled: true,
      contentTypes: ['posts', 'pages'],
      mediaStorage: 'local'
    }
  }
});
```

### Membuat Plugin

```typescript
import { KilatPlugin } from 'kilat-plugins';

export class MyPlugin implements KilatPlugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  async onInit(context) {
    console.log('Plugin initialized');
  }
  
  async onRequest(context) {
    // Handle requests
  }
  
  async onDestroy() {
    console.log('Plugin destroyed');
  }
}
```

## 🛠️ CLI Commands

```bash
# Project management
kilat create my-app          # Create new project
kilat dev                    # Start development
kilat build                  # Build for production
kilat preview                # Preview production build
kilat deploy                 # Deploy to platform

# Database
kilat db migrate             # Run migrations
kilat db seed                # Seed database
kilat db reset               # Reset database
kilat db status              # Show migration status

# Plugins
kilat plugin list            # List installed plugins
kilat plugin install auth   # Install plugin
kilat plugin uninstall auth # Uninstall plugin
kilat plugin create my-plugin # Create new plugin

# Themes
kilat theme list             # List available themes
kilat theme set cyberpunk    # Set theme
kilat theme create my-theme  # Create custom theme

# Utilities
kilat doctor                 # Health check
kilat analytics              # View analytics
kilat config get theme       # Get config value
kilat config set theme cyberpunk # Set config value
```

## 🚀 Deployment

### Vercel

```bash
kilat deploy --platform vercel
```

### Netlify

```bash
kilat deploy --platform netlify
```

### Docker

```bash
kilat build --platform docker
docker run -p 3000:3000 my-app
```

## 📚 Next Steps

1. **Explore Examples** - Lihat contoh aplikasi di `/examples`
2. **Read Documentation** - Pelajari lebih lanjut di `/docs`
3. **Join Community** - Bergabung dengan komunitas Kilat.js
4. **Contribute** - Kontribusi ke pengembangan framework

## 🆘 Bantuan

- 📖 **Documentation**: [docs.kilat-js.pcode.my.id](https://docs.kilat-js.pcode.my.id)
- 💬 **Discord**: [discord.gg/kilatjs](https://discord.gg/kilatjs)
- 🐛 **Issues**: [github.com/kangpcode/kilat.js/issues](https://github.com/kangpcode/kilat.js/issues)
- 📧 **Email**: <EMAIL>

Selamat coding dengan Kilat.js! ⚡
