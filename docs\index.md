# 📚 Kilat.js Documentation

Selamat datang di dokumentasi Kilat.js! Framework fullstack masa depan dari Nusantara.

## 🚀 Getting Started

### Instalasi

```bash
# Install dengan Bun (Recommended)
bun create kilat-app my-app

# Atau dengan npm
npm create kilat-app my-app

# Atau dengan yarn
yarn create kilat-app my-app
```

### Struktur Project

```
my-app/
├── pages/                  → File-based routing
│   ├── index.tsx          → Homepage (/)
│   ├── about.tsx          → About page (/about)
│   └── api/               → API routes
├── components/            → React components
├── styles/                → CSS files
├── public/                → Static assets
├── database/              → Database files
│   ├── migrations/        → Database migrations
│   └── seeds/             → Database seeds
├── kilat.config.ts        → Kilat.js configuration
└── package.json
```

### Konfigurasi Dasar

```typescript
// kilat.config.ts
export default {
  theme: 'cyberpunk',
  mode: 'dark',
  
  router: {
    basePath: '/',
    middleware: ['auth', 'i18n']
  },
  
  database: {
    driver: 'sqlite',
    connection: {
      sqlite: { file: './data.db' }
    }
  },
  
  plugins: {
    auth: { enabled: true },
    cms: { enabled: true }
  }
};
```

## 🎨 Tema UI

Kilat.js menyediakan 15+ tema yang dapat dipilih:

### Tema Utama

- **cyberpunk** - Futuristik dengan efek neon
- **nusantara** - Tradisional Indonesia dengan sentuhan modern
- **minimalist** - Bersih dan sederhana
- **retro** - Nostalgia 80s dengan pixel art
- **aurora** - Cahaya utara dengan gradient

### Menggunakan Tema

```typescript
// Dalam konfigurasi
export default {
  theme: 'nusantara',
  mode: 'dark'
};

// Atau secara dinamis
import { useTheme } from 'kilat-core';

function ThemeSwitcher() {
  const { setTheme } = useTheme();
  
  return (
    <button onClick={() => setTheme('cyberpunk')}>
      Switch to Cyberpunk
    </button>
  );
}
```

## 🌌 Animasi 3D

KilatAnim.js menyediakan preset animasi 3D yang siap pakai:

```tsx
import { KilatScene } from 'kilatanim.js';

// Galaxy preset
<KilatScene preset="galaxy" autoRotate />

// Matrix digital rain
<KilatScene preset="matrix" speed={2} />

// Neon tunnel
<KilatScene 
  preset="neonTunnel" 
  colors={['#00ffff', '#ff00ff']} 
/>

// Nusantara floating islands
<KilatScene 
  preset="nusantara" 
  islandCount={8}
  batikPatterns={true}
/>
```

## 🧭 Routing

Kilat.js menggunakan file-based routing seperti Next.js:

```
pages/
├── index.tsx              → /
├── about.tsx              → /about
├── blog/
│   ├── index.tsx          → /blog
│   ├── [slug].tsx         → /blog/:slug
│   └── category/
│       └── [id].tsx       → /blog/category/:id
└── api/
    ├── users.ts           → /api/users
    └── auth/
        └── login.ts       → /api/auth/login
```

### Dynamic Routes

```tsx
// pages/blog/[slug].tsx
import { useRouter } from 'kilat-router';

export default function BlogPost() {
  const { params, query } = useRouter();
  
  return (
    <div>
      <h1>Post: {params.slug}</h1>
      <p>Category: {query.category}</p>
    </div>
  );
}
```

### Middleware

```typescript
// middleware/auth.ts
export function authMiddleware(context) {
  const token = context.req.headers.authorization;
  
  if (!token) {
    return context.redirect('/login');
  }
  
  return context.next();
}
```

## 🗃️ Database

Kilat.js mendukung SQLite dan MySQL dengan ORM yang mudah digunakan:

### Model Definition

```typescript
import { defineModel } from 'kilat-db';

export const User = defineModel('users', {
  id: { type: 'number', primaryKey: true },
  name: { type: 'string', required: true },
  email: { type: 'string', unique: true },
  password: { type: 'string', required: true },
  createdAt: { type: 'date', default: () => new Date() }
});

export const Post = defineModel('posts', {
  id: { type: 'number', primaryKey: true },
  title: { type: 'string', required: true },
  content: { type: 'text' },
  userId: { type: 'number', foreignKey: 'users.id' },
  published: { type: 'boolean', default: false }
});
```

### Query Examples

```typescript
// Create
const user = await User.create({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'hashedpassword'
});

// Find
const users = await User.where('active', true).limit(10).get();
const user = await User.find(1);

// Update
await User.where('id', 1).update({ name: 'Jane Doe' });

// Delete
await User.where('id', 1).delete();

// Relations
const userWithPosts = await User.with('posts').find(1);
```

### Migrations

```bash
# Create migration
kilat db create add_users_table

# Run migrations
kilat db migrate

# Rollback
kilat db rollback

# Reset database
kilat db reset
```

## 🔌 Plugin System

Kilat.js memiliki sistem plugin yang powerful:

### Plugin Resmi

- **auth** - Authentication & authorization
- **cms** - Content management system
- **payments** - Payment gateway integration
- **ai** - AI assistant features
- **monitoring** - Application monitoring

### Menggunakan Plugin

```typescript
// kilat.config.ts
export default {
  plugins: {
    auth: {
      enabled: true,
      providers: ['local', 'google', 'github'],
      sessionTimeout: 24 * 60 * 60 * 1000
    },
    cms: {
      enabled: true,
      contentTypes: ['posts', 'pages'],
      mediaStorage: 'local'
    }
  }
};
```

### Membuat Plugin

```typescript
import { KilatPlugin } from 'kilat-plugins';

export class MyPlugin implements KilatPlugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  async onInit(context) {
    console.log('Plugin initialized');
  }
  
  async onRequest(context) {
    // Handle requests
  }
  
  async onDestroy() {
    console.log('Plugin destroyed');
  }
}
```

## 🛠️ CLI Commands

```bash
# Project management
kilat create my-app          # Create new project
kilat dev                    # Start development
kilat build                  # Build for production
kilat preview                # Preview production build
kilat deploy                 # Deploy to platform

# Database
kilat db migrate             # Run migrations
kilat db seed                # Seed database
kilat db reset               # Reset database
kilat db status              # Show migration status

# Plugins
kilat plugin list            # List installed plugins
kilat plugin install auth   # Install plugin
kilat plugin uninstall auth # Uninstall plugin
kilat plugin create my-plugin # Create new plugin

# Themes
kilat theme list             # List available themes
kilat theme set cyberpunk    # Set theme
kilat theme create my-theme  # Create custom theme

# Utilities
kilat doctor                 # Health check
kilat analytics              # View analytics
kilat config get theme       # Get config value
kilat config set theme cyberpunk # Set config value
```

## 🌐 Multi-Platform

### Web Application

```bash
# Start web development
kilat dev

# Build for web
kilat build --platform web

# Deploy to Vercel/Netlify
kilat deploy --platform vercel
```

### Desktop Application

```bash
# Start desktop development
kilat dev --platform desktop

# Build desktop app
kilat build --platform desktop

# Package for distribution
kilat package --platform desktop
```

### Mobile Application

```bash
# Start mobile development
kilat dev --platform mobile

# Build for iOS/Android
kilat build --platform mobile

# Run on device
kilat run ios
kilat run android
```

## 🧪 Testing

```bash
# Unit tests
kilat test

# E2E tests
kilat test --e2e

# Coverage report
kilat test --coverage

# Watch mode
kilat test --watch

# Specific test file
kilat test components/Button.test.tsx
```

## 📊 Monitoring

Kilat.js menyediakan monitoring built-in:

```typescript
import { useMonitoring } from 'kilat-core';

function MyComponent() {
  const { trackEvent, recordMetric } = useMonitoring();
  
  const handleClick = () => {
    trackEvent('button_click', {
      component: 'MyComponent',
      location: 'header'
    });
    
    recordMetric('user_interactions', 1);
  };
  
  return <button onClick={handleClick}>Click me</button>;
}
```

## 🛡️ Error Handling

```typescript
// Error boundaries
import { ErrorBoundary } from 'kilat-core';

function App() {
  return (
    <ErrorBoundary fallback={<ErrorPage />}>
      <MyApp />
    </ErrorBoundary>
  );
}

// Global error handling
export default {
  errorHandling: {
    killSwitch: true,
    safeMode: true,
    crashReport: {
      enabled: true,
      webhookURL: 'https://hooks.slack.com/...'
    }
  }
};
```

## 🔗 Links

- [KilatCSS Guide](./kilatcss.md)
- [Animations Guide](./kilatanim.md)
- [Router Guide](./router.md)
- [Database Guide](./db.md)
- [Plugin Development](./plugins.md)
- [CLI Reference](./cli.md)
- [API Reference](./api.md)
