import type { KilatConfig } from './packages/kilat-core/src/types';

/**
 * Kilat.js ⚡ Global Configuration
 * Framework Glow Futuristik Nusantara
 */
export default {
  // 🎨 UI Theme Configuration
  theme: "cyberpunk", // cyberpunk | nusantara | retro | material | neumorphism | carbon | minimalist | asymetric | elemen3d | dana | ark | aurora | unix | classic
  mode: "dark", // dark | light | auto
  
  // 🌌 3D Animation Scene
  presetScene: "galaxy", // galaxy | matrix | neonTunnel | cyberwave | glowParticles
  animation: {
    autoRotate: true,
    background: "#000",
    interactive: true,
    ambientSound: false
  },

  // 🔀 Router Configuration
  router: {
    basePath: "/",
    middleware: ["auth", "i18n"],
    fileBasedRouting: true,
    dynamicRoutes: true,
    layoutNesting: true
  },

  // 🗃️ Database Configuration
  database: {
    driver: "sqlite", // sqlite | mysql
    connection: {
      sqlite: { 
        file: "./data.db",
        enableWAL: true,
        timeout: 5000
      },
      mysql: {
        host: "localhost",
        port: 3306,
        user: "root",
        password: "pass",
        database: "kilat",
        ssl: false
      }
    },
    migrations: {
      directory: "./migrations",
      autoRun: true
    }
  },

  // 🔧 Build Engine Configuration
  build: {
    engine: "kilatpack", // kilatpack | vite | webpack
    target: "es2022",
    minify: true,
    sourcemap: true,
    debugOverlay: false,
    hotReload: true,
    analyze: false,
    outputDir: "dist",
    publicDir: "public",
    assetsDir: "assets"
  },

  // 🔌 Plugin System
  plugins: [
    "auth",      // Authentication & Authorization
    "upload",    // File Upload Handler
    "i18n",      // Internationalization
    "realtime",  // WebSocket/SignalR
    "payments"   // Payment Gateway Integration
  ],

  // 🤖 AI Assistant (Optional)
  aiAssistant: {
    enabled: false,
    endpoint: "/api/ai",
    model: "gpt-4",
    features: ["code-completion", "bug-detection", "optimization"]
  },

  // 🌐 Platform Support
  platform: {
    web: {
      enabled: true,
      ssr: true,
      pwa: true
    },
    desktop: {
      enabled: true,
      electron: true,
      tauri: false
    },
    mobile: {
      enabled: true,
      expo: true,
      capacitor: false
    }
  },

  // 📊 Development Tools
  dev: {
    port: 3000,
    host: "localhost",
    open: true,
    cors: true,
    proxy: {},
    hmr: {
      port: 24678,
      overlay: true
    }
  },

  // 🎯 Performance Optimization
  performance: {
    bundleSplitting: true,
    treeshaking: true,
    compression: "gzip", // gzip | brotli | none
    caching: {
      enabled: true,
      strategy: "stale-while-revalidate"
    }
  },

  // 🔒 Security Configuration
  security: {
    csp: {
      enabled: true,
      directives: {
        "default-src": ["'self'"],
        "script-src": ["'self'", "'unsafe-inline'"],
        "style-src": ["'self'", "'unsafe-inline'"],
        "img-src": ["'self'", "data:", "https:"]
      }
    },
    cors: {
      origin: ["http://localhost:3000"],
      credentials: true
    }
  }
} satisfies KilatConfig;
