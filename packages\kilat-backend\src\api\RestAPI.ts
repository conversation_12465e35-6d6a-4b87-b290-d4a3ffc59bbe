import { createLogger } from 'kilat-utils';
import type { 
  Context, 
  RestAPIConfig, 
  ResourceConfig, 
  ValidationSchema,
  AuthConfig,
  CacheConfig
} from '../types';
import { KilatServer } from '../core/KilatServer';
import { ValidationManager } from '../validation/ValidationManager';
import { AuthManager } from '../auth/AuthManager';

/**
 * 🔌 RestAPI - Auto-generated REST API from models
 * Provides CRUD operations with validation, auth, and caching
 */
export class RestAPI {
  private server: KilatServer;
  private config: RestAPIConfig;
  private validationManager: ValidationManager;
  private authManager: AuthManager;
  private logger = createLogger({ prefix: 'RestAPI' });
  private resources = new Map<string, ResourceConfig>();

  constructor(server: KilatServer, config: RestAPIConfig = {}) {
    this.server = server;
    this.config = {
      prefix: '/api',
      version: 'v1',
      pagination: { enabled: true, defaultLimit: 20, maxLimit: 100 },
      validation: { enabled: true, strict: true },
      auth: { enabled: false },
      cache: { enabled: true, ttl: 300 },
      cors: { enabled: true },
      rateLimit: { enabled: true, max: 1000, windowMs: 60000 },
      ...config
    };

    this.validationManager = new ValidationManager();
    this.authManager = new AuthManager(this.config.auth);
  }

  // 📋 Register resource
  resource<T = any>(name: string, config: Partial<ResourceConfig<T>> = {}): void {
    const resourceConfig: ResourceConfig<T> = {
      name,
      path: `/${name}`,
      model: name,
      operations: ['list', 'create', 'read', 'update', 'delete'],
      validation: {},
      auth: this.config.auth,
      cache: this.config.cache,
      hooks: {},
      ...config
    };

    this.resources.set(name, resourceConfig);
    this.setupResourceRoutes(resourceConfig);
    
    this.logger.info(`Resource registered: ${name}`);
  }

  // 🛤️ Setup routes for resource
  private setupResourceRoutes<T>(config: ResourceConfig<T>): void {
    const basePath = `${this.config.prefix}/${this.config.version}${config.path}`;

    // List resources: GET /api/v1/users
    if (config.operations.includes('list')) {
      this.server.get(basePath, this.createListHandler(config));
    }

    // Create resource: POST /api/v1/users
    if (config.operations.includes('create')) {
      this.server.post(basePath, this.createCreateHandler(config));
    }

    // Read resource: GET /api/v1/users/:id
    if (config.operations.includes('read')) {
      this.server.get(`${basePath}/:id`, this.createReadHandler(config));
    }

    // Update resource: PUT /api/v1/users/:id
    if (config.operations.includes('update')) {
      this.server.put(`${basePath}/:id`, this.createUpdateHandler(config));
      this.server.patch(`${basePath}/:id`, this.createUpdateHandler(config));
    }

    // Delete resource: DELETE /api/v1/users/:id
    if (config.operations.includes('delete')) {
      this.server.delete(`${basePath}/:id`, this.createDeleteHandler(config));
    }

    // Bulk operations
    if (config.operations.includes('bulk')) {
      this.server.post(`${basePath}/bulk`, this.createBulkHandler(config));
      this.server.delete(`${basePath}/bulk`, this.createBulkDeleteHandler(config));
    }

    // Search endpoint
    if (config.operations.includes('search')) {
      this.server.get(`${basePath}/search`, this.createSearchHandler(config));
    }
  }

  // 📋 Create list handler
  private createListHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Run before hooks
        if (config.hooks?.beforeList) {
          await config.hooks.beforeList(context);
        }

        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'read', config.name);
        }

        // Parse query parameters
        const query = this.parseListQuery(context);

        // Get model
        const model = this.getModel(config.model);

        // Build query options
        const queryOptions: any = {
          where: query.filters,
          orderBy: query.sort,
          limit: query.limit,
          offset: query.offset
        };

        // Include relations if specified
        if (query.include) {
          queryOptions.include = query.include;
        }

        // Execute query
        let result;
        if (this.config.pagination?.enabled) {
          result = await model.paginate(query.page, query.limit, queryOptions);
        } else {
          const data = await model.find(queryOptions);
          result = { data, total: data.length };
        }

        // Run after hooks
        if (config.hooks?.afterList) {
          result = await config.hooks.afterList(context, result);
        }

        // Send response
        context.response.body = {
          success: true,
          data: result.data,
          meta: {
            total: result.total,
            page: query.page,
            limit: query.limit,
            totalPages: Math.ceil(result.total / query.limit)
          }
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // ➕ Create create handler
  private createCreateHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Run before hooks
        if (config.hooks?.beforeCreate) {
          await config.hooks.beforeCreate(context);
        }

        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'create', config.name);
        }

        // Validate input
        if (config.validation?.create) {
          await this.validationManager.validate(
            context.request.body, 
            config.validation.create
          );
        }

        // Get model
        const model = this.getModel(config.model);

        // Create record
        const created = await model.create(context.request.body);

        // Run after hooks
        if (config.hooks?.afterCreate) {
          await config.hooks.afterCreate(context, created);
        }

        // Send response
        context.response.statusCode = 201;
        context.response.body = {
          success: true,
          data: created,
          message: `${config.name} created successfully`
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🔍 Create read handler
  private createReadHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Run before hooks
        if (config.hooks?.beforeRead) {
          await config.hooks.beforeRead(context);
        }

        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'read', config.name);
        }

        const id = context.request.params.id;
        const query = this.parseReadQuery(context);

        // Get model
        const model = this.getModel(config.model);

        // Find record
        let record = await model.findById(id);

        if (!record) {
          context.response.statusCode = 404;
          context.response.body = {
            success: false,
            error: 'Not Found',
            message: `${config.name} not found`
          };
          return;
        }

        // Load relations if specified
        if (query.include) {
          record = await model.loadRelations([record], query.include);
          record = record[0];
        }

        // Run after hooks
        if (config.hooks?.afterRead) {
          record = await config.hooks.afterRead(context, record);
        }

        // Send response
        context.response.body = {
          success: true,
          data: record
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🔄 Create update handler
  private createUpdateHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Run before hooks
        if (config.hooks?.beforeUpdate) {
          await config.hooks.beforeUpdate(context);
        }

        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'update', config.name);
        }

        const id = context.request.params.id;

        // Validate input
        if (config.validation?.update) {
          await this.validationManager.validate(
            context.request.body, 
            config.validation.update
          );
        }

        // Get model
        const model = this.getModel(config.model);

        // Check if record exists
        const existing = await model.findById(id);
        if (!existing) {
          context.response.statusCode = 404;
          context.response.body = {
            success: false,
            error: 'Not Found',
            message: `${config.name} not found`
          };
          return;
        }

        // Update record
        const updated = await model.updateById(id, context.request.body);

        // Run after hooks
        if (config.hooks?.afterUpdate) {
          await config.hooks.afterUpdate(context, updated);
        }

        // Send response
        context.response.body = {
          success: true,
          data: updated,
          message: `${config.name} updated successfully`
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🗑️ Create delete handler
  private createDeleteHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Run before hooks
        if (config.hooks?.beforeDelete) {
          await config.hooks.beforeDelete(context);
        }

        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'delete', config.name);
        }

        const id = context.request.params.id;

        // Get model
        const model = this.getModel(config.model);

        // Check if record exists
        const existing = await model.findById(id);
        if (!existing) {
          context.response.statusCode = 404;
          context.response.body = {
            success: false,
            error: 'Not Found',
            message: `${config.name} not found`
          };
          return;
        }

        // Delete record
        const deleted = await model.deleteById(id);

        // Run after hooks
        if (config.hooks?.afterDelete) {
          await config.hooks.afterDelete(context, existing);
        }

        // Send response
        context.response.statusCode = 204;
        context.response.body = null;

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 📦 Create bulk handler
  private createBulkHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'create', config.name);
        }

        const items = context.request.body.items || [];

        if (!Array.isArray(items)) {
          context.response.statusCode = 400;
          context.response.body = {
            success: false,
            error: 'Bad Request',
            message: 'Items must be an array'
          };
          return;
        }

        // Validate each item
        if (config.validation?.create) {
          for (const item of items) {
            await this.validationManager.validate(item, config.validation.create);
          }
        }

        // Get model
        const model = this.getModel(config.model);

        // Create records
        const created = await model.createMany(items);

        // Send response
        context.response.statusCode = 201;
        context.response.body = {
          success: true,
          data: created,
          message: `${created.length} ${config.name}(s) created successfully`
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🗑️ Create bulk delete handler
  private createBulkDeleteHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'delete', config.name);
        }

        const ids = context.request.body.ids || [];

        if (!Array.isArray(ids)) {
          context.response.statusCode = 400;
          context.response.body = {
            success: false,
            error: 'Bad Request',
            message: 'IDs must be an array'
          };
          return;
        }

        // Get model
        const model = this.getModel(config.model);

        // Delete records
        let deletedCount = 0;
        for (const id of ids) {
          const deleted = await model.deleteById(id);
          if (deleted) deletedCount++;
        }

        // Send response
        context.response.body = {
          success: true,
          message: `${deletedCount} ${config.name}(s) deleted successfully`,
          deletedCount
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🔍 Create search handler
  private createSearchHandler<T>(config: ResourceConfig<T>) {
    return async (context: Context) => {
      try {
        // Check authentication
        if (config.auth?.enabled) {
          await this.authManager.authenticate(context);
          await this.authManager.authorize(context, 'read', config.name);
        }

        const query = context.request.query.q as string;
        const fields = (context.request.query.fields as string)?.split(',') || [];

        if (!query) {
          context.response.statusCode = 400;
          context.response.body = {
            success: false,
            error: 'Bad Request',
            message: 'Search query is required'
          };
          return;
        }

        // Get model
        const model = this.getModel(config.model);

        // Build search conditions
        const searchConditions: any = {};
        if (fields.length > 0) {
          searchConditions.$or = fields.map(field => ({
            [field]: { $like: `%${query}%` }
          }));
        }

        // Execute search
        const results = await model.find({ where: searchConditions });

        // Send response
        context.response.body = {
          success: true,
          data: results,
          query,
          total: results.length
        };

      } catch (error) {
        this.handleError(context, error);
      }
    };
  }

  // 🔧 Parse list query parameters
  private parseListQuery(context: Context) {
    const query = context.request.query;
    
    return {
      page: parseInt(query.page as string) || 1,
      limit: Math.min(
        parseInt(query.limit as string) || this.config.pagination?.defaultLimit || 20,
        this.config.pagination?.maxLimit || 100
      ),
      offset: ((parseInt(query.page as string) || 1) - 1) * 
               (parseInt(query.limit as string) || this.config.pagination?.defaultLimit || 20),
      sort: query.sort as string || 'id',
      filters: this.parseFilters(query.filter as string),
      include: (query.include as string)?.split(',') || []
    };
  }

  // 🔧 Parse read query parameters
  private parseReadQuery(context: Context) {
    const query = context.request.query;
    
    return {
      include: (query.include as string)?.split(',') || []
    };
  }

  // 🔧 Parse filters
  private parseFilters(filterString?: string): any {
    if (!filterString) return {};
    
    try {
      return JSON.parse(filterString);
    } catch {
      return {};
    }
  }

  // 🔧 Get model instance
  private getModel(modelName: string): any {
    // This would integrate with the ORM system
    // For now, return a mock model
    return {
      find: async (options: any) => [],
      findById: async (id: any) => null,
      create: async (data: any) => data,
      createMany: async (data: any[]) => data,
      updateById: async (id: any, data: any) => data,
      deleteById: async (id: any) => true,
      paginate: async (page: number, limit: number, options: any) => ({
        data: [],
        total: 0,
        page,
        limit
      }),
      loadRelations: async (records: any[], relations: string[]) => records
    };
  }

  // ❌ Handle errors
  private handleError(context: Context, error: any): void {
    this.logger.error('API Error', error);

    if (error.name === 'ValidationError') {
      context.response.statusCode = 400;
      context.response.body = {
        success: false,
        error: 'Validation Error',
        message: error.message,
        details: error.details
      };
    } else if (error.name === 'AuthenticationError') {
      context.response.statusCode = 401;
      context.response.body = {
        success: false,
        error: 'Authentication Required',
        message: error.message
      };
    } else if (error.name === 'AuthorizationError') {
      context.response.statusCode = 403;
      context.response.body = {
        success: false,
        error: 'Access Denied',
        message: error.message
      };
    } else {
      context.response.statusCode = 500;
      context.response.body = {
        success: false,
        error: 'Internal Server Error',
        message: 'Something went wrong'
      };
    }
  }

  // 📊 Get API documentation
  getDocumentation(): any {
    const docs: any = {
      openapi: '3.0.0',
      info: {
        title: 'Kilat.js API',
        version: this.config.version || '1.0.0',
        description: 'Auto-generated REST API'
      },
      servers: [
        {
          url: `${this.config.prefix}/${this.config.version}`,
          description: 'API Server'
        }
      ],
      paths: {}
    };

    // Generate documentation for each resource
    for (const [name, config] of this.resources) {
      const basePath = config.path;
      
      docs.paths[basePath] = {};
      docs.paths[`${basePath}/{id}`] = {};

      // Add operation documentation
      if (config.operations.includes('list')) {
        docs.paths[basePath].get = this.generateOperationDoc('list', config);
      }
      
      if (config.operations.includes('create')) {
        docs.paths[basePath].post = this.generateOperationDoc('create', config);
      }
      
      if (config.operations.includes('read')) {
        docs.paths[`${basePath}/{id}`].get = this.generateOperationDoc('read', config);
      }
      
      if (config.operations.includes('update')) {
        docs.paths[`${basePath}/{id}`].put = this.generateOperationDoc('update', config);
      }
      
      if (config.operations.includes('delete')) {
        docs.paths[`${basePath}/{id}`].delete = this.generateOperationDoc('delete', config);
      }
    }

    return docs;
  }

  // 📋 Generate operation documentation
  private generateOperationDoc(operation: string, config: ResourceConfig): any {
    const operationDocs: any = {
      tags: [config.name],
      summary: `${operation} ${config.name}`,
      responses: {
        200: { description: 'Success' },
        400: { description: 'Bad Request' },
        401: { description: 'Unauthorized' },
        403: { description: 'Forbidden' },
        404: { description: 'Not Found' },
        500: { description: 'Internal Server Error' }
      }
    };

    // Add parameters and request body based on operation
    switch (operation) {
      case 'list':
        operationDocs.parameters = [
          { name: 'page', in: 'query', schema: { type: 'integer' } },
          { name: 'limit', in: 'query', schema: { type: 'integer' } },
          { name: 'sort', in: 'query', schema: { type: 'string' } },
          { name: 'filter', in: 'query', schema: { type: 'string' } },
          { name: 'include', in: 'query', schema: { type: 'string' } }
        ];
        break;
      
      case 'read':
        operationDocs.parameters = [
          { name: 'id', in: 'path', required: true, schema: { type: 'string' } },
          { name: 'include', in: 'query', schema: { type: 'string' } }
        ];
        break;
      
      case 'create':
      case 'update':
        operationDocs.requestBody = {
          required: true,
          content: {
            'application/json': {
              schema: { type: 'object' }
            }
          }
        };
        break;
      
      case 'delete':
        operationDocs.parameters = [
          { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
        ];
        break;
    }

    return operationDocs;
  }
}

// Placeholder classes for dependencies
class ValidationManager {
  async validate(data: any, schema: ValidationSchema): Promise<void> {
    // Validation implementation
  }
}

class AuthManager {
  constructor(private config: any) {}
  
  async authenticate(context: Context): Promise<void> {
    // Authentication implementation
  }
  
  async authorize(context: Context, action: string, resource: string): Promise<void> {
    // Authorization implementation
  }
}
