import { promises as fs } from 'fs';
import { join, extname, relative } from 'path';
import { createLogger } from 'kilat-utils';
import type { Route, RouteHandler, Context } from '../types';
import { Router } from './Router';

/**
 * 📁 File-based Router for Kilat Backend
 * Automatically generates routes from file system structure
 */
export class FileBasedRouter {
  private router: Router;
  private logger = createLogger({ prefix: 'FileBasedRouter' });
  private routesDirectory: string;
  private watchMode = false;

  constructor(routesDirectory: string = './api', router?: Router) {
    this.routesDirectory = routesDirectory;
    this.router = router || new Router();
  }

  // 🚀 Initialize file-based routing
  async initialize(): Promise<Router> {
    try {
      await this.loadRoutes();
      this.logger.success(`File-based routes loaded from: ${this.routesDirectory}`);
      
      if (this.watchMode) {
        this.setupFileWatcher();
      }
      
      return this.router;
    } catch (error) {
      this.logger.error('Failed to initialize file-based router:', error);
      throw error;
    }
  }

  // 📁 Load routes from file system
  private async loadRoutes(): Promise<void> {
    try {
      const files = await this.scanDirectory(this.routesDirectory);
      
      for (const file of files) {
        await this.loadRouteFile(file);
      }
      
      this.logger.info(`Loaded ${files.length} route files`);
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        this.logger.warn(`Routes directory not found: ${this.routesDirectory}`);
        return;
      }
      throw error;
    }
  }

  // 🔍 Scan directory for route files
  private async scanDirectory(dir: string): Promise<string[]> {
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        
        if (entry.isDirectory()) {
          // Recursively scan subdirectories
          const subFiles = await this.scanDirectory(fullPath);
          files.push(...subFiles);
        } else if (this.isRouteFile(entry.name)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to scan directory ${dir}:`, error);
    }
    
    return files;
  }

  // 📄 Check if file is a valid route file
  private isRouteFile(filename: string): boolean {
    const validExtensions = ['.js', '.ts', '.mjs'];
    const ext = extname(filename);
    return validExtensions.includes(ext) && !filename.startsWith('_');
  }

  // 📦 Load individual route file
  private async loadRouteFile(filePath: string): Promise<void> {
    try {
      // Convert file path to route path
      const routePath = this.filePathToRoutePath(filePath);
      
      // Dynamic import the route module
      const module = await import(filePath);
      
      // Register HTTP method handlers
      const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
      
      for (const method of httpMethods) {
        const handler = module[method] || module[method.toLowerCase()];
        
        if (typeof handler === 'function') {
          this.router.register(method, routePath, handler, {
            middleware: module.middleware || [],
            meta: module.meta || {},
            cache: module.cache,
            rateLimit: module.rateLimit,
            validation: module.validation,
            auth: module.auth
          });
          
          this.logger.debug(`Route registered: ${method} ${routePath} -> ${filePath}`);
        }
      }
      
      // Register default export as GET handler if no specific methods found
      if (module.default && typeof module.default === 'function') {
        const hasSpecificMethods = httpMethods.some(method => 
          module[method] || module[method.toLowerCase()]
        );
        
        if (!hasSpecificMethods) {
          this.router.register('GET', routePath, module.default, {
            middleware: module.middleware || [],
            meta: module.meta || {}
          });
          
          this.logger.debug(`Default route registered: GET ${routePath} -> ${filePath}`);
        }
      }
      
    } catch (error) {
      this.logger.error(`Failed to load route file ${filePath}:`, error);
    }
  }

  // 🛤️ Convert file path to route path
  private filePathToRoutePath(filePath: string): string {
    // Get relative path from routes directory
    let routePath = relative(this.routesDirectory, filePath);
    
    // Remove file extension
    routePath = routePath.replace(/\.[^.]+$/, '');
    
    // Convert to URL path
    routePath = routePath.replace(/\\/g, '/'); // Windows path separator
    
    // Handle index files
    if (routePath.endsWith('/index') || routePath === 'index') {
      routePath = routePath.replace(/\/index$/, '') || '/';
    }
    
    // Handle dynamic routes [param] -> :param
    routePath = routePath.replace(/\[([^\]]+)\]/g, ':$1');
    
    // Handle catch-all routes [...param] -> :param*
    routePath = routePath.replace(/\[\.\.\.([^\]]+)\]/g, ':$1*');
    
    // Ensure leading slash
    if (!routePath.startsWith('/')) {
      routePath = '/' + routePath;
    }
    
    return routePath;
  }

  // 👀 Setup file watcher for hot reloading
  private setupFileWatcher(): void {
    // Note: In a real implementation, you'd use fs.watch or chokidar
    this.logger.info('File watching enabled for hot reloading');
    
    // Simplified file watching (in production, use proper file watcher)
    setInterval(async () => {
      try {
        await this.reloadRoutes();
      } catch (error) {
        this.logger.error('Failed to reload routes:', error);
      }
    }, 5000); // Check every 5 seconds
  }

  // 🔄 Reload routes
  private async reloadRoutes(): Promise<void> {
    this.router.clear();
    await this.loadRoutes();
    this.logger.debug('Routes reloaded');
  }

  // ⚙️ Enable watch mode
  enableWatchMode(): void {
    this.watchMode = true;
    if (this.router) {
      this.setupFileWatcher();
    }
  }

  // ⚙️ Disable watch mode
  disableWatchMode(): void {
    this.watchMode = false;
  }

  // 📊 Get router statistics
  getStats() {
    return this.router.getStats();
  }

  // 📋 List all routes
  listRoutes() {
    return this.router.listRoutes();
  }

  // 🎯 Get the underlying router
  getRouter(): Router {
    return this.router;
  }
}

// 🏭 Route file template generator
export class RouteGenerator {
  private logger = createLogger({ prefix: 'RouteGenerator' });

  // 📝 Generate route file
  async generateRoute(
    path: string, 
    methods: string[] = ['GET'], 
    options: {
      middleware?: boolean;
      validation?: boolean;
      auth?: boolean;
      typescript?: boolean;
    } = {}
  ): Promise<void> {
    const { middleware = false, validation = false, auth = false, typescript = true } = options;
    const ext = typescript ? '.ts' : '.js';
    const filePath = `${path}${ext}`;

    const template = this.generateTemplate(methods, {
      middleware,
      validation,
      auth,
      typescript
    });

    try {
      await fs.writeFile(filePath, template, 'utf8');
      this.logger.success(`Route file generated: ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to generate route file ${filePath}:`, error);
      throw error;
    }
  }

  // 📄 Generate route template
  private generateTemplate(
    methods: string[], 
    options: {
      middleware?: boolean;
      validation?: boolean;
      auth?: boolean;
      typescript?: boolean;
    }
  ): string {
    const { middleware, validation, auth, typescript } = options;
    const typeAnnotations = typescript ? ': Context' : '';
    const importType = typescript ? 'import type { Context } from \'kilat-backend\';' : '';

    let template = `${importType ? importType + '\n' : ''}
// 🎯 Route handlers
${methods.map(method => `
export async function ${method}(context${typeAnnotations}) {
  const { req, res, params, query, body } = context;
  
  // TODO: Implement ${method} handler
  res.statusCode = 200;
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify({
    message: '${method} handler not implemented',
    method: '${method}',
    params,
    query,
    body
  }));
}
`).join('')}`;

    if (middleware) {
      template += `
// 🔧 Route middleware
export const middleware = [
  // Add your middleware here
];
`;
    }

    if (validation) {
      template += `
// ✅ Request validation
export const validation = {
  body: {
    // Add body validation schema
  },
  query: {
    // Add query validation schema
  },
  params: {
    // Add params validation schema
  }
};
`;
    }

    if (auth) {
      template += `
// 🔐 Authentication requirements
export const auth = {
  required: true,
  roles: ['user'] // Specify required roles
};
`;
    }

    template += `
// 📋 Route metadata
export const meta = {
  title: 'API Route',
  description: 'Generated API route',
  tags: ['api'],
  version: '1.0.0'
};
`;

    return template;
  }
}
