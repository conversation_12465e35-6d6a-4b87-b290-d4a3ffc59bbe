import { createServer, IncomingMessage, ServerResponse } from 'http';
import { createServer as createHttpsServer } from 'https';
import { parse } from 'url';
import { createLogger } from 'kilat-utils';
import type { 
  KilatServerConfig, 
  Route, 
  Middleware, 
  Context, 
  Plugin,
  ServerStats,
  HealthCheck
} from '../types';
import { Router } from './Router';
import { MiddlewareManager } from './MiddlewareManager';
import { PluginManager } from './PluginManager';
import { SecurityManager } from './SecurityManager';
import { CacheManager } from './CacheManager';

/**
 * 🚀 KilatServer - High-performance Node.js server
 * Built for speed, security, and developer experience
 */
export class KilatServer {
  private config: KilatServerConfig;
  private server?: any;
  private router: Router;
  private middlewareManager: MiddlewareManager;
  private pluginManager: PluginManager;
  private securityManager: SecurityManager;
  private cacheManager: CacheManager;
  private logger = createLogger({ prefix: 'KilatServer' });
  private isRunning = false;
  private stats: ServerStats = {
    startTime: new Date(),
    requests: 0,
    errors: 0,
    averageResponseTime: 0,
    activeConnections: 0,
    totalConnections: 0
  };

  constructor(config: KilatServerConfig) {
    this.config = {
      port: 3000,
      host: '0.0.0.0',
      https: false,
      cors: { enabled: true },
      compression: { enabled: true },
      rateLimit: { enabled: true, max: 100, windowMs: 60000 },
      security: { helmet: true, csrf: false },
      cache: { enabled: true, ttl: 300 },
      clustering: { enabled: false, workers: 'auto' },
      monitoring: { enabled: true, metrics: true },
      ...config
    };

    this.router = new Router();
    this.middlewareManager = new MiddlewareManager();
    this.pluginManager = new PluginManager();
    this.securityManager = new SecurityManager(this.config.security);
    this.cacheManager = new CacheManager(this.config.cache);

    this.setupDefaultMiddleware();
  }

  // 🚀 Start the server
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Server is already running');
      return;
    }

    try {
      // Initialize plugins
      await this.pluginManager.initialize();

      // Create HTTP/HTTPS server
      this.server = this.config.https 
        ? createHttpsServer(this.config.https, this.handleRequest.bind(this))
        : createServer(this.handleRequest.bind(this));

      // Setup server event handlers
      this.setupServerEvents();

      // Start listening
      await new Promise<void>((resolve, reject) => {
        this.server.listen(this.config.port, this.config.host, (error?: Error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            this.stats.startTime = new Date();
            this.logger.success(`🚀 Server running at ${this.getServerUrl()}`);
            resolve();
          }
        });
      });

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  // 🛑 Stop the server
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      this.logger.info('Stopping server...');

      // Close server
      await new Promise<void>((resolve) => {
        this.server.close(() => {
          this.isRunning = false;
          this.logger.info('Server stopped');
          resolve();
        });
      });

      // Cleanup plugins
      await this.pluginManager.cleanup();

    } catch (error) {
      this.logger.error('Error stopping server', error);
      throw error;
    }
  }

  // 🔧 Handle incoming requests
  private async handleRequest(req: IncomingMessage, res: ServerResponse): Promise<void> {
    const startTime = Date.now();
    this.stats.requests++;
    this.stats.activeConnections++;

    try {
      // Parse URL
      const parsedUrl = parse(req.url || '/', true);
      
      // Create context
      const context: Context = {
        request: {
          method: req.method || 'GET',
          url: req.url || '/',
          path: parsedUrl.pathname || '/',
          query: parsedUrl.query,
          headers: req.headers,
          body: null,
          params: {},
          ip: this.getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          timestamp: new Date()
        },
        response: {
          statusCode: 200,
          headers: {},
          body: null,
          sent: false
        },
        state: {},
        server: this,
        startTime
      };

      // Parse request body if needed
      if (['POST', 'PUT', 'PATCH'].includes(context.request.method)) {
        context.request.body = await this.parseRequestBody(req);
      }

      // Run middleware pipeline
      await this.middlewareManager.run(context);

      // Route the request
      const route = this.router.match(context.request.method, context.request.path);
      
      if (route) {
        // Set route params
        context.request.params = route.params;
        
        // Execute route handler
        await route.handler(context);
      } else {
        // 404 Not Found
        context.response.statusCode = 404;
        context.response.body = { error: 'Not Found', path: context.request.path };
      }

      // Send response if not already sent
      if (!context.response.sent) {
        this.sendResponse(res, context);
      }

    } catch (error) {
      this.stats.errors++;
      this.logger.error('Request handling error', error);
      
      // Send error response
      if (!res.headersSent) {
        res.statusCode = 500;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({ 
          error: 'Internal Server Error',
          message: this.config.debug ? error.message : 'Something went wrong'
        }));
      }
    } finally {
      // Update stats
      const responseTime = Date.now() - startTime;
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime + responseTime) / 2;
      this.stats.activeConnections--;
    }
  }

  // 📤 Send response
  private sendResponse(res: ServerResponse, context: Context): void {
    const { response } = context;
    
    // Set status code
    res.statusCode = response.statusCode;
    
    // Set headers
    Object.entries(response.headers).forEach(([key, value]) => {
      res.setHeader(key, value as string);
    });
    
    // Set default content type if not set
    if (!res.getHeader('Content-Type')) {
      res.setHeader('Content-Type', 'application/json');
    }
    
    // Send body
    if (response.body !== null) {
      const body = typeof response.body === 'string' 
        ? response.body 
        : JSON.stringify(response.body);
      res.end(body);
    } else {
      res.end();
    }
    
    response.sent = true;
  }

  // 📥 Parse request body
  private async parseRequestBody(req: IncomingMessage): Promise<any> {
    return new Promise((resolve, reject) => {
      let body = '';
      
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        try {
          const contentType = req.headers['content-type'] || '';
          
          if (contentType.includes('application/json')) {
            resolve(body ? JSON.parse(body) : {});
          } else if (contentType.includes('application/x-www-form-urlencoded')) {
            const params = new URLSearchParams(body);
            const result: any = {};
            for (const [key, value] of params) {
              result[key] = value;
            }
            resolve(result);
          } else {
            resolve(body);
          }
        } catch (error) {
          reject(error);
        }
      });
      
      req.on('error', reject);
    });
  }

  // 🌐 Get client IP
  private getClientIP(req: IncomingMessage): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const realIP = req.headers['x-real-ip'] as string;
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    return req.socket.remoteAddress || 'unknown';
  }

  // 🔧 Setup default middleware
  private setupDefaultMiddleware(): void {
    // Security middleware
    if (this.config.security?.helmet) {
      this.middlewareManager.use(this.securityManager.helmet());
    }

    // CORS middleware
    if (this.config.cors?.enabled) {
      this.middlewareManager.use(this.createCORSMiddleware());
    }

    // Compression middleware
    if (this.config.compression?.enabled) {
      this.middlewareManager.use(this.createCompressionMiddleware());
    }

    // Rate limiting middleware
    if (this.config.rateLimit?.enabled) {
      this.middlewareManager.use(this.createRateLimitMiddleware());
    }

    // Caching middleware
    if (this.config.cache?.enabled) {
      this.middlewareManager.use(this.cacheManager.middleware());
    }

    // Request logging middleware
    if (this.config.monitoring?.enabled) {
      this.middlewareManager.use(this.createLoggingMiddleware());
    }
  }

  // 🌐 Create CORS middleware
  private createCORSMiddleware(): Middleware {
    return async (context, next) => {
      const { cors } = this.config;
      
      if (cors?.origin) {
        context.response.headers['Access-Control-Allow-Origin'] = cors.origin;
      } else {
        context.response.headers['Access-Control-Allow-Origin'] = '*';
      }
      
      if (cors?.methods) {
        context.response.headers['Access-Control-Allow-Methods'] = cors.methods.join(', ');
      }
      
      if (cors?.headers) {
        context.response.headers['Access-Control-Allow-Headers'] = cors.headers.join(', ');
      }
      
      if (cors?.credentials) {
        context.response.headers['Access-Control-Allow-Credentials'] = 'true';
      }
      
      // Handle preflight requests
      if (context.request.method === 'OPTIONS') {
        context.response.statusCode = 204;
        return;
      }
      
      await next();
    };
  }

  // 🗜️ Create compression middleware
  private createCompressionMiddleware(): Middleware {
    return async (context, next) => {
      await next();
      
      // Simple compression logic (in production, use a proper compression library)
      const acceptEncoding = context.request.headers['accept-encoding'] as string || '';
      
      if (acceptEncoding.includes('gzip') && context.response.body) {
        const zlib = require('zlib');
        const compressed = zlib.gzipSync(JSON.stringify(context.response.body));
        context.response.headers['Content-Encoding'] = 'gzip';
        context.response.body = compressed;
      }
    };
  }

  // 🚦 Create rate limit middleware
  private createRateLimitMiddleware(): Middleware {
    const requests = new Map<string, { count: number; resetTime: number }>();
    
    return async (context, next) => {
      const { rateLimit } = this.config;
      const clientIP = context.request.ip;
      const now = Date.now();
      const windowMs = rateLimit?.windowMs || 60000;
      const maxRequests = rateLimit?.max || 100;
      
      const clientData = requests.get(clientIP);
      
      if (!clientData || now > clientData.resetTime) {
        requests.set(clientIP, { count: 1, resetTime: now + windowMs });
      } else {
        clientData.count++;
        
        if (clientData.count > maxRequests) {
          context.response.statusCode = 429;
          context.response.body = { error: 'Too Many Requests' };
          return;
        }
      }
      
      await next();
    };
  }

  // 📝 Create logging middleware
  private createLoggingMiddleware(): Middleware {
    return async (context, next) => {
      const startTime = Date.now();
      
      await next();
      
      const duration = Date.now() - startTime;
      const { request, response } = context;
      
      this.logger.info(`${request.method} ${request.path} ${response.statusCode} ${duration}ms`, {
        ip: request.ip,
        userAgent: request.userAgent,
        responseTime: duration
      });
    };
  }

  // 🔧 Setup server events
  private setupServerEvents(): void {
    this.server.on('connection', () => {
      this.stats.totalConnections++;
    });

    this.server.on('error', (error: Error) => {
      this.logger.error('Server error', error);
    });

    this.server.on('clientError', (error: Error) => {
      this.logger.warn('Client error', error);
    });
  }

  // 🔧 Setup graceful shutdown
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      this.logger.info(`Received ${signal}, shutting down gracefully...`);
      await this.stop();
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }

  // 🌐 Get server URL
  private getServerUrl(): string {
    const protocol = this.config.https ? 'https' : 'http';
    return `${protocol}://${this.config.host}:${this.config.port}`;
  }

  // 🛤️ Route registration methods
  get(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('GET', path, handler);
  }

  post(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('POST', path, handler);
  }

  put(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('PUT', path, handler);
  }

  patch(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('PATCH', path, handler);
  }

  delete(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('DELETE', path, handler);
  }

  options(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('OPTIONS', path, handler);
  }

  head(path: string, handler: (context: Context) => Promise<void> | void): void {
    this.router.add('HEAD', path, handler);
  }

  // 🔌 Middleware and plugin management
  use(middleware: Middleware): void {
    this.middlewareManager.use(middleware);
  }

  plugin(plugin: Plugin): void {
    this.pluginManager.register(plugin);
  }

  // 📊 Health check endpoint
  async healthCheck(): Promise<HealthCheck> {
    return {
      status: 'healthy',
      timestamp: new Date(),
      uptime: Date.now() - this.stats.startTime.getTime(),
      stats: this.stats,
      memory: process.memoryUsage(),
      cpu: process.cpuUsage()
    };
  }

  // 📊 Get server statistics
  getStats(): ServerStats {
    return { ...this.stats };
  }

  // 🔧 Get configuration
  getConfig(): KilatServerConfig {
    return { ...this.config };
  }

  // 🔍 Check if server is running
  isServerRunning(): boolean {
    return this.isRunning;
  }
}
