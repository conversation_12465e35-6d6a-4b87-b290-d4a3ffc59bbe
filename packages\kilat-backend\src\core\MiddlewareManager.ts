import { createLogger } from 'kilat-utils';
import type { Middleware, Context, MiddlewareFunction } from '../types';

/**
 * 🔧 Middleware Manager for <PERSON><PERSON> Backend
 * Handles middleware registration, execution, and error handling
 */
export class MiddlewareManager {
  private globalMiddleware: Middleware[] = [];
  private routeMiddleware: Map<string, Middleware[]> = new Map();
  private logger = createLogger({ prefix: 'MiddlewareManager' });

  // 📝 Register global middleware
  use(middleware: Middleware): void {
    this.globalMiddleware.push(middleware);
    this.logger.debug(`Global middleware registered: ${middleware.name || 'anonymous'}`);
  }

  // 📝 Register route-specific middleware
  useForRoute(routeId: string, middleware: Middleware): void {
    if (!this.routeMiddleware.has(routeId)) {
      this.routeMiddleware.set(routeId, []);
    }
    this.routeMiddleware.get(routeId)!.push(middleware);
    this.logger.debug(`Route middleware registered for ${routeId}: ${middleware.name || 'anonymous'}`);
  }

  // 🚀 Execute middleware chain
  async execute(context: Context, routeMiddleware: Middleware[] = []): Promise<void> {
    const allMiddleware = [
      ...this.globalMiddleware,
      ...routeMiddleware
    ];

    let index = 0;

    const next = async (): Promise<void> => {
      if (index >= allMiddleware.length) {
        return;
      }

      const middleware = allMiddleware[index++];
      
      try {
        await this.executeMiddleware(middleware, context, next);
      } catch (error) {
        this.logger.error(`Middleware error in ${middleware.name || 'anonymous'}:`, error);
        throw error;
      }
    };

    await next();
  }

  // 🔧 Execute single middleware
  private async executeMiddleware(
    middleware: Middleware, 
    context: Context, 
    next: () => Promise<void>
  ): Promise<void> {
    const startTime = Date.now();

    try {
      if (typeof middleware === 'function') {
        await middleware(context, next);
      } else if (middleware.handler) {
        await middleware.handler(context, next);
      } else {
        throw new Error('Invalid middleware format');
      }

      const duration = Date.now() - startTime;
      this.logger.debug(`Middleware executed: ${middleware.name || 'anonymous'} (${duration}ms)`);

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Middleware failed: ${middleware.name || 'anonymous'} (${duration}ms)`, error);
      throw error;
    }
  }

  // 📊 Get middleware statistics
  getStats(): {
    globalCount: number;
    routeCount: number;
    totalRoutes: number;
  } {
    return {
      globalCount: this.globalMiddleware.length,
      routeCount: Array.from(this.routeMiddleware.values()).reduce((sum, arr) => sum + arr.length, 0),
      totalRoutes: this.routeMiddleware.size
    };
  }

  // 📋 List all middleware
  listMiddleware(): {
    global: string[];
    routes: Record<string, string[]>;
  } {
    const global = this.globalMiddleware.map(m => m.name || 'anonymous');
    const routes: Record<string, string[]> = {};

    for (const [routeId, middleware] of this.routeMiddleware) {
      routes[routeId] = middleware.map(m => m.name || 'anonymous');
    }

    return { global, routes };
  }

  // 🧹 Clear middleware
  clear(): void {
    this.globalMiddleware = [];
    this.routeMiddleware.clear();
    this.logger.debug('All middleware cleared');
  }

  // 🔧 Create middleware from function
  static create(name: string, handler: MiddlewareFunction): Middleware {
    return {
      name,
      handler,
      enabled: true,
      priority: 0
    };
  }

  // 🎯 Built-in middleware creators
  static cors(options: {
    origin?: string | string[] | boolean;
    methods?: string[];
    allowedHeaders?: string[];
    credentials?: boolean;
  } = {}): Middleware {
    return {
      name: 'cors',
      handler: async (context: Context, next: () => Promise<void>) => {
        const { req, res } = context;
        const origin = req.headers.origin;

        // Set CORS headers
        if (options.origin === true || !options.origin) {
          res.setHeader('Access-Control-Allow-Origin', '*');
        } else if (typeof options.origin === 'string') {
          res.setHeader('Access-Control-Allow-Origin', options.origin);
        } else if (Array.isArray(options.origin) && origin) {
          if (options.origin.includes(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin);
          }
        }

        if (options.methods) {
          res.setHeader('Access-Control-Allow-Methods', options.methods.join(', '));
        } else {
          res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
        }

        if (options.allowedHeaders) {
          res.setHeader('Access-Control-Allow-Headers', options.allowedHeaders.join(', '));
        } else {
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        }

        if (options.credentials) {
          res.setHeader('Access-Control-Allow-Credentials', 'true');
        }

        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          res.statusCode = 204;
          res.end();
          return;
        }

        await next();
      },
      enabled: true,
      priority: -100 // High priority (execute early)
    };
  }

  static json(options: { limit?: string } = {}): Middleware {
    return {
      name: 'json',
      handler: async (context: Context, next: () => Promise<void>) => {
        const { req } = context;
        const contentType = req.headers['content-type'] || '';

        if (contentType.includes('application/json') && context.body) {
          try {
            if (typeof context.body === 'string') {
              context.body = JSON.parse(context.body);
            }
          } catch (error) {
            context.res.statusCode = 400;
            context.res.setHeader('Content-Type', 'application/json');
            context.res.end(JSON.stringify({
              error: 'Bad Request',
              message: 'Invalid JSON in request body'
            }));
            return;
          }
        }

        await next();
      },
      enabled: true,
      priority: -50
    };
  }

  static logger(): Middleware {
    const logger = createLogger({ prefix: 'HTTP' });

    return {
      name: 'logger',
      handler: async (context: Context, next: () => Promise<void>) => {
        const { req, res, startTime } = context;
        const start = startTime || Date.now();

        await next();

        const duration = Date.now() - start;
        const status = res.statusCode;
        const method = req.method;
        const url = req.url;

        const logLevel = status >= 500 ? 'error' : status >= 400 ? 'warn' : 'info';
        logger[logLevel](`${method} ${url} ${status} - ${duration}ms`);
      },
      enabled: true,
      priority: -200 // Very high priority (execute first)
    };
  }

  static rateLimit(options: {
    max?: number;
    windowMs?: number;
    message?: string;
    keyGenerator?: (context: Context) => string;
  } = {}): Middleware {
    const {
      max = 100,
      windowMs = 60000,
      message = 'Too many requests',
      keyGenerator = (context) => context.req.socket.remoteAddress || 'unknown'
    } = options;

    const requests = new Map<string, { count: number; resetTime: number }>();

    return {
      name: 'rateLimit',
      handler: async (context: Context, next: () => Promise<void>) => {
        const key = keyGenerator(context);
        const now = Date.now();
        const windowStart = now - windowMs;

        // Clean up old entries
        for (const [k, v] of requests) {
          if (v.resetTime < windowStart) {
            requests.delete(k);
          }
        }

        const current = requests.get(key);
        
        if (!current) {
          requests.set(key, { count: 1, resetTime: now + windowMs });
        } else if (current.resetTime < now) {
          requests.set(key, { count: 1, resetTime: now + windowMs });
        } else if (current.count >= max) {
          context.res.statusCode = 429;
          context.res.setHeader('Content-Type', 'application/json');
          context.res.setHeader('Retry-After', Math.ceil((current.resetTime - now) / 1000));
          context.res.end(JSON.stringify({
            error: 'Too Many Requests',
            message
          }));
          return;
        } else {
          current.count++;
        }

        await next();
      },
      enabled: true,
      priority: -75
    };
  }

  static compression(): Middleware {
    return {
      name: 'compression',
      handler: async (context: Context, next: () => Promise<void>) => {
        const { req, res } = context;
        const acceptEncoding = req.headers['accept-encoding'] || '';

        // Simple gzip support (in production, use a proper compression library)
        if (acceptEncoding.includes('gzip')) {
          res.setHeader('Content-Encoding', 'gzip');
        }

        await next();
      },
      enabled: true,
      priority: 50 // Low priority (execute late)
    };
  }
}
