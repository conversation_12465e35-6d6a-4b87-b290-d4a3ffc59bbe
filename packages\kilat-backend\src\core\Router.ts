import { IncomingMessage, ServerResponse } from 'http';
import { parse } from 'url';
import { createLogger } from 'kilat-utils';
import type { Route, Context, RouteHandler, RouteParams } from '../types';

/**
 * 🧭 High-performance Router for Kilat Backend
 * File-based routing with middleware support
 */
export class Router {
  private routes: Map<string, Map<string, Route>> = new Map();
  private logger = createLogger({ prefix: 'Router' });
  private paramRoutes: Array<{ pattern: RegExp; route: Route; keys: string[] }> = [];

  constructor() {
    // Initialize HTTP methods
    ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'].forEach(method => {
      this.routes.set(method, new Map());
    });
  }

  // 📝 Register a route
  register(method: string, path: string, handler: RouteHandler, options: Partial<Route> = {}): void {
    const route: Route = {
      method: method.toUpperCase(),
      path,
      handler,
      middleware: options.middleware || [],
      meta: options.meta || {},
      cache: options.cache,
      rateLimit: options.rateLimit,
      validation: options.validation,
      auth: options.auth
    };

    const methodRoutes = this.routes.get(method.toUpperCase());
    if (!methodRoutes) {
      throw new Error(`Unsupported HTTP method: ${method}`);
    }

    // Check if route has parameters
    if (path.includes(':')) {
      this.registerParameterRoute(route);
    } else {
      methodRoutes.set(path, route);
    }

    this.logger.debug(`Route registered: ${method.toUpperCase()} ${path}`);
  }

  // 📝 Register route with parameters
  private registerParameterRoute(route: Route): void {
    const keys: string[] = [];
    const pattern = route.path.replace(/:([^/]+)/g, (match, key) => {
      keys.push(key);
      return '([^/]+)';
    });

    const regex = new RegExp(`^${pattern}$`);
    this.paramRoutes.push({ pattern: regex, route, keys });
  }

  // 🔍 Find matching route
  find(method: string, pathname: string): { route: Route; params: RouteParams } | null {
    const methodRoutes = this.routes.get(method.toUpperCase());
    if (!methodRoutes) {
      return null;
    }

    // Try exact match first
    const exactRoute = methodRoutes.get(pathname);
    if (exactRoute) {
      return { route: exactRoute, params: {} };
    }

    // Try parameter routes
    for (const { pattern, route, keys } of this.paramRoutes) {
      if (route.method !== method.toUpperCase()) continue;

      const match = pathname.match(pattern);
      if (match) {
        const params: RouteParams = {};
        keys.forEach((key, index) => {
          params[key] = match[index + 1];
        });
        return { route, params };
      }
    }

    return null;
  }

  // 🎯 Handle incoming request
  async handle(req: IncomingMessage, res: ServerResponse): Promise<void> {
    const startTime = Date.now();
    const url = parse(req.url || '', true);
    const method = req.method || 'GET';
    const pathname = url.pathname || '/';

    try {
      // Find matching route
      const match = this.find(method, pathname);
      if (!match) {
        this.sendNotFound(res, pathname);
        return;
      }

      const { route, params } = match;

      // Create context
      const context: Context = {
        req,
        res,
        method,
        url: req.url || '',
        pathname,
        params,
        query: url.query as Record<string, any>,
        headers: req.headers,
        body: null,
        user: null,
        meta: {},
        startTime
      };

      // Parse body for POST/PUT/PATCH requests
      if (['POST', 'PUT', 'PATCH'].includes(method)) {
        context.body = await this.parseBody(req);
      }

      // Execute route handler
      await route.handler(context);

    } catch (error) {
      this.logger.error('Route handling error:', error);
      this.sendError(res, error as Error);
    }
  }

  // 📦 Parse request body
  private async parseBody(req: IncomingMessage): Promise<any> {
    return new Promise((resolve, reject) => {
      let body = '';
      
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const contentType = req.headers['content-type'] || '';
          
          if (contentType.includes('application/json')) {
            resolve(JSON.parse(body));
          } else if (contentType.includes('application/x-www-form-urlencoded')) {
            const params = new URLSearchParams(body);
            const result: Record<string, any> = {};
            for (const [key, value] of params) {
              result[key] = value;
            }
            resolve(result);
          } else {
            resolve(body);
          }
        } catch (error) {
          reject(new Error('Invalid request body'));
        }
      });

      req.on('error', reject);
    });
  }

  // 🚫 Send 404 Not Found
  private sendNotFound(res: ServerResponse, pathname: string): void {
    res.statusCode = 404;
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({
      error: 'Not Found',
      message: `Route not found: ${pathname}`,
      statusCode: 404,
      timestamp: new Date().toISOString()
    }));
  }

  // 🚨 Send error response
  private sendError(res: ServerResponse, error: Error): void {
    res.statusCode = 500;
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({
      error: 'Internal Server Error',
      message: error.message,
      statusCode: 500,
      timestamp: new Date().toISOString()
    }));
  }

  // 📊 Get route statistics
  getStats(): { totalRoutes: number; routesByMethod: Record<string, number> } {
    let totalRoutes = 0;
    const routesByMethod: Record<string, number> = {};

    for (const [method, routes] of this.routes) {
      const count = routes.size;
      routesByMethod[method] = count;
      totalRoutes += count;
    }

    // Add parameter routes
    totalRoutes += this.paramRoutes.length;

    return { totalRoutes, routesByMethod };
  }

  // 📋 List all routes
  listRoutes(): Array<{ method: string; path: string; hasParams: boolean }> {
    const routes: Array<{ method: string; path: string; hasParams: boolean }> = [];

    // Static routes
    for (const [method, methodRoutes] of this.routes) {
      for (const [path] of methodRoutes) {
        routes.push({ method, path, hasParams: false });
      }
    }

    // Parameter routes
    for (const { route } of this.paramRoutes) {
      routes.push({ 
        method: route.method, 
        path: route.path, 
        hasParams: true 
      });
    }

    return routes.sort((a, b) => {
      if (a.method !== b.method) {
        return a.method.localeCompare(b.method);
      }
      return a.path.localeCompare(b.path);
    });
  }

  // 🧹 Clear all routes
  clear(): void {
    for (const routes of this.routes.values()) {
      routes.clear();
    }
    this.paramRoutes = [];
    this.logger.debug('All routes cleared');
  }

  // 🎯 Convenience methods for HTTP verbs
  get(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('GET', path, handler, options);
  }

  post(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('POST', path, handler, options);
  }

  put(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('PUT', path, handler, options);
  }

  delete(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('DELETE', path, handler, options);
  }

  patch(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('PATCH', path, handler, options);
  }

  head(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('HEAD', path, handler, options);
  }

  options(path: string, handler: RouteHandler, options?: Partial<Route>): void {
    this.register('OPTIONS', path, handler, options);
  }

  // 🔄 Route group with prefix
  group(prefix: string, callback: (router: Router) => void): void {
    const groupRouter = new Router();
    callback(groupRouter);

    // Copy routes with prefix
    for (const [method, routes] of groupRouter.routes) {
      for (const [path, route] of routes) {
        const prefixedPath = `${prefix}${path}`.replace(/\/+/g, '/');
        this.register(method, prefixedPath, route.handler, route);
      }
    }

    // Copy parameter routes with prefix
    for (const { route } of groupRouter.paramRoutes) {
      const prefixedPath = `${prefix}${route.path}`.replace(/\/+/g, '/');
      this.register(route.method, prefixedPath, route.handler, route);
    }
  }
}
