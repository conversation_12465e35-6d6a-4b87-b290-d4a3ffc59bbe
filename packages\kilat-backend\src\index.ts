// 🚀 Kilat Backend - Production Ready Internal API Server
// Complete backend solution with middleware, routing, WebSocket, and GraphQL

// Export main server class
export { KilatBackend } from './server';
export { KilatServer } from './core/KilatServer';
export { Application } from './core/Application';

// Export all types
export * from './types';

// Core Components
export { Router } from './core/Router';
export { Middleware } from './core/Middleware';
export { Context } from './core/Context';
export { Request } from './core/Request';
export { Response } from './core/Response';

// Routing System
export { RouteManager } from './routing/RouteManager';
export { RouteGroup } from './routing/RouteGroup';
export { RouteMiddleware } from './routing/RouteMiddleware';
export { ParameterValidator } from './routing/ParameterValidator';

// Middleware Collection
export * from './middleware/auth';
export * from './middleware/validation';
export * from './middleware/error';
export * from './middleware/cors';
export * from './middleware/helmet';
export * from './middleware/rateLimit';
export * from './middleware/compression';
export * from './middleware/logging';
export * from './middleware/cache';
export * from './middleware/session';

// API Features
export { createAPI } from './api/createAPI';
export { createRoute } from './api/createRoute';
export { APIController } from './api/APIController';
export { RESTController } from './api/RESTController';
export { GraphQLController } from './api/GraphQLController';

// WebSocket Support
export { WebSocketServer } from './websocket/WebSocketServer';
export { WebSocketManager } from './websocket/WebSocketManager';
export { SocketHandler } from './websocket/SocketHandler';
export { RoomManager } from './websocket/RoomManager';

// GraphQL Integration
export { GraphQLServer } from './graphql/GraphQLServer';
export { SchemaBuilder } from './graphql/SchemaBuilder';
export { ResolverManager } from './graphql/ResolverManager';

// File Handling
export { FileUpload } from './files/FileUpload';
export { FileManager } from './files/FileManager';
export { ImageProcessor } from './files/ImageProcessor';

// Security
export { SecurityManager } from './security/SecurityManager';
export { JWTManager } from './security/JWTManager';
export { PasswordManager } from './security/PasswordManager';

// Export utilities
export * from './utils/logger';
export { Validator } from './utils/Validator';
export { Cache } from './utils/Cache';
export { Queue } from './utils/Queue';
export { EventEmitter } from './utils/EventEmitter';

// Export convenience functions
import { KilatBackend } from './server';
import type { KilatBackendConfig } from './types';

/**
 * Create a new Kilat Backend server instance
 */
export function createKilatBackend(config?: Partial<KilatBackendConfig>): KilatBackend {
  return new KilatBackend(config);
}

/**
 * Quick start function for simple setups
 */
export async function startKilatBackend(config?: Partial<KilatBackendConfig>): Promise<KilatBackend> {
  const server = new KilatBackend(config);
  await server.start();
  return server;
}

// Default export
export default {
  KilatBackend,
  createKilatBackend,
  startKilatBackend
};

// Version info
export const KILAT_BACKEND_VERSION = '1.0.0';
