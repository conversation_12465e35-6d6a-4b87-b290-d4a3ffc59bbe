import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import type { 
  KilatRequest, 
  KilatResponse, 
  KilatMiddleware, 
  AuthUser, 
  AuthToken,
  LoginCredentials,
  RegisterData,
  ApiResponse 
} from '../types';

/**
 * Authentication Middleware for Kilat Backend
 * Handles JWT tokens, user sessions, and role-based access
 */

// 🔐 JWT Authentication Middleware
export const authMiddleware = (options: {
  required?: boolean;
  roles?: string[];
  permissions?: string[];
} = {}): KilatMiddleware => {
  return async (req: KilatRequest, res: KilatResponse, next) => {
    try {
      const token = extractToken(req);
      
      if (!token) {
        if (options.required) {
          return sendAuthError(res, 'Authentication token required');
        }
        return next();
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'kilat-secret-key') as AuthToken;
      
      // Attach user info to request
      req.user = {
        id: decoded.userId,
        email: decoded.email,
        roles: decoded.roles || [],
        permissions: [], // Would be loaded from database
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Check role requirements
      if (options.roles && options.roles.length > 0) {
        const hasRole = options.roles.some(role => req.user.roles.includes(role));
        if (!hasRole) {
          return sendAuthError(res, 'Insufficient permissions', 403);
        }
      }

      // Check permission requirements
      if (options.permissions && options.permissions.length > 0) {
        const hasPermission = options.permissions.some(permission => 
          req.user.permissions.includes(permission)
        );
        if (!hasPermission) {
          return sendAuthError(res, 'Insufficient permissions', 403);
        }
      }

      next();
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return sendAuthError(res, 'Invalid authentication token');
      }
      if (error instanceof jwt.TokenExpiredError) {
        return sendAuthError(res, 'Authentication token expired');
      }
      
      console.error('Auth middleware error:', error);
      return sendAuthError(res, 'Authentication failed');
    }
  };
};

// 🔑 Login Handler
export const loginHandler = async (req: KilatRequest, res: KilatResponse) => {
  try {
    const { email, password }: LoginCredentials = req.body;

    // Validate input
    if (!email || !password) {
      return sendAuthError(res, 'Email and password are required', 400);
    }

    // TODO: Replace with actual database lookup
    const user = await findUserByEmail(email);
    if (!user) {
      return sendAuthError(res, 'Invalid credentials', 401);
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      return sendAuthError(res, 'Invalid credentials', 401);
    }

    // Generate JWT token
    const token = generateToken(user);

    // Send response
    const response: ApiResponse<{ user: Partial<AuthUser>; token: string }> = {
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          roles: user.roles,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        token
      },
      message: 'Login successful',
      meta: {
        timestamp: new Date().toISOString(),
        requestId: req.kilat?.requestId || 'unknown',
        processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
        version: '1.0.0'
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Login error:', error);
    sendAuthError(res, 'Login failed');
  }
};

// 📝 Register Handler
export const registerHandler = async (req: KilatRequest, res: KilatResponse) => {
  try {
    const { email, password, username }: RegisterData = req.body;

    // Validate input
    if (!email || !password) {
      return sendAuthError(res, 'Email and password are required', 400);
    }

    if (password.length < 8) {
      return sendAuthError(res, 'Password must be at least 8 characters', 400);
    }

    // Check if user already exists
    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return sendAuthError(res, 'User already exists', 409);
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const newUser: AuthUser = {
      id: generateUserId(),
      email,
      username,
      roles: ['user'], // Default role
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // TODO: Save to database
    await saveUser({ ...newUser, passwordHash });

    // Generate token
    const token = generateToken(newUser);

    // Send response
    const response: ApiResponse<{ user: Partial<AuthUser>; token: string }> = {
      success: true,
      data: {
        user: {
          id: newUser.id,
          email: newUser.email,
          username: newUser.username,
          roles: newUser.roles,
          createdAt: newUser.createdAt,
          updatedAt: newUser.updatedAt
        },
        token
      },
      message: 'Registration successful',
      meta: {
        timestamp: new Date().toISOString(),
        requestId: req.kilat?.requestId || 'unknown',
        processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
        version: '1.0.0'
      }
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Registration error:', error);
    sendAuthError(res, 'Registration failed');
  }
};

// 👤 Profile Handler
export const profileHandler = async (req: KilatRequest, res: KilatResponse) => {
  try {
    if (!req.user) {
      return sendAuthError(res, 'Authentication required', 401);
    }

    // TODO: Fetch fresh user data from database
    const user = await findUserById(req.user.id);
    if (!user) {
      return sendAuthError(res, 'User not found', 404);
    }

    const response: ApiResponse<Partial<AuthUser>> = {
      success: true,
      data: {
        id: user.id,
        email: user.email,
        username: user.username,
        roles: user.roles,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: req.kilat?.requestId || 'unknown',
        processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
        version: '1.0.0'
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Profile error:', error);
    sendAuthError(res, 'Failed to fetch profile');
  }
};

// 🚪 Logout Handler
export const logoutHandler = async (req: KilatRequest, res: KilatResponse) => {
  try {
    // TODO: Implement token blacklisting if needed
    
    const response: ApiResponse = {
      success: true,
      message: 'Logout successful',
      meta: {
        timestamp: new Date().toISOString(),
        requestId: req.kilat?.requestId || 'unknown',
        processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
        version: '1.0.0'
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Logout error:', error);
    sendAuthError(res, 'Logout failed');
  }
};

// 🔧 Utility Functions
function extractToken(req: KilatRequest): string | null {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Check for token in cookies
  const cookieToken = req.cookies?.token;
  if (cookieToken) {
    return cookieToken;
  }
  
  // Check for token in query params (not recommended for production)
  const queryToken = req.query?.token as string;
  if (queryToken) {
    return queryToken;
  }
  
  return null;
}

function generateToken(user: AuthUser): string {
  const payload: Omit<AuthToken, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    roles: user.roles
  };

  return jwt.sign(
    payload,
    process.env.JWT_SECRET || 'kilat-secret-key',
    { 
      expiresIn: process.env.JWT_EXPIRY || '24h',
      issuer: 'kilat-backend',
      audience: 'kilat-app'
    }
  );
}

function generateUserId(): string {
  return 'user_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function sendAuthError(res: KilatResponse, message: string, status = 401) {
  const response: ApiResponse = {
    success: false,
    error: message,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: 'unknown',
      processingTime: 0,
      version: '1.0.0'
    }
  };

  res.status(status).json(response);
}

// 🗄️ Mock Database Functions (replace with actual database integration)
async function findUserByEmail(email: string): Promise<(AuthUser & { passwordHash: string }) | null> {
  // TODO: Implement actual database lookup
  // This is a mock implementation
  if (email === '<EMAIL>') {
    return {
      id: 'user_admin',
      email: '<EMAIL>',
      username: 'admin',
      roles: ['admin', 'user'],
      permissions: ['*'],
      passwordHash: await bcrypt.hash('admin123', 12),
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
  return null;
}

async function findUserById(id: string): Promise<AuthUser | null> {
  // TODO: Implement actual database lookup
  if (id === 'user_admin') {
    return {
      id: 'user_admin',
      email: '<EMAIL>',
      username: 'admin',
      roles: ['admin', 'user'],
      permissions: ['*'],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
  return null;
}

async function saveUser(user: AuthUser & { passwordHash: string }): Promise<void> {
  // TODO: Implement actual database save
  console.log('Saving user:', user.email);
}
