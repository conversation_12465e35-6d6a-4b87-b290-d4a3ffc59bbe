import type { 
  KilatRequest, 
  KilatResponse, 
  KilatMiddleware,
  ApiResponse 
} from '../types';

/**
 * Error Handling Middleware for Kilat Backend
 * Centralized error handling with logging and user-friendly responses
 */

// 🚨 Custom Error Classes
export class KilatError extends Error {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode = 500, code = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'KilatError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, KilatError);
  }
}

export class ValidationError extends KilatError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends KilatError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends KilatError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends KilatError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends KilatError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends KilatError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

// 🛡️ Main Error Handler Middleware
export const errorHandler: KilatMiddleware = (error: any, req: KilatRequest, res: KilatResponse, next) => {
  // Log the error
  logError(error, req);

  // Handle different error types
  if (error instanceof KilatError) {
    return sendErrorResponse(res, error, req);
  }

  // Handle validation errors from external libraries
  if (error.name === 'ValidationError' || error.type === 'validation') {
    const validationError = new ValidationError(error.message, error.details);
    return sendErrorResponse(res, validationError, req);
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    const authError = new AuthenticationError('Invalid authentication token');
    return sendErrorResponse(res, authError, req);
  }

  if (error.name === 'TokenExpiredError') {
    const authError = new AuthenticationError('Authentication token expired');
    return sendErrorResponse(res, authError, req);
  }

  // Handle database errors
  if (error.code === 'SQLITE_CONSTRAINT' || error.code === 'ER_DUP_ENTRY') {
    const conflictError = new ConflictError('Resource already exists');
    return sendErrorResponse(res, conflictError, req);
  }

  // Handle file upload errors
  if (error.code === 'LIMIT_FILE_SIZE') {
    const uploadError = new ValidationError('File size too large');
    return sendErrorResponse(res, uploadError, req);
  }

  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    const uploadError = new ValidationError('Unexpected file field');
    return sendErrorResponse(res, uploadError, req);
  }

  // Handle syntax errors (malformed JSON, etc.)
  if (error instanceof SyntaxError && error.message.includes('JSON')) {
    const syntaxError = new ValidationError('Invalid JSON in request body');
    return sendErrorResponse(res, syntaxError, req);
  }

  // Default to internal server error
  const internalError = new KilatError(
    process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message || 'Unknown error occurred'
  );
  
  return sendErrorResponse(res, internalError, req);
};

// 📝 Error Logging
function logError(error: any, req: KilatRequest) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: 'error',
    message: error.message || 'Unknown error',
    requestId: req.kilat?.requestId,
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection?.remoteAddress,
    userId: req.user?.id,
    stack: error.stack,
    code: error.code,
    statusCode: error.statusCode
  };

  // Log to console (in production, this would go to a proper logging service)
  console.error('🚨 Kilat Backend Error:', JSON.stringify(logEntry, null, 2));

  // TODO: Send to external logging service (Sentry, LogRocket, etc.)
  if (process.env.NODE_ENV === 'production') {
    // sendToLoggingService(logEntry);
  }
}

// 📤 Send Error Response
function sendErrorResponse(res: KilatResponse, error: KilatError, req: KilatRequest) {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    ...(error.details && { data: error.details }),
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.kilat?.requestId || 'unknown',
      processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
      version: '1.0.0',
      ...(process.env.NODE_ENV === 'development' && {
        errorCode: error.code,
        stack: error.stack
      })
    }
  };

  res.status(error.statusCode).json(response);
}

// 🔧 Error Handler Utilities
export const asyncHandler = (fn: Function) => {
  return (req: KilatRequest, res: KilatResponse, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 🎯 Error Response Helpers
export const throwNotFound = (message?: string) => {
  throw new NotFoundError(message);
};

export const throwValidation = (message: string, details?: any) => {
  throw new ValidationError(message, details);
};

export const throwAuth = (message?: string) => {
  throw new AuthenticationError(message);
};

export const throwForbidden = (message?: string) => {
  throw new AuthorizationError(message);
};

export const throwConflict = (message?: string) => {
  throw new ConflictError(message);
};

export const throwRateLimit = (message?: string) => {
  throw new RateLimitError(message);
};

// 🔍 Error Detection Utilities
export const isOperationalError = (error: any): boolean => {
  if (error instanceof KilatError) {
    return true;
  }
  
  // Check for known operational errors
  const operationalErrors = [
    'ValidationError',
    'CastError',
    'JsonWebTokenError',
    'TokenExpiredError',
    'MongoError',
    'SequelizeValidationError'
  ];
  
  return operationalErrors.includes(error.name);
};

// 🚨 Crash Handler for Unhandled Errors
export const setupGlobalErrorHandlers = () => {
  // Handle uncaught exceptions
  process.on('uncaughtException', (error: Error) => {
    console.error('💥 Uncaught Exception:', error);
    
    // Log the error
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: 'fatal',
      message: error.message,
      stack: error.stack,
      type: 'uncaughtException'
    };
    
    console.error('💥 Fatal Error:', JSON.stringify(logEntry, null, 2));
    
    // Graceful shutdown
    process.exit(1);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: 'fatal',
      message: reason?.message || 'Unhandled promise rejection',
      stack: reason?.stack,
      type: 'unhandledRejection',
      reason: reason
    };
    
    console.error('💥 Fatal Error:', JSON.stringify(logEntry, null, 2));
    
    // Graceful shutdown
    process.exit(1);
  });

  // Handle SIGTERM
  process.on('SIGTERM', () => {
    console.log('👋 SIGTERM received, shutting down gracefully');
    process.exit(0);
  });

  // Handle SIGINT (Ctrl+C)
  process.on('SIGINT', () => {
    console.log('👋 SIGINT received, shutting down gracefully');
    process.exit(0);
  });
};

// 🎭 Development Error Handler (with more details)
export const devErrorHandler: KilatMiddleware = (error: any, req: KilatRequest, res: KilatResponse, next) => {
  console.error('🐛 Development Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    query: req.query,
    params: req.params,
    headers: req.headers
  });

  const response: ApiResponse = {
    success: false,
    error: error.message || 'Development Error',
    data: {
      stack: error.stack,
      url: req.url,
      method: req.method,
      body: req.body,
      query: req.query,
      params: req.params
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.kilat?.requestId || 'unknown',
      processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
      version: '1.0.0'
    }
  };

  res.status(error.statusCode || 500).json(response);
};

// 🏥 Health Check Error Handler
export const healthCheckErrorHandler = (error: any) => {
  console.error('🏥 Health Check Error:', error);
  
  return {
    status: 'unhealthy',
    error: error.message,
    timestamp: new Date().toISOString()
  };
};
