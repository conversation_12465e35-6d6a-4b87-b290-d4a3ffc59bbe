import validator from 'validator';
import type { 
  KilatRequest, 
  KilatResponse, 
  KilatMiddleware, 
  ValidationSchema, 
  ValidationRule,
  ApiResponse 
} from '../types';

/**
 * Validation Middleware for Kilat Backend
 * Validates request body, query parameters, and route parameters
 */

export const validateRequest = (schema: {
  body?: ValidationSchema;
  query?: ValidationSchema;
  params?: ValidationSchema;
}): KilatMiddleware => {
  return async (req: KilatRequest, res: KilatResponse, next) => {
    try {
      const errors: string[] = [];

      // Validate request body
      if (schema.body && req.body) {
        const bodyErrors = validateObject(req.body, schema.body, 'body');
        errors.push(...bodyErrors);
      }

      // Validate query parameters
      if (schema.query && req.query) {
        const queryErrors = validateObject(req.query, schema.query, 'query');
        errors.push(...queryErrors);
      }

      // Validate route parameters
      if (schema.params && req.params) {
        const paramErrors = validateObject(req.params, schema.params, 'params');
        errors.push(...paramErrors);
      }

      if (errors.length > 0) {
        return sendValidationError(res, errors, req);
      }

      next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      return sendValidationError(res, ['Validation failed'], req);
    }
  };
};

// 🔍 Validate object against schema
function validateObject(
  obj: Record<string, any>, 
  schema: ValidationSchema, 
  context: string
): string[] {
  const errors: string[] = [];

  for (const [field, rule] of Object.entries(schema)) {
    const value = obj[field];
    const fieldErrors = validateField(value, rule, `${context}.${field}`);
    errors.push(...fieldErrors);
  }

  return errors;
}

// 🔍 Validate individual field
function validateField(value: any, rule: ValidationRule, fieldPath: string): string[] {
  const errors: string[] = [];

  // Check if field is required
  if (rule.required && (value === undefined || value === null || value === '')) {
    errors.push(`${fieldPath} is required`);
    return errors; // Don't continue validation if required field is missing
  }

  // Skip validation if field is not provided and not required
  if (value === undefined || value === null) {
    return errors;
  }

  // Type validation
  if (rule.type) {
    const typeError = validateType(value, rule.type, fieldPath);
    if (typeError) {
      errors.push(typeError);
      return errors; // Don't continue if type is wrong
    }
  }

  // Length/size validation
  if (rule.min !== undefined) {
    const minError = validateMin(value, rule.min, fieldPath);
    if (minError) errors.push(minError);
  }

  if (rule.max !== undefined) {
    const maxError = validateMax(value, rule.max, fieldPath);
    if (maxError) errors.push(maxError);
  }

  // Pattern validation
  if (rule.pattern) {
    const patternError = validatePattern(value, rule.pattern, fieldPath);
    if (patternError) errors.push(patternError);
  }

  // Custom validation
  if (rule.custom) {
    const customResult = rule.custom(value);
    if (customResult !== true) {
      const errorMessage = typeof customResult === 'string' 
        ? customResult 
        : `${fieldPath} failed custom validation`;
      errors.push(errorMessage);
    }
  }

  return errors;
}

// 🎯 Type validation
function validateType(value: any, type: ValidationRule['type'], fieldPath: string): string | null {
  switch (type) {
    case 'string':
      if (typeof value !== 'string') {
        return `${fieldPath} must be a string`;
      }
      break;

    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        return `${fieldPath} must be a number`;
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        return `${fieldPath} must be a boolean`;
      }
      break;

    case 'email':
      if (typeof value !== 'string' || !validator.isEmail(value)) {
        return `${fieldPath} must be a valid email address`;
      }
      break;

    case 'url':
      if (typeof value !== 'string' || !validator.isURL(value)) {
        return `${fieldPath} must be a valid URL`;
      }
      break;

    case 'uuid':
      if (typeof value !== 'string' || !validator.isUUID(value)) {
        return `${fieldPath} must be a valid UUID`;
      }
      break;

    default:
      return `Unknown validation type: ${type}`;
  }

  return null;
}

// 📏 Minimum value/length validation
function validateMin(value: any, min: number, fieldPath: string): string | null {
  if (typeof value === 'string' || Array.isArray(value)) {
    if (value.length < min) {
      return `${fieldPath} must be at least ${min} characters long`;
    }
  } else if (typeof value === 'number') {
    if (value < min) {
      return `${fieldPath} must be at least ${min}`;
    }
  }
  return null;
}

// 📏 Maximum value/length validation
function validateMax(value: any, max: number, fieldPath: string): string | null {
  if (typeof value === 'string' || Array.isArray(value)) {
    if (value.length > max) {
      return `${fieldPath} must be at most ${max} characters long`;
    }
  } else if (typeof value === 'number') {
    if (value > max) {
      return `${fieldPath} must be at most ${max}`;
    }
  }
  return null;
}

// 🔍 Pattern validation
function validatePattern(value: any, pattern: RegExp, fieldPath: string): string | null {
  if (typeof value === 'string' && !pattern.test(value)) {
    return `${fieldPath} format is invalid`;
  }
  return null;
}

// 📤 Send validation error response
function sendValidationError(res: KilatResponse, errors: string[], req: KilatRequest) {
  const response: ApiResponse = {
    success: false,
    error: 'Validation failed',
    data: { errors },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.kilat?.requestId || 'unknown',
      processingTime: Date.now() - (req.kilat?.startTime || Date.now()),
      version: '1.0.0'
    }
  };

  res.status(400).json(response);
}

// 🎯 Common validation schemas
export const commonSchemas = {
  // User registration
  register: {
    body: {
      email: { required: true, type: 'email' as const },
      password: { required: true, type: 'string' as const, min: 8, max: 128 },
      username: { type: 'string' as const, min: 3, max: 50 }
    }
  },

  // User login
  login: {
    body: {
      email: { required: true, type: 'email' as const },
      password: { required: true, type: 'string' as const }
    }
  },

  // Pagination
  pagination: {
    query: {
      page: { type: 'number' as const, min: 1 },
      limit: { type: 'number' as const, min: 1, max: 100 },
      sort: { type: 'string' as const },
      order: { 
        type: 'string' as const,
        custom: (value: string) => ['asc', 'desc'].includes(value.toLowerCase()) || 'Order must be asc or desc'
      }
    }
  },

  // ID parameter
  idParam: {
    params: {
      id: { required: true, type: 'string' as const, min: 1 }
    }
  },

  // UUID parameter
  uuidParam: {
    params: {
      id: { required: true, type: 'uuid' as const }
    }
  },

  // File upload
  fileUpload: {
    body: {
      name: { required: true, type: 'string' as const, min: 1, max: 255 },
      description: { type: 'string' as const, max: 1000 }
    }
  },

  // Search
  search: {
    query: {
      q: { required: true, type: 'string' as const, min: 1, max: 100 },
      category: { type: 'string' as const },
      tags: { type: 'string' as const }
    }
  }
};

// 🔧 Validation helper functions
export const validators = {
  // Check if string is a valid JSON
  isValidJSON: (value: string): boolean => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  },

  // Check if string contains only alphanumeric characters
  isAlphanumeric: (value: string): boolean => {
    return /^[a-zA-Z0-9]+$/.test(value);
  },

  // Check if string is a valid slug
  isSlug: (value: string): boolean => {
    return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(value);
  },

  // Check if value is in allowed list
  isInList: (allowedValues: any[]) => (value: any): boolean => {
    return allowedValues.includes(value);
  },

  // Check if string is a valid hex color
  isHexColor: (value: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);
  },

  // Check if number is positive
  isPositive: (value: number): boolean => {
    return value > 0;
  },

  // Check if array has unique values
  hasUniqueValues: (value: any[]): boolean => {
    return new Set(value).size === value.length;
  }
};

// 🎨 Validation rule builders
export const rules = {
  required: (): Partial<ValidationRule> => ({ required: true }),
  
  string: (min?: number, max?: number): Partial<ValidationRule> => ({
    type: 'string',
    ...(min !== undefined && { min }),
    ...(max !== undefined && { max })
  }),
  
  number: (min?: number, max?: number): Partial<ValidationRule> => ({
    type: 'number',
    ...(min !== undefined && { min }),
    ...(max !== undefined && { max })
  }),
  
  email: (): Partial<ValidationRule> => ({ type: 'email' }),
  
  url: (): Partial<ValidationRule> => ({ type: 'url' }),
  
  uuid: (): Partial<ValidationRule> => ({ type: 'uuid' }),
  
  pattern: (regex: RegExp): Partial<ValidationRule> => ({ pattern: regex }),
  
  custom: (validator: (value: any) => boolean | string): Partial<ValidationRule> => ({
    custom: validator
  }),
  
  oneOf: (values: any[]): Partial<ValidationRule> => ({
    custom: (value: any) => values.includes(value) || `Value must be one of: ${values.join(', ')}`
  })
};
