import type { KilatRequest, KilatResponse, ApiResponse, HealthCheck } from '../../types';

/**
 * Health Check Endpoint
 * GET /api/health
 */
export async function GET(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<HealthCheck>> {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  const healthData: HealthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(uptime),
    memory: {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
      rss: Math.round(memoryUsage.rss / 1024 / 1024)
    },
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    platform: process.platform,
    nodeVersion: process.version
  };

  return {
    success: true,
    data: healthData,
    message: 'Server is healthy',
    timestamp: new Date().toISOString()
  };
}

/**
 * Detailed Health Check
 * GET /api/health/detailed
 */
export async function detailed(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<any>> {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  const detailedHealth = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    server: {
      uptime: Math.floor(uptime),
      pid: process.pid,
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development'
    },
    memory: {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0],
    network: {
      hostname: require('os').hostname(),
      networkInterfaces: Object.keys(require('os').networkInterfaces())
    }
  };

  return {
    success: true,
    data: detailedHealth,
    message: 'Detailed server health information',
    timestamp: new Date().toISOString()
  };
}
