import type { KilatRequest, KilatResponse, ApiResponse } from '../../types';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

interface UploadedFile {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedAt: string;
}

/**
 * Upload File
 * POST /api/upload
 */
export async function POST(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<UploadedFile | UploadedFile[]>> {
  try {
    // Check if files were uploaded
    if (!req.files || (Array.isArray(req.files) && req.files.length === 0)) {
      return {
        success: false,
        data: null,
        message: 'No files uploaded',
        timestamp: new Date().toISOString(),
        error: {
          code: 'NO_FILES',
          details: 'Please select at least one file to upload'
        }
      };
    }

    const uploadDir = join(process.cwd(), 'uploads');
    
    // Create uploads directory if it doesn't exist
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    const files = Array.isArray(req.files) ? req.files : [req.files];
    const uploadedFiles: UploadedFile[] = [];

    for (const file of files) {
      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      const fileExtension = file.originalname.split('.').pop();
      const filename = `${timestamp}-${randomString}.${fileExtension}`;
      const filepath = join(uploadDir, filename);

      // Save file
      await writeFile(filepath, file.buffer);

      const uploadedFile: UploadedFile = {
        id: `${timestamp}-${randomString}`,
        filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: `/uploads/${filename}`,
        uploadedAt: new Date().toISOString()
      };

      uploadedFiles.push(uploadedFile);
    }

    // Return single file or array based on input
    const result = uploadedFiles.length === 1 ? uploadedFiles[0] : uploadedFiles;

    return {
      success: true,
      data: result,
      message: `${uploadedFiles.length} file(s) uploaded successfully`,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    return {
      success: false,
      data: null,
      message: 'File upload failed',
      timestamp: new Date().toISOString(),
      error: {
        code: 'UPLOAD_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}

/**
 * Upload Image with Processing
 * POST /api/upload/image
 */
export async function image(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<UploadedFile>> {
  try {
    if (!req.files || Array.isArray(req.files) || !req.files.image) {
      return {
        success: false,
        data: null,
        message: 'No image file uploaded',
        timestamp: new Date().toISOString(),
        error: {
          code: 'NO_IMAGE',
          details: 'Please upload an image file'
        }
      };
    }

    const file = req.files.image;

    // Validate image type
    if (!file.mimetype.startsWith('image/')) {
      return {
        success: false,
        data: null,
        message: 'Invalid file type',
        timestamp: new Date().toISOString(),
        error: {
          code: 'INVALID_TYPE',
          details: 'Only image files are allowed'
        }
      };
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        success: false,
        data: null,
        message: 'File too large',
        timestamp: new Date().toISOString(),
        error: {
          code: 'FILE_TOO_LARGE',
          details: 'Image must be smaller than 5MB'
        }
      };
    }

    const uploadDir = join(process.cwd(), 'uploads', 'images');
    
    // Create uploads directory if it doesn't exist
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const fileExtension = file.originalname.split('.').pop();
    const filename = `img-${timestamp}-${randomString}.${fileExtension}`;
    const filepath = join(uploadDir, filename);

    // Save file
    await writeFile(filepath, file.buffer);

    const uploadedFile: UploadedFile = {
      id: `img-${timestamp}-${randomString}`,
      filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url: `/uploads/images/${filename}`,
      uploadedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: uploadedFile,
      message: 'Image uploaded successfully',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    return {
      success: false,
      data: null,
      message: 'Image upload failed',
      timestamp: new Date().toISOString(),
      error: {
        code: 'UPLOAD_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}

/**
 * Get Upload Statistics
 * GET /api/upload/stats
 */
export async function stats(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<any>> {
  try {
    const uploadDir = join(process.cwd(), 'uploads');
    
    if (!existsSync(uploadDir)) {
      return {
        success: true,
        data: {
          totalFiles: 0,
          totalSize: 0,
          fileTypes: {},
          recentUploads: []
        },
        message: 'Upload statistics retrieved successfully',
        timestamp: new Date().toISOString()
      };
    }

    // This is a simplified version - in a real app you'd track this in a database
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      fileTypes: {} as Record<string, number>,
      recentUploads: [] as any[],
      uploadTrends: {
        today: 0,
        thisWeek: 0,
        thisMonth: 0
      }
    };

    return {
      success: true,
      data: stats,
      message: 'Upload statistics retrieved successfully',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    return {
      success: false,
      data: null,
      message: 'Failed to retrieve upload statistics',
      timestamp: new Date().toISOString(),
      error: {
        code: 'STATS_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}
