import type { KilatRequest, KilatResponse, ApiResponse } from '../../types';

// Mock user data for demonstration
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jane',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Ahmad',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z'
  }
];

/**
 * Get All Users
 * GET /api/users
 */
export async function GET(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<User[]>> {
  const { page = 1, limit = 10, search } = req.query;
  
  let filteredUsers = mockUsers;
  
  // Search functionality
  if (search) {
    const searchTerm = search.toString().toLowerCase();
    filteredUsers = mockUsers.filter(user => 
      user.name.toLowerCase().includes(searchTerm) ||
      user.email.toLowerCase().includes(searchTerm)
    );
  }
  
  // Pagination
  const startIndex = (Number(page) - 1) * Number(limit);
  const endIndex = startIndex + Number(limit);
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
  
  return {
    success: true,
    data: paginatedUsers,
    message: 'Users retrieved successfully',
    timestamp: new Date().toISOString(),
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total: filteredUsers.length,
      totalPages: Math.ceil(filteredUsers.length / Number(limit))
    }
  };
}

/**
 * Get User by ID
 * GET /api/users/[id]
 */
export async function getById(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<User | null>> {
  const { id } = req.params;
  
  const user = mockUsers.find(u => u.id === id);
  
  if (!user) {
    return {
      success: false,
      data: null,
      message: 'User not found',
      timestamp: new Date().toISOString(),
      error: {
        code: 'USER_NOT_FOUND',
        details: `User with ID ${id} does not exist`
      }
    };
  }
  
  return {
    success: true,
    data: user,
    message: 'User retrieved successfully',
    timestamp: new Date().toISOString()
  };
}

/**
 * Create New User
 * POST /api/users
 */
export async function POST(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<User>> {
  const { name, email } = req.body;
  
  // Validation
  if (!name || !email) {
    return {
      success: false,
      data: null,
      message: 'Name and email are required',
      timestamp: new Date().toISOString(),
      error: {
        code: 'VALIDATION_ERROR',
        details: 'Missing required fields: name, email'
      }
    };
  }
  
  // Check if email already exists
  const existingUser = mockUsers.find(u => u.email === email);
  if (existingUser) {
    return {
      success: false,
      data: null,
      message: 'Email already exists',
      timestamp: new Date().toISOString(),
      error: {
        code: 'EMAIL_EXISTS',
        details: 'A user with this email already exists'
      }
    };
  }
  
  // Create new user
  const newUser: User = {
    id: (mockUsers.length + 1).toString(),
    name,
    email,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockUsers.push(newUser);
  
  return {
    success: true,
    data: newUser,
    message: 'User created successfully',
    timestamp: new Date().toISOString()
  };
}

/**
 * Update User
 * PUT /api/users/[id]
 */
export async function PUT(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<User | null>> {
  const { id } = req.params;
  const { name, email } = req.body;
  
  const userIndex = mockUsers.findIndex(u => u.id === id);
  
  if (userIndex === -1) {
    return {
      success: false,
      data: null,
      message: 'User not found',
      timestamp: new Date().toISOString(),
      error: {
        code: 'USER_NOT_FOUND',
        details: `User with ID ${id} does not exist`
      }
    };
  }
  
  // Update user
  if (name) mockUsers[userIndex].name = name;
  if (email) mockUsers[userIndex].email = email;
  mockUsers[userIndex].updatedAt = new Date().toISOString();
  
  return {
    success: true,
    data: mockUsers[userIndex],
    message: 'User updated successfully',
    timestamp: new Date().toISOString()
  };
}

/**
 * Delete User
 * DELETE /api/users/[id]
 */
export async function DELETE(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<null>> {
  const { id } = req.params;
  
  const userIndex = mockUsers.findIndex(u => u.id === id);
  
  if (userIndex === -1) {
    return {
      success: false,
      data: null,
      message: 'User not found',
      timestamp: new Date().toISOString(),
      error: {
        code: 'USER_NOT_FOUND',
        details: `User with ID ${id} does not exist`
      }
    };
  }
  
  // Remove user
  mockUsers.splice(userIndex, 1);
  
  return {
    success: true,
    data: null,
    message: 'User deleted successfully',
    timestamp: new Date().toISOString()
  };
}

/**
 * Get User Statistics
 * GET /api/users/stats
 */
export async function stats(req: KilatRequest, res: KilatResponse): Promise<ApiResponse<any>> {
  const totalUsers = mockUsers.length;
  const recentUsers = mockUsers.filter(user => {
    const createdAt = new Date(user.createdAt);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return createdAt > weekAgo;
  }).length;
  
  const emailDomains = mockUsers.reduce((acc, user) => {
    const domain = user.email.split('@')[1];
    acc[domain] = (acc[domain] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    success: true,
    data: {
      total: totalUsers,
      recentUsers,
      emailDomains,
      averageNameLength: Math.round(
        mockUsers.reduce((sum, user) => sum + user.name.length, 0) / totalUsers
      )
    },
    message: 'User statistics retrieved successfully',
    timestamp: new Date().toISOString()
  };
}
