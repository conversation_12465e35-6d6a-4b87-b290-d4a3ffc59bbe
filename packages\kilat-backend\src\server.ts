import { createServer } from 'http';
import { join, extname } from 'path';
import { readdir, stat } from 'fs/promises';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import multer from 'multer';
import type {
  KilatBackendConfig,
  KilatRequest,
  KilatResponse,
  KilatMiddleware,
  KilatHandler,
  FileRoute,
  ApiResponse,
  ServerStats,
  HealthCheck
} from './types';
import { createLogger } from './utils/logger';
import { validateRequest } from './middleware/validation';
import { authMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/error';

/**
 * Kilat Backend Server
 * File-based routing with built-in middleware and utilities
 */
export class KilatBackend {
  private server: any;
  private routes: Map<string, FileRoute> = new Map();
  private middleware: KilatMiddleware[] = [];
  private config: KilatBackendConfig;
  private logger: any;
  private stats: ServerStats;
  private startTime: number;

  constructor(config: Partial<KilatBackendConfig> = {}) {
    this.config = this.mergeConfig(config);
    this.logger = createLogger(this.config.logging);
    this.startTime = Date.now();
    this.stats = this.initStats();
    
    this.setupServer();
  }

  private mergeConfig(config: Partial<KilatBackendConfig>): KilatBackendConfig {
    return {
      port: config.port || 8080,
      host: config.host || 'localhost',
      apiPrefix: config.apiPrefix || '/api',
      autoStart: config.autoStart ?? true,
      cors: {
        enabled: config.cors?.enabled ?? true,
        origin: config.cors?.origin || '*',
        credentials: config.cors?.credentials ?? true
      },
      rateLimit: {
        enabled: config.rateLimit?.enabled ?? true,
        windowMs: config.rateLimit?.windowMs || 15 * 60 * 1000, // 15 minutes
        max: config.rateLimit?.max || 100,
        message: config.rateLimit?.message || 'Too many requests'
      },
      security: {
        helmet: config.security?.helmet ?? true,
        compression: config.security?.compression ?? true,
        trustProxy: config.security?.trustProxy ?? false
      },
      uploads: {
        enabled: config.uploads?.enabled ?? true,
        maxFileSize: config.uploads?.maxFileSize || 10 * 1024 * 1024, // 10MB
        allowedTypes: config.uploads?.allowedTypes || ['image/*', 'application/pdf'],
        destination: config.uploads?.destination || './uploads'
      },
      logging: {
        enabled: config.logging?.enabled ?? true,
        level: config.logging?.level || 'info',
        format: config.logging?.format || 'combined'
      },
      database: {
        enabled: config.database?.enabled ?? true,
        autoConnect: config.database?.autoConnect ?? true
      },
      auth: {
        enabled: config.auth?.enabled ?? true,
        jwtSecret: config.auth?.jwtSecret || 'kilat-secret-key',
        jwtExpiry: config.auth?.jwtExpiry || '24h',
        bcryptRounds: config.auth?.bcryptRounds || 12
      }
    };
  }

  private initStats(): ServerStats {
    return {
      uptime: 0,
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      },
      database: {
        connected: false,
        queries: 0,
        averageQueryTime: 0
      },
      errors: {
        total: 0,
        last24h: 0
      }
    };
  }

  private setupServer() {
    // Create HTTP server with Bun's optimized handler
    this.server = createServer(this.handleRequest.bind(this));

    // Setup global middleware
    this.setupMiddleware();

    // Load file-based routes
    this.loadRoutes();

    // Setup built-in routes
    this.setupBuiltinRoutes();
  }

  private setupMiddleware() {
    // Security middleware
    if (this.config.security.helmet) {
      this.use(helmet() as any);
    }

    // Compression
    if (this.config.security.compression) {
      this.use(compression() as any);
    }

    // CORS
    if (this.config.cors.enabled) {
      this.use(cors({
        origin: this.config.cors.origin,
        credentials: this.config.cors.credentials
      }) as any);
    }

    // Rate limiting
    if (this.config.rateLimit.enabled) {
      this.use(rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.max,
        message: { error: this.config.rateLimit.message }
      }) as any);
    }

    // Request logging and stats
    this.use(this.requestLogger.bind(this));

    // Body parsing (built into Bun)
    this.use(this.bodyParser.bind(this));

    // File uploads
    if (this.config.uploads.enabled) {
      const upload = multer({
        dest: this.config.uploads.destination,
        limits: { fileSize: this.config.uploads.maxFileSize }
      });
      this.use(upload.any() as any);
    }
  }

  private async loadRoutes() {
    const routesDir = join(process.cwd(), 'api');
    
    try {
      await this.scanRoutes(routesDir, '');
      this.logger.info(`Loaded ${this.routes.size} routes`);
    } catch (error) {
      this.logger.warn('No api directory found, skipping file-based routes');
    }
  }

  private async scanRoutes(dir: string, prefix: string) {
    const entries = await readdir(dir);

    for (const entry of entries) {
      const fullPath = join(dir, entry);
      const stats = await stat(fullPath);

      if (stats.isDirectory()) {
        await this.scanRoutes(fullPath, `${prefix}/${entry}`);
      } else if (extname(entry) === '.ts' || extname(entry) === '.js') {
        await this.loadRouteFile(fullPath, prefix);
      }
    }
  }

  private async loadRouteFile(filePath: string, prefix: string) {
    try {
      const module = await import(filePath);
      const fileName = filePath.split('/').pop()?.replace(/\.(ts|js)$/, '') || '';
      
      // Support different export patterns
      const handlers = module.default || module;
      
      for (const [method, handler] of Object.entries(handlers)) {
        if (typeof handler === 'function') {
          const routePath = this.buildRoutePath(prefix, fileName, method);
          const route: FileRoute = {
            filePath,
            routePath,
            method: method.toUpperCase(),
            handler: handler as KilatHandler,
            middleware: []
          };
          
          this.routes.set(`${method.toUpperCase()} ${routePath}`, route);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to load route file ${filePath}:`, error);
    }
  }

  private buildRoutePath(prefix: string, fileName: string, method: string): string {
    let path = prefix;
    
    // Handle index files
    if (fileName === 'index') {
      path = prefix || '/';
    } else if (fileName !== method) {
      path = `${prefix}/${fileName}`;
    }
    
    // Handle dynamic routes [param]
    path = path.replace(/\[([^\]]+)\]/g, ':$1');
    
    return this.config.apiPrefix + path;
  }

  private setupBuiltinRoutes() {
    // Health check endpoint
    this.get('/health', this.healthCheck.bind(this));
    
    // Server stats endpoint
    this.get('/stats', this.serverStats.bind(this));
    
    // Route listing endpoint (development only)
    if (process.env.NODE_ENV === 'development') {
      this.get('/routes', this.listRoutes.bind(this));
    }
  }

  private async handleRequest(req: any, res: any) {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // Enhance request object
    req.kilat = {
      startTime,
      requestId,
      platform: this.detectPlatform(req),
      version: '1.0.0'
    };

    try {
      // Find matching route
      const routeKey = `${req.method} ${req.url.split('?')[0]}`;
      const route = this.routes.get(routeKey) || this.findDynamicRoute(req.method, req.url);

      if (route) {
        // Execute middleware chain
        await this.executeMiddleware(req, res, [...this.middleware, ...route.middleware]);
        
        // Execute route handler
        await route.handler(req, res);
      } else {
        this.sendNotFound(res);
      }
    } catch (error) {
      await errorHandler(error, req, res, () => {});
    } finally {
      this.updateStats(req, res, startTime);
    }
  }

  private findDynamicRoute(method: string, url: string): FileRoute | null {
    const urlParts = url.split('?')[0].split('/');
    
    for (const [key, route] of this.routes) {
      if (!key.startsWith(method)) continue;
      
      const routePath = key.substring(method.length + 1);
      const routeParts = routePath.split('/');
      
      if (routeParts.length !== urlParts.length) continue;
      
      let matches = true;
      for (let i = 0; i < routeParts.length; i++) {
        if (routeParts[i].startsWith(':')) continue;
        if (routeParts[i] !== urlParts[i]) {
          matches = false;
          break;
        }
      }
      
      if (matches) {
        // Extract params
        const params: Record<string, string> = {};
        for (let i = 0; i < routeParts.length; i++) {
          if (routeParts[i].startsWith(':')) {
            params[routeParts[i].substring(1)] = urlParts[i];
          }
        }
        (route as any).params = params;
        return route;
      }
    }
    
    return null;
  }

  private async executeMiddleware(req: any, res: any, middleware: KilatMiddleware[]) {
    for (const mw of middleware) {
      await new Promise<void>((resolve, reject) => {
        mw(req, res, (error?: any) => {
          if (error) reject(error);
          else resolve();
        });
      });
    }
  }

  // HTTP method helpers
  public get(path: string, handler: KilatHandler, middleware: KilatMiddleware[] = []) {
    this.addRoute('GET', path, handler, middleware);
  }

  public post(path: string, handler: KilatHandler, middleware: KilatMiddleware[] = []) {
    this.addRoute('POST', path, handler, middleware);
  }

  public put(path: string, handler: KilatHandler, middleware: KilatMiddleware[] = []) {
    this.addRoute('PUT', path, handler, middleware);
  }

  public delete(path: string, handler: KilatHandler, middleware: KilatMiddleware[] = []) {
    this.addRoute('DELETE', path, handler, middleware);
  }

  public patch(path: string, handler: KilatHandler, middleware: KilatMiddleware[] = []) {
    this.addRoute('PATCH', path, handler, middleware);
  }

  private addRoute(method: string, path: string, handler: KilatHandler, middleware: KilatMiddleware[]) {
    const fullPath = this.config.apiPrefix + path;
    const route: FileRoute = {
      filePath: 'programmatic',
      routePath: fullPath,
      method,
      handler,
      middleware
    };
    
    this.routes.set(`${method} ${fullPath}`, route);
  }

  public use(middleware: KilatMiddleware) {
    this.middleware.push(middleware);
  }

  // Built-in route handlers
  private async healthCheck(req: KilatRequest, res: KilatResponse) {
    const health: HealthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: '1.0.0',
      services: {
        database: 'up',
        cache: 'up',
        storage: 'up'
      },
      metrics: this.stats
    };

    this.sendJson(res, health);
  }

  private async serverStats(req: KilatRequest, res: KilatResponse) {
    this.updateMemoryStats();
    this.sendJson(res, this.stats);
  }

  private async listRoutes(req: KilatRequest, res: KilatResponse) {
    const routes = Array.from(this.routes.entries()).map(([key, route]) => ({
      method: route.method,
      path: route.routePath,
      file: route.filePath
    }));

    this.sendJson(res, { routes });
  }

  // Utility methods
  private requestLogger: KilatMiddleware = (req, res, next) => {
    if (this.config.logging.enabled) {
      this.logger.info(`${req.method} ${req.url}`, {
        requestId: req.kilat?.requestId,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
    }
    next();
  };

  private bodyParser: KilatMiddleware = async (req, res, next) => {
    if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
      try {
        const body = await req.json?.() || {};
        req.body = body;
      } catch (error) {
        // Body might not be JSON, that's okay
      }
    }
    next();
  };

  private generateRequestId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private detectPlatform(req: any): 'web' | 'mobile' | 'desktop' {
    const userAgent = req.headers['user-agent'] || '';
    if (/mobile|android|iphone/i.test(userAgent)) return 'mobile';
    if (/electron/i.test(userAgent)) return 'desktop';
    return 'web';
  }

  private sendJson(res: any, data: any, status = 200) {
    res.status(status).json(data);
  }

  private sendNotFound(res: any) {
    const response: ApiResponse = {
      success: false,
      error: 'Route not found',
      meta: {
        timestamp: new Date().toISOString(),
        requestId: 'unknown',
        processingTime: 0,
        version: '1.0.0'
      }
    };
    this.sendJson(res, response, 404);
  }

  private updateStats(req: any, res: any, startTime: number) {
    const processingTime = Date.now() - startTime;
    this.stats.requests.total++;
    
    if (res.statusCode < 400) {
      this.stats.requests.successful++;
    } else {
      this.stats.requests.failed++;
    }
    
    // Update average response time
    this.stats.requests.averageResponseTime = 
      (this.stats.requests.averageResponseTime * (this.stats.requests.total - 1) + processingTime) / 
      this.stats.requests.total;
  }

  private updateMemoryStats() {
    const memUsage = process.memoryUsage();
    this.stats.memory.used = memUsage.heapUsed;
    this.stats.memory.total = memUsage.heapTotal;
    this.stats.memory.percentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    this.stats.uptime = Date.now() - this.startTime;
  }

  // Server lifecycle
  public async start(): Promise<void> {
    return new Promise((resolve) => {
      this.server.listen(this.config.port, this.config.host, () => {
        this.logger.info(`🚀 Kilat Backend started on http://${this.config.host}:${this.config.port}`);
        this.logger.info(`📡 API prefix: ${this.config.apiPrefix}`);
        resolve();
      });
    });
  }

  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      this.server.close(() => {
        this.logger.info('🛑 Kilat Backend stopped');
        resolve();
      });
    });
  }

  public getStats(): ServerStats {
    this.updateMemoryStats();
    return this.stats;
  }

  public getConfig(): KilatBackendConfig {
    return this.config;
  }
}
