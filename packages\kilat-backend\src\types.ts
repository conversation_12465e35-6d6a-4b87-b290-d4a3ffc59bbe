import { Request, Response, NextFunction } from 'express';

// 🎯 Core Backend Types
export interface KilatRequest extends Request {
  user?: any;
  session?: any;
  kilat?: {
    startTime: number;
    requestId: string;
    platform: 'web' | 'mobile' | 'desktop';
    version: string;
  };
}

export interface KilatResponse extends Response {
  kilat?: {
    cached?: boolean;
    processingTime?: number;
  };
}

export type KilatMiddleware = (
  req: KilatRequest,
  res: KilatResponse,
  next: NextFunction
) => void | Promise<void>;

export type KilatHandler = (
  req: KilatRequest,
  res: KilatResponse
) => void | Promise<void>;

// 🔧 Server Configuration
export interface KilatBackendConfig {
  port: number;
  host: string;
  apiPrefix: string;
  autoStart: boolean;
  cors: {
    enabled: boolean;
    origin: string | string[];
    credentials: boolean;
  };
  rateLimit: {
    enabled: boolean;
    windowMs: number;
    max: number;
    message: string;
  };
  security: {
    helmet: boolean;
    compression: boolean;
    trustProxy: boolean;
  };
  uploads: {
    enabled: boolean;
    maxFileSize: number;
    allowedTypes: string[];
    destination: string;
  };
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    format: 'json' | 'combined' | 'dev';
  };
  database: {
    enabled: boolean;
    autoConnect: boolean;
  };
  auth: {
    enabled: boolean;
    jwtSecret: string;
    jwtExpiry: string;
    bcryptRounds: number;
  };
}

// 🛣️ Route Definition
export interface RouteDefinition {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  handler: KilatHandler;
  middleware?: KilatMiddleware[];
  auth?: boolean;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  validation?: {
    body?: any;
    query?: any;
    params?: any;
  };
}

// 📁 File-based Route Structure
export interface FileRoute {
  filePath: string;
  routePath: string;
  method: string;
  handler: KilatHandler;
  middleware: KilatMiddleware[];
}

// 🔐 Authentication Types
export interface AuthUser {
  id: string;
  email: string;
  username?: string;
  roles: string[];
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthToken {
  userId: string;
  email: string;
  roles: string[];
  iat: number;
  exp: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  username?: string;
}

// 📊 API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  meta?: {
    timestamp: string;
    requestId: string;
    processingTime: number;
    version: string;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 🔍 Validation Types
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'uuid';
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

// 📝 Logging Types
export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: string;
  requestId?: string;
  userId?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  processingTime?: number;
  userAgent?: string;
  ip?: string;
  error?: Error;
  meta?: Record<string, any>;
}

// 🔄 Middleware Types
export interface MiddlewareOptions {
  auth?: {
    required: boolean;
    roles?: string[];
    permissions?: string[];
  };
  rateLimit?: {
    windowMs: number;
    max: number;
    skipSuccessfulRequests?: boolean;
  };
  validation?: {
    body?: ValidationSchema;
    query?: ValidationSchema;
    params?: ValidationSchema;
  };
  cache?: {
    ttl: number;
    key?: string;
  };
}

// 🗄️ Database Integration Types
export interface DatabaseConnection {
  isConnected: boolean;
  driver: 'sqlite' | 'mysql';
  connectionString: string;
  lastPing?: Date;
}

// 📤 File Upload Types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer?: Buffer;
}

// 🎮 Plugin Integration Types
export interface BackendPlugin {
  name: string;
  version: string;
  enabled: boolean;
  routes?: RouteDefinition[];
  middleware?: KilatMiddleware[];
  onInit?: (server: any) => void | Promise<void>;
  onStart?: (server: any) => void | Promise<void>;
  onStop?: (server: any) => void | Promise<void>;
  onError?: (error: Error, server: any) => void | Promise<void>;
}

// 📊 Server Statistics
export interface ServerStats {
  uptime: number;
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  database: {
    connected: boolean;
    queries: number;
    averageQueryTime: number;
  };
  errors: {
    total: number;
    last24h: number;
    lastError?: {
      message: string;
      timestamp: string;
    };
  };
}

// 🔧 Health Check Types
export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: 'up' | 'down' | 'degraded';
    cache: 'up' | 'down' | 'degraded';
    storage: 'up' | 'down' | 'degraded';
  };
  metrics: ServerStats;
}
