import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import type { LogEntry } from '../types';

/**
 * Kilat Backend Logger
 * Simple but effective logging system with file and console output
 */

export interface LoggerConfig {
  enabled: boolean;
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'combined' | 'dev';
  file?: {
    enabled: boolean;
    path: string;
    maxSize: number; // in bytes
    maxFiles: number;
  };
  console?: {
    enabled: boolean;
    colors: boolean;
  };
}

export class KilatLogger {
  private config: LoggerConfig;
  private logLevels = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
  };

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      enabled: config.enabled ?? true,
      level: config.level || 'info',
      format: config.format || 'combined',
      file: {
        enabled: config.file?.enabled ?? true,
        path: config.file?.path || './.kilat/logs',
        maxSize: config.file?.maxSize || 10 * 1024 * 1024, // 10MB
        maxFiles: config.file?.maxFiles || 5
      },
      console: {
        enabled: config.console?.enabled ?? true,
        colors: config.console?.colors ?? true
      }
    };

    this.ensureLogDirectory();
  }

  private async ensureLogDirectory() {
    if (this.config.file?.enabled) {
      try {
        await mkdir(this.config.file.path, { recursive: true });
      } catch (error) {
        console.error('Failed to create log directory:', error);
      }
    }
  }

  private shouldLog(level: keyof typeof this.logLevels): boolean {
    if (!this.config.enabled) return false;
    return this.logLevels[level] >= this.logLevels[this.config.level];
  }

  private formatMessage(entry: LogEntry): string {
    switch (this.config.format) {
      case 'json':
        return JSON.stringify(entry);
      
      case 'dev':
        const timestamp = new Date(entry.timestamp).toLocaleTimeString();
        const level = entry.level.toUpperCase().padEnd(5);
        const color = this.getColorCode(entry.level);
        const reset = '\x1b[0m';
        
        if (this.config.console?.colors) {
          return `${color}[${timestamp}] ${level}${reset} ${entry.message}`;
        }
        return `[${timestamp}] ${level} ${entry.message}`;
      
      case 'combined':
      default:
        const parts = [
          entry.timestamp,
          entry.level.toUpperCase(),
          entry.requestId ? `[${entry.requestId}]` : '',
          entry.method && entry.url ? `${entry.method} ${entry.url}` : '',
          entry.statusCode ? `${entry.statusCode}` : '',
          entry.processingTime ? `${entry.processingTime}ms` : '',
          entry.message
        ].filter(Boolean);
        
        return parts.join(' ');
    }
  }

  private getColorCode(level: string): string {
    const colors = {
      debug: '\x1b[36m', // Cyan
      info: '\x1b[32m',  // Green
      warn: '\x1b[33m',  // Yellow
      error: '\x1b[31m'  // Red
    };
    return colors[level as keyof typeof colors] || '\x1b[0m';
  }

  private async writeToFile(entry: LogEntry) {
    if (!this.config.file?.enabled) return;

    try {
      const logFile = join(this.config.file.path, `kilat-${entry.level}.log`);
      const logLine = this.formatMessage(entry) + '\n';
      
      await writeFile(logFile, logLine, { flag: 'a' });
      
      // TODO: Implement log rotation when file size exceeds maxSize
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private writeToConsole(entry: LogEntry) {
    if (!this.config.console?.enabled) return;

    const message = this.formatMessage(entry);
    
    switch (entry.level) {
      case 'error':
        console.error(message);
        if (entry.error?.stack) {
          console.error(entry.error.stack);
        }
        break;
      case 'warn':
        console.warn(message);
        break;
      case 'debug':
        console.debug(message);
        break;
      case 'info':
      default:
        console.log(message);
        break;
    }
  }

  private async log(level: keyof typeof this.logLevels, message: string, meta: Partial<LogEntry> = {}) {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      ...meta
    };

    // Write to console
    this.writeToConsole(entry);

    // Write to file
    await this.writeToFile(entry);
  }

  public debug(message: string, meta?: Partial<LogEntry>) {
    return this.log('debug', message, meta);
  }

  public info(message: string, meta?: Partial<LogEntry>) {
    return this.log('info', message, meta);
  }

  public warn(message: string, meta?: Partial<LogEntry>) {
    return this.log('warn', message, meta);
  }

  public error(message: string, error?: Error, meta?: Partial<LogEntry>) {
    return this.log('error', message, { ...meta, error });
  }

  // Request logging helper
  public request(req: any, res: any, processingTime: number) {
    const entry: Partial<LogEntry> = {
      requestId: req.kilat?.requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      processingTime,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection?.remoteAddress,
      userId: req.user?.id
    };

    const level = res.statusCode >= 400 ? 'warn' : 'info';
    const message = `${req.method} ${req.url} ${res.statusCode} ${processingTime}ms`;
    
    return this.log(level, message, entry);
  }

  // Performance logging
  public performance(operation: string, duration: number, meta?: Partial<LogEntry>) {
    const message = `Performance: ${operation} took ${duration}ms`;
    const level = duration > 1000 ? 'warn' : 'info';
    
    return this.log(level, message, { ...meta, processingTime: duration });
  }

  // Security logging
  public security(event: string, meta?: Partial<LogEntry>) {
    const message = `Security: ${event}`;
    return this.log('warn', message, meta);
  }

  // Database logging
  public database(query: string, duration: number, meta?: Partial<LogEntry>) {
    const message = `Database: ${query} (${duration}ms)`;
    const level = duration > 500 ? 'warn' : 'debug';
    
    return this.log(level, message, { ...meta, processingTime: duration });
  }
}

// Default logger instance
let defaultLogger: KilatLogger;

export function createLogger(config?: Partial<LoggerConfig>): KilatLogger {
  if (!defaultLogger || config) {
    defaultLogger = new KilatLogger(config);
  }
  return defaultLogger;
}

// Convenience functions using default logger
export const logger = {
  debug: (message: string, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.debug(message, meta);
  },
  
  info: (message: string, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.info(message, meta);
  },
  
  warn: (message: string, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.warn(message, meta);
  },
  
  error: (message: string, error?: Error, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.error(message, error, meta);
  },
  
  request: (req: any, res: any, processingTime: number) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.request(req, res, processingTime);
  },
  
  performance: (operation: string, duration: number, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.performance(operation, duration, meta);
  },
  
  security: (event: string, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.security(event, meta);
  },
  
  database: (query: string, duration: number, meta?: Partial<LogEntry>) => {
    if (!defaultLogger) defaultLogger = new KilatLogger();
    return defaultLogger.database(query, duration, meta);
  }
};

// Export types
export type { LogEntry };
