#!/usr/bin/env node

/**
 * Kilat.js CLI
 * Interactive command-line interface for Kilat.js framework
 */

import { program } from 'commander';
import chalk from 'chalk';
import updateNotifier from 'update-notifier';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const pkg = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));

// Check for updates
const notifier = updateNotifier({ pkg });
notifier.notify();

// Import commands
import { createCommand } from '../dist/commands/create.js';
import { devCommand } from '../dist/commands/dev.js';
import { buildCommand, buildWatchCommand } from '../dist/commands/build.js';

// CLI Header
console.log(chalk.cyan.bold(`
⚡ Kilat.js CLI v${pkg.version}
Framework Glow Futuristik Nusantara
`));

// Main program
program
  .name('kilat')
  .description('⚡ Kilat.js - Modern fullstack framework with glow effects')
  .version(pkg.version);

// 🎯 Create command
program
  .command('create [project-name]')
  .description('Create a new Kilat.js project')
  .option('-t, --template <template>', 'Project template')
  .option('--theme <theme>', 'UI theme (cyberpunk, nusantara, retro, etc.)')
  .option('--database <db>', 'Database type (sqlite, mysql, none)')
  .option('--plugins <plugins...>', 'Plugins to install')
  .option('--features <features...>', 'Features to enable')
  .option('-f, --force', 'Overwrite existing directory')
  .option('--dry', 'Dry run without installing dependencies')
  .option('-y, --yes', 'Skip interactive prompts')
  .action(createCommand);

// 🔧 Development command
program
  .command('dev')
  .description('Start development server with hot reload')
  .option('-p, --port <port>', 'Development server port', '3000')
  .option('-h, --host <host>', 'Development server host', 'localhost')
  .option('--no-open', 'Don\'t open browser automatically')
  .option('-v, --verbose', 'Verbose logging')
  .action(devCommand);

// 🏗️ Build command
program
  .command('build')
  .description('Build project for production')
  .option('--analyze', 'Analyze bundle size')
  .option('--watch', 'Watch mode for development builds')
  .option('-v, --verbose', 'Verbose logging')
  .action((options) => {
    if (options.watch) {
      buildWatchCommand(options);
    } else {
      buildCommand(options);
    }
  });

// 🚀 Start command
program
  .command('start')
  .description('Start production server')
  .option('-p, --port <port>', 'Server port', '3000')
  .option('-h, --host <host>', 'Server host', 'localhost')
  .action(async (options) => {
    console.log(chalk.cyan('🚀 Starting production server...'));
    // Implementation would start the built application
  });

// 🧪 Test command
program
  .command('test')
  .description('Run tests')
  .option('--watch', 'Watch mode')
  .option('--coverage', 'Generate coverage report')
  .option('--ui', 'Open test UI')
  .action(async (options) => {
    console.log(chalk.cyan('🧪 Running tests...'));
    // Implementation would run test suite
  });

// 🔍 Doctor command
program
  .command('doctor')
  .description('Diagnose project health and configuration')
  .option('--fix', 'Automatically fix issues')
  .action(async (options) => {
    console.log(chalk.cyan('🔍 Running project diagnostics...'));
    // Implementation would check project health
  });

// 🔌 Plugin commands
const pluginCmd = program
  .command('plugin')
  .description('Manage plugins');

pluginCmd
  .command('list')
  .description('List available plugins')
  .option('--installed', 'Show only installed plugins')
  .action(async (options) => {
    console.log(chalk.cyan('🔌 Listing plugins...'));
  });

pluginCmd
  .command('add <plugin>')
  .description('Install a plugin')
  .option('--dev', 'Install as dev dependency')
  .action(async (plugin, options) => {
    console.log(chalk.cyan(`🔌 Installing plugin: ${plugin}`));
  });

pluginCmd
  .command('remove <plugin>')
  .description('Remove a plugin')
  .action(async (plugin) => {
    console.log(chalk.cyan(`🔌 Removing plugin: ${plugin}`));
  });

// 🗄️ Database commands
const dbCmd = program
  .command('db')
  .description('Database management');

dbCmd
  .command('setup')
  .description('Setup database')
  .action(async () => {
    console.log(chalk.cyan('🗄️ Setting up database...'));
  });

dbCmd
  .command('migrate')
  .description('Run database migrations')
  .option('--rollback', 'Rollback last migration')
  .action(async (options) => {
    console.log(chalk.cyan('🗄️ Running migrations...'));
  });

dbCmd
  .command('seed')
  .description('Seed database with sample data')
  .action(async () => {
    console.log(chalk.cyan('🗄️ Seeding database...'));
  });

// 🎨 Generate commands
const generateCmd = program
  .command('generate')
  .alias('g')
  .description('Generate code scaffolds');

generateCmd
  .command('component <name>')
  .description('Generate a new component')
  .option('--theme <theme>', 'Component theme')
  .action(async (name, options) => {
    console.log(chalk.cyan(`🎨 Generating component: ${name}`));
  });

generateCmd
  .command('page <name>')
  .description('Generate a new page')
  .option('--layout <layout>', 'Page layout')
  .action(async (name, options) => {
    console.log(chalk.cyan(`🎨 Generating page: ${name}`));
  });

generateCmd
  .command('api <name>')
  .description('Generate API route')
  .action(async (name) => {
    console.log(chalk.cyan(`🎨 Generating API route: ${name}`));
  });

// 🚀 Deploy command
program
  .command('deploy')
  .description('Deploy to production')
  .option('--provider <provider>', 'Deployment provider (vercel, netlify, aws)')
  .option('--env <environment>', 'Target environment', 'production')
  .action(async (options) => {
    console.log(chalk.cyan('🚀 Deploying to production...'));
  });

// 📊 Analytics command
program
  .command('analyze')
  .description('Analyze project performance and bundle size')
  .option('--bundle', 'Analyze bundle size')
  .option('--performance', 'Performance analysis')
  .action(async (options) => {
    console.log(chalk.cyan('📊 Analyzing project...'));
  });

// 🔄 Update command
program
  .command('update')
  .description('Update Kilat.js and dependencies')
  .option('--check', 'Check for updates only')
  .option('--force', 'Force update all packages')
  .action(async (options) => {
    console.log(chalk.cyan('🔄 Checking for updates...'));
  });

// 📚 Docs command
program
  .command('docs')
  .description('Open documentation')
  .option('--local', 'Open local documentation')
  .action(async (options) => {
    const { default: open } = await import('open');
    const url = options.local 
      ? 'http://localhost:3001/docs' 
      : 'https://kilat-js.pcode.my.id/docs';
    
    console.log(chalk.cyan(`📚 Opening documentation: ${url}`));
    await open(url);
  });

// 🎯 Info command
program
  .command('info')
  .description('Display project and environment information')
  .action(async () => {
    console.log(chalk.cyan.bold('\n📋 Project Information\n'));
    
    // System info
    console.log(chalk.white('System:'));
    console.log(chalk.gray(`  OS: ${process.platform} ${process.arch}`));
    console.log(chalk.gray(`  Node: ${process.version}`));
    
    // Package manager
    try {
      const { detectPackageManager } = await import('../dist/utils/package-manager.js');
      const pm = await detectPackageManager();
      console.log(chalk.gray(`  Package Manager: ${pm}`));
    } catch (error) {
      console.log(chalk.gray('  Package Manager: unknown'));
    }

    // Project info
    try {
      const { loadKilatConfig } = await import('../dist/utils/config.js');
      const config = await loadKilatConfig();
      
      console.log(chalk.white('\nProject:'));
      console.log(chalk.gray(`  Name: ${config.name}`));
      console.log(chalk.gray(`  Platform: ${config.platform}`));
      console.log(chalk.gray(`  Theme: ${config.theme}`));
      console.log(chalk.gray(`  Database: ${config.database.driver}`));
    } catch (error) {
      console.log(chalk.yellow('\n⚠️  Not in a Kilat.js project directory'));
    }
    
    console.log();
  });

// Error handling
program.on('command:*', () => {
  console.error(chalk.red(`❌ Invalid command: ${program.args.join(' ')}`));
  console.log(chalk.gray('Run "kilat --help" for available commands'));
  process.exit(1);
});

// Global error handler
process.on('uncaughtException', (error) => {
  console.error(chalk.red.bold('\n💥 Unexpected error:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  console.log(chalk.gray('\nPlease report this issue: https://github.com/kangpcode/kilatjs/issues'));
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red.bold('\n💥 Unhandled promise rejection:'), reason);
  process.exit(1);
});

// Parse command line arguments
program.parse();

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
