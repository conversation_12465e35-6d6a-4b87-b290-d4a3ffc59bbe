{"name": "kilat-cli", "version": "1.0.0", "description": "⚡ Kilat CLI - Interactive command-line tools for Kilat.js framework", "type": "module", "main": "dist/index.js", "bin": {"kilat": "./bin/kilat.js"}, "files": ["dist", "bin", "templates", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --platform=node --external:inquirer --external:chalk --external:ora --external:fs-extra", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --platform=node --external:inquirer --external:chalk --external:ora --external:fs-extra", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run src/cli.ts", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "cli", "framework", "generator", "scaffold", "interactive", "developer-tools"], "author": "KangPCode", "license": "MIT", "dependencies": {"inquirer": "^9.2.0", "chalk": "^5.3.0", "ora": "^7.0.0", "fs-extra": "^11.2.0", "commander": "^11.1.0", "semver": "^7.5.0", "node-fetch": "^3.3.0", "tar": "^6.2.0", "degit": "^2.8.4", "execa": "^8.0.0", "listr2": "^7.0.0", "update-notifier": "^7.0.0"}, "devDependencies": {"@types/inquirer": "^9.0.0", "@types/fs-extra": "^11.0.0", "@types/semver": "^7.5.0", "@types/tar": "^6.1.0", "esbuild": "^0.19.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}