#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { createLogger } from 'kilat-utils';
import { KILAT_CLI_VERSION } from './index';

// Import commands
import { createCommand } from './commands/create';
import { devCommand } from './commands/dev';
import { buildCommand } from './commands/build';
import { doctorCommand } from './commands/doctor';
import { pluginCommand } from './commands/plugin';

/**
 * ⚡ Kilat CLI - Interactive command-line interface
 * Smart tools for Kilat.js development
 */

const logger = createLogger({ prefix: 'KilatCLI' });
const program = new Command();

// 🎨 Display banner
function displayBanner(): void {
  console.log(
    chalk.cyan(
      figlet.textSync('Kilat.js', {
        font: 'ANSI Shadow',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      })
    )
  );
  
  console.log(chalk.yellow('⚡ Lightning-fast fullstack framework from Nusantara'));
  console.log(chalk.gray(`Version ${KILAT_CLI_VERSION}\n`));
}

// 🚀 Setup CLI program
function setupCLI(): void {
  program
    .name('kilat')
    .description('⚡ Kilat.js CLI - Lightning-fast development tools')
    .version(KILAT_CLI_VERSION, '-v, --version', 'Display version number')
    .option('-d, --debug', 'Enable debug mode')
    .option('--no-banner', 'Disable banner display')
    .hook('preAction', (thisCommand) => {
      // Show banner unless disabled
      if (!thisCommand.opts().noBanner && process.env.NODE_ENV !== 'test') {
        displayBanner();
      }
      
      // Enable debug mode
      if (thisCommand.opts().debug) {
        process.env.DEBUG = 'kilat:*';
        logger.info('Debug mode enabled');
      }
    });

  // 📦 Create command - Generate new projects
  program
    .command('create')
    .alias('new')
    .description('🚀 Create a new Kilat.js project')
    .argument('[project-name]', 'Name of the project')
    .option('-t, --template <template>', 'Project template', 'default')
    .option('-p, --package-manager <pm>', 'Package manager (npm, yarn, pnpm, bun)', 'bun')
    .option('--typescript', 'Use TypeScript', true)
    .option('--no-git', 'Skip Git initialization')
    .option('--no-install', 'Skip dependency installation')
    .option('--theme <theme>', 'UI theme (cyberpunk, nusantara, minimalist)', 'cyberpunk')
    .option('--features <features...>', 'Additional features (auth, cms, payments, ai)')
    .action(createCommand);

  // 🔥 Dev command - Start development server
  program
    .command('dev')
    .alias('serve')
    .description('🔥 Start development server with HMR')
    .option('-p, --port <port>', 'Port number', '3000')
    .option('-h, --host <host>', 'Host address', 'localhost')
    .option('--https', 'Use HTTPS')
    .option('--open', 'Open browser automatically')
    .option('--backend', 'Start backend server', true)
    .option('--backend-port <port>', 'Backend port', '8080')
    .option('--watch', 'Watch for file changes', true)
    .option('--hmr', 'Enable Hot Module Replacement', true)
    .action(devCommand);

  // 📦 Build command - Build for production
  program
    .command('build')
    .description('📦 Build project for production')
    .option('-o, --outDir <dir>', 'Output directory', 'dist')
    .option('--analyze', 'Analyze bundle size')
    .option('--sourcemap', 'Generate source maps', true)
    .option('--minify', 'Minify output', true)
    .option('--gzip', 'Generate gzip files')
    .option('--brotli', 'Generate brotli files')
    .option('--ssr', 'Build for server-side rendering')
    .option('--prerender', 'Pre-render static pages')
    .option('--target <target>', 'Build target', 'es2020')
    .action(buildCommand);

  // 🏥 Doctor command - Health check and diagnostics
  program
    .command('doctor')
    .alias('check')
    .description('🏥 Run health checks and diagnostics')
    .option('--fix', 'Automatically fix issues')
    .option('--verbose', 'Verbose output')
    .option('--config', 'Check configuration')
    .option('--dependencies', 'Check dependencies')
    .option('--plugins', 'Check plugins')
    .option('--performance', 'Performance analysis')
    .action(doctorCommand);

  // 🔌 Plugin command - Manage plugins
  program
    .command('plugin')
    .description('🔌 Manage Kilat.js plugins')
    .argument('<action>', 'Action (list, install, uninstall, update, create)')
    .argument('[plugin-name]', 'Plugin name')
    .option('--dev', 'Install as dev dependency')
    .option('--global', 'Install globally')
    .option('--force', 'Force operation')
    .option('--template <template>', 'Plugin template for creation')
    .action(pluginCommand);

  // 🎨 Theme command - Manage themes
  program
    .command('theme')
    .description('🎨 Manage UI themes')
    .argument('<action>', 'Action (list, set, create, preview)')
    .argument('[theme-name]', 'Theme name')
    .option('--preview', 'Preview theme')
    .option('--custom', 'Create custom theme')
    .action(async (action: string, themeName?: string, options?: any) => {
      const { themeCommand } = await import('./commands/theme');
      await themeCommand(action, themeName, options);
    });

  // 🗃️ Database command - Database operations
  program
    .command('db')
    .description('🗃️ Database operations')
    .argument('<action>', 'Action (migrate, seed, reset, status)')
    .option('--env <env>', 'Environment', 'development')
    .option('--force', 'Force operation')
    .option('--dry-run', 'Show what would be done')
    .action(async (action: string, options?: any) => {
      const { dbCommand } = await import('./commands/db');
      await dbCommand(action, options);
    });

  // 🚀 Deploy command - Deployment operations
  program
    .command('deploy')
    .description('🚀 Deploy to various platforms')
    .argument('[platform]', 'Platform (vercel, netlify, aws, docker)')
    .option('--env <env>', 'Environment', 'production')
    .option('--build', 'Build before deploy', true)
    .option('--config <config>', 'Deployment config file')
    .action(async (platform?: string, options?: any) => {
      const { deployCommand } = await import('./commands/deploy');
      await deployCommand(platform, options);
    });

  // 🧪 Test command - Run tests
  program
    .command('test')
    .description('🧪 Run tests')
    .option('--watch', 'Watch mode')
    .option('--coverage', 'Generate coverage report')
    .option('--unit', 'Run unit tests only')
    .option('--e2e', 'Run e2e tests only')
    .option('--update-snapshots', 'Update snapshots')
    .action(async (options?: any) => {
      const { testCommand } = await import('./commands/test');
      await testCommand(options);
    });

  // 📊 Analytics command - Project analytics
  program
    .command('analytics')
    .description('📊 Project analytics and insights')
    .argument('[action]', 'Action (bundle, performance, dependencies)')
    .option('--output <format>', 'Output format (json, html, text)', 'text')
    .option('--save <file>', 'Save report to file')
    .action(async (action?: string, options?: any) => {
      const { analyticsCommand } = await import('./commands/analytics');
      await analyticsCommand(action, options);
    });

  // 🔧 Config command - Configuration management
  program
    .command('config')
    .description('🔧 Manage configuration')
    .argument('<action>', 'Action (get, set, list, reset)')
    .argument('[key]', 'Configuration key')
    .argument('[value]', 'Configuration value')
    .option('--global', 'Global configuration')
    .option('--json', 'JSON output')
    .action(async (action: string, key?: string, value?: string, options?: any) => {
      const { configCommand } = await import('./commands/config');
      await configCommand(action, key, value, options);
    });

  // 📚 Docs command - Documentation
  program
    .command('docs')
    .description('📚 Open documentation')
    .argument('[section]', 'Documentation section')
    .option('--local', 'Open local documentation')
    .option('--search <query>', 'Search documentation')
    .action(async (section?: string, options?: any) => {
      const { docsCommand } = await import('./commands/docs');
      await docsCommand(section, options);
    });

  // 🎯 Add global error handler
  program.exitOverride((err) => {
    if (err.code === 'commander.version') {
      console.log(chalk.cyan(`⚡ Kilat.js CLI v${KILAT_CLI_VERSION}`));
      process.exit(0);
    }
    
    if (err.code === 'commander.help') {
      process.exit(0);
    }
    
    logger.error('CLI Error:', err.message);
    process.exit(1);
  });

  // 🎯 Handle unknown commands
  program.on('command:*', (operands) => {
    console.error(chalk.red(`Unknown command: ${operands[0]}`));
    console.log(chalk.yellow('Run "kilat --help" to see available commands.'));
    process.exit(1);
  });
}

// 🚀 Main execution
async function main(): Promise<void> {
  try {
    setupCLI();
    
    // Parse command line arguments
    await program.parseAsync(process.argv);
    
  } catch (error) {
    logger.error('Unexpected error:', error);
    process.exit(1);
  }
}

// 🎯 Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 🚀 Start CLI if this file is executed directly
if (require.main === module) {
  main();
}

export { program, main };
