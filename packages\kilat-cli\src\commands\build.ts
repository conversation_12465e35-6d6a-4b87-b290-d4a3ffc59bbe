import chalk from 'chalk';
import ora from 'ora';
import { join } from 'path';
import { pathExists, remove, ensureDir, stat } from 'fs-extra';
import { execa } from 'execa';
import type { CommandOptions } from '../types';
import { loadKilatConfig } from '../utils/config';
import { formatBytes, formatDuration } from '../utils/format';

/**
 * Build command - Production build with optimization
 * Handles frontend, backend, and asset optimization
 */

export async function buildCommand(options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n⚡ Building Kilat.js Project for Production\n'));

  const startTime = Date.now();

  try {
    // Load configuration
    const config = await loadKilatConfig();
    
    // Clean previous build
    if (!options.watch) {
      await cleanBuildDirectory(config);
    }
    
    // Run build process
    await runBuildProcess(config, options);
    
    // Show build summary
    await showBuildSummary(config, startTime);
    
    console.log(chalk.green.bold('\n🎉 Build completed successfully!\n'));
    
  } catch (error) {
    console.error(chalk.red.bold('\n❌ Build failed:'), error.message);
    process.exit(1);
  }
}

// 🧹 Clean build directory
async function cleanBuildDirectory(config: any) {
  const spinner = ora('Cleaning build directory...').start();
  
  try {
    const outputDir = config.build?.outputDir || 'dist';
    
    if (await pathExists(outputDir)) {
      await remove(outputDir);
    }
    
    await ensureDir(outputDir);
    spinner.succeed('Build directory cleaned');
  } catch (error) {
    spinner.fail('Failed to clean build directory');
    throw error;
  }
}

// 🏗️ Run build process
async function runBuildProcess(config: any, options: CommandOptions) {
  const buildSteps = [];

  // Frontend build
  buildSteps.push({
    name: 'Frontend',
    command: getBuildCommand(config, 'frontend'),
    icon: '🌐'
  });

  // Backend build (if enabled)
  if (config.backend?.enabled) {
    buildSteps.push({
      name: 'Backend',
      command: getBuildCommand(config, 'backend'),
      icon: '🚀'
    });
  }

  // Assets optimization
  buildSteps.push({
    name: 'Assets',
    command: getBuildCommand(config, 'assets'),
    icon: '🎨'
  });

  // Run build steps
  for (const step of buildSteps) {
    await runBuildStep(step, config, options);
  }

  // Post-build optimizations
  if (options.analyze) {
    await analyzeBuild(config);
  }
}

// 🔧 Get build command for each step
function getBuildCommand(config: any, type: 'frontend' | 'backend' | 'assets'): string[] {
  const engine = config.build?.engine || 'kilatpack';

  switch (type) {
    case 'frontend':
      switch (engine) {
        case 'vite':
          return ['vite', 'build'];
        case 'webpack':
          return ['webpack', '--mode=production'];
        case 'kilatpack':
        default:
          return ['kilatpack', 'build', '--frontend'];
      }

    case 'backend':
      return ['kilatpack', 'build', '--backend'];

    case 'assets':
      return ['kilatpack', 'optimize', '--assets'];

    default:
      return ['echo', 'Unknown build type'];
  }
}

// ⚙️ Run individual build step
async function runBuildStep(step: any, config: any, options: CommandOptions) {
  const spinner = ora(`Building ${step.name}...`).start();
  const stepStartTime = Date.now();

  try {
    const result = await execa(step.command[0], step.command.slice(1), {
      stdio: options.verbose ? 'inherit' : 'pipe',
      cwd: process.cwd()
    });

    const duration = Date.now() - stepStartTime;
    spinner.succeed(`${step.icon} ${step.name} built (${formatDuration(duration)})`);

    // Show output in verbose mode
    if (options.verbose && result.stdout) {
      console.log(chalk.gray(result.stdout));
    }

  } catch (error) {
    spinner.fail(`${step.icon} ${step.name} build failed`);
    
    if (error.stdout) {
      console.log(chalk.red('\nStdout:'));
      console.log(error.stdout);
    }
    
    if (error.stderr) {
      console.log(chalk.red('\nStderr:'));
      console.log(error.stderr);
    }
    
    throw new Error(`${step.name} build failed: ${error.message}`);
  }
}

// 📊 Analyze build output
async function analyzeBuild(config: any) {
  const spinner = ora('Analyzing build...').start();

  try {
    // Run bundle analyzer
    await execa('kilatpack', ['analyze'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    spinner.succeed('Build analysis completed');
    console.log(chalk.cyan('\n📊 Bundle analysis saved to ./build-analysis.html'));
    
  } catch (error) {
    spinner.warn('Build analysis skipped (analyzer not available)');
  }
}

// 📋 Show build summary
async function showBuildSummary(config: any, startTime: number) {
  const totalDuration = Date.now() - startTime;
  const outputDir = config.build?.outputDir || 'dist';

  console.log(chalk.green.bold('\n📋 Build Summary'));
  console.log(chalk.green('================\n'));

  // Build time
  console.log(chalk.white(`⏱️  Total build time: ${formatDuration(totalDuration)}`));

  // Output directory info
  if (await pathExists(outputDir)) {
    const stats = await getBuildStats(outputDir);
    
    console.log(chalk.white(`📁 Output directory: ${outputDir}`));
    console.log(chalk.white(`📦 Total size: ${formatBytes(stats.totalSize)}`));
    console.log(chalk.white(`📄 Files created: ${stats.fileCount}`));

    // Show largest files
    if (stats.largestFiles.length > 0) {
      console.log(chalk.white('\n📊 Largest files:'));
      stats.largestFiles.slice(0, 5).forEach(file => {
        console.log(chalk.gray(`   ${file.name.padEnd(30)} ${formatBytes(file.size)}`));
      });
    }
  }

  // Performance recommendations
  showPerformanceRecommendations(config);

  // Next steps
  showNextSteps(config);
}

// 📊 Get build statistics
async function getBuildStats(outputDir: string) {
  const stats = {
    totalSize: 0,
    fileCount: 0,
    largestFiles: [] as Array<{ name: string; size: number }>
  };

  async function scanDirectory(dir: string, relativePath = '') {
    const { readdir } = await import('fs/promises');
    const entries = await readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = join(dir, entry.name);
      const relativeFilePath = join(relativePath, entry.name);

      if (entry.isDirectory()) {
        await scanDirectory(fullPath, relativeFilePath);
      } else {
        const fileStat = await stat(fullPath);
        stats.totalSize += fileStat.size;
        stats.fileCount++;

        stats.largestFiles.push({
          name: relativeFilePath,
          size: fileStat.size
        });
      }
    }
  }

  await scanDirectory(outputDir);

  // Sort largest files by size
  stats.largestFiles.sort((a, b) => b.size - a.size);

  return stats;
}

// 💡 Show performance recommendations
function showPerformanceRecommendations(config: any) {
  const recommendations = [];

  // Check if compression is enabled
  if (!config.performance?.compression) {
    recommendations.push('Enable compression (gzip/brotli) for smaller bundle sizes');
  }

  // Check if tree shaking is enabled
  if (!config.performance?.treeshaking) {
    recommendations.push('Enable tree shaking to remove unused code');
  }

  // Check if bundle splitting is enabled
  if (!config.performance?.bundleSplitting) {
    recommendations.push('Enable bundle splitting for better caching');
  }

  if (recommendations.length > 0) {
    console.log(chalk.yellow('\n💡 Performance Recommendations:'));
    recommendations.forEach(rec => {
      console.log(chalk.yellow(`   • ${rec}`));
    });
  }
}

// 🚀 Show next steps
function showNextSteps(config: any) {
  console.log(chalk.cyan('\n🚀 Next Steps:'));
  
  console.log(chalk.white('1. Test the production build:'));
  console.log(chalk.gray('   kilat preview\n'));
  
  console.log(chalk.white('2. Deploy to production:'));
  console.log(chalk.gray('   kilat deploy\n'));
  
  console.log(chalk.white('3. Monitor performance:'));
  console.log(chalk.gray('   kilat analyze\n'));

  // Platform-specific instructions
  if (config.platform === 'web') {
    console.log(chalk.white('4. Serve static files:'));
    console.log(chalk.gray('   Serve the dist/ directory with any static file server'));
  }

  if (config.platform === 'desktop') {
    console.log(chalk.white('4. Package desktop app:'));
    console.log(chalk.gray('   kilat package --platform desktop'));
  }

  if (config.platform === 'mobile') {
    console.log(chalk.white('4. Build mobile app:'));
    console.log(chalk.gray('   kilat build --platform mobile'));
  }
}

// 🔄 Watch mode for development builds
export async function buildWatchCommand(options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n⚡ Starting Kilat.js Build Watcher\n'));

  const config = await loadKilatConfig();
  
  console.log(chalk.yellow('🔄 Watching for file changes...\n'));

  // This would implement file watching and incremental builds
  // For now, we'll use the build engine's watch mode
  try {
    await execa('kilatpack', ['build', '--watch'], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
  } catch (error) {
    console.error(chalk.red('❌ Watch mode failed:'), error.message);
    process.exit(1);
  }
}
