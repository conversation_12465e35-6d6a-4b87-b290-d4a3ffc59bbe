import chalk from 'chalk';
import inquirer from 'inquirer';
import { promises as fs } from 'fs';
import { join } from 'path';
import { createLogger } from 'kilat-utils';
import { loadKilatConfig } from '../utils/config';
import { formatSuccess, formatError, formatInfo, formatWarning } from '../utils/format';

const logger = createLogger({ prefix: 'DBCommand' });

/**
 * 🗃️ Database operations command
 */
export async function dbCommand(action: string, options: any = {}): Promise<void> {
  try {
    switch (action) {
      case 'migrate':
        await runMigrations(options);
        break;
      case 'seed':
        await runSeeders(options);
        break;
      case 'reset':
        await resetDatabase(options);
        break;
      case 'status':
        await showStatus(options);
        break;
      case 'create':
        await createMigration(options);
        break;
      case 'rollback':
        await rollbackMigration(options);
        break;
      case 'fresh':
        await freshDatabase(options);
        break;
      default:
        console.log(formatError(`Unknown database action: ${action}`));
        console.log(formatInfo('Available actions: migrate, seed, reset, status, create, rollback, fresh'));
    }
  } catch (error) {
    logger.error('Database command failed:', error);
    console.log(formatError(`Database operation failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

// 🔄 Run migrations
async function runMigrations(options: any): Promise<void> {
  console.log(chalk.cyan('🔄 Running database migrations...\n'));

  const config = await loadKilatConfig();
  const dbConfig = config.database;

  if (!dbConfig) {
    throw new Error('Database configuration not found in kilat.config.ts');
  }

  // Check for pending migrations
  const migrationsDir = join(process.cwd(), 'database', 'migrations');
  const migrationFiles = await getMigrationFiles(migrationsDir);

  if (migrationFiles.length === 0) {
    console.log(formatInfo('No migrations found'));
    return;
  }

  // Show dry run if requested
  if (options.dryRun) {
    console.log(formatInfo('Dry run - showing migrations that would be executed:'));
    migrationFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
    return;
  }

  // Confirm if not forced
  if (!options.force) {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Run ${migrationFiles.length} migration(s)?`,
        default: true
      }
    ]);

    if (!confirm) {
      console.log(formatInfo('Migration cancelled'));
      return;
    }
  }

  // Execute migrations
  for (const file of migrationFiles) {
    try {
      console.log(chalk.gray(`Running: ${file}`));
      
      // Import and execute migration
      const migrationPath = join(migrationsDir, file);
      const migration = await import(migrationPath);
      
      if (migration.up) {
        await migration.up();
        console.log(formatSuccess(`✓ ${file}`));
      } else {
        console.log(formatWarning(`⚠ ${file} - No 'up' function found`));
      }
    } catch (error) {
      console.log(formatError(`✗ ${file} - ${(error as Error).message}`));
      throw error;
    }
  }

  console.log(formatSuccess(`\n🎉 Successfully ran ${migrationFiles.length} migration(s)`));
}

// 🌱 Run seeders
async function runSeeders(options: any): Promise<void> {
  console.log(chalk.cyan('🌱 Running database seeders...\n'));

  const seedersDir = join(process.cwd(), 'database', 'seeders');
  const seederFiles = await getSeederFiles(seedersDir);

  if (seederFiles.length === 0) {
    console.log(formatInfo('No seeders found'));
    return;
  }

  // Show dry run if requested
  if (options.dryRun) {
    console.log(formatInfo('Dry run - showing seeders that would be executed:'));
    seederFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
    return;
  }

  // Execute seeders
  for (const file of seederFiles) {
    try {
      console.log(chalk.gray(`Running: ${file}`));
      
      const seederPath = join(seedersDir, file);
      const seeder = await import(seederPath);
      
      if (seeder.run) {
        await seeder.run();
        console.log(formatSuccess(`✓ ${file}`));
      } else {
        console.log(formatWarning(`⚠ ${file} - No 'run' function found`));
      }
    } catch (error) {
      console.log(formatError(`✗ ${file} - ${(error as Error).message}`));
      throw error;
    }
  }

  console.log(formatSuccess(`\n🎉 Successfully ran ${seederFiles.length} seeder(s)`));
}

// 🔄 Reset database
async function resetDatabase(options: any): Promise<void> {
  console.log(chalk.red('🔄 Resetting database...\n'));

  if (!options.force) {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.red('⚠️  This will delete all data. Are you sure?'),
        default: false
      }
    ]);

    if (!confirm) {
      console.log(formatInfo('Reset cancelled'));
      return;
    }
  }

  // Drop all tables
  console.log(chalk.gray('Dropping all tables...'));
  
  // Rollback all migrations
  await rollbackAllMigrations();
  
  console.log(formatSuccess('✓ Database reset complete'));
}

// 📊 Show database status
async function showStatus(options: any): Promise<void> {
  console.log(chalk.cyan('📊 Database Status\n'));

  const config = await loadKilatConfig();
  const dbConfig = config.database;

  // Show configuration
  console.log(chalk.white.bold('Configuration:'));
  console.log(`  Driver: ${chalk.cyan(dbConfig?.driver || 'Not configured')}`);
  console.log(`  Environment: ${chalk.cyan(options.env)}`);
  
  if (dbConfig?.driver === 'sqlite') {
    console.log(`  Database file: ${chalk.cyan(dbConfig.connection?.sqlite?.file || 'data.db')}`);
  } else if (dbConfig?.driver === 'mysql') {
    const mysql = dbConfig.connection?.mysql;
    console.log(`  Host: ${chalk.cyan(mysql?.host || 'localhost')}`);
    console.log(`  Database: ${chalk.cyan(mysql?.database || 'kilat')}`);
  }

  // Show migration status
  console.log(chalk.white.bold('\nMigrations:'));
  const migrationsDir = join(process.cwd(), 'database', 'migrations');
  const migrationFiles = await getMigrationFiles(migrationsDir);
  
  if (migrationFiles.length === 0) {
    console.log(chalk.gray('  No migrations found'));
  } else {
    migrationFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${chalk.green('✓')} ${file}`);
    });
  }

  // Show seeder status
  console.log(chalk.white.bold('\nSeeders:'));
  const seedersDir = join(process.cwd(), 'database', 'seeders');
  const seederFiles = await getSeederFiles(seedersDir);
  
  if (seederFiles.length === 0) {
    console.log(chalk.gray('  No seeders found'));
  } else {
    seederFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
  }

  // Show tables (if possible)
  try {
    console.log(chalk.white.bold('\nTables:'));
    // This would require actual database connection
    console.log(chalk.gray('  Database connection required to show tables'));
  } catch (error) {
    console.log(chalk.gray('  Unable to connect to database'));
  }
}

// 📝 Create new migration
async function createMigration(options: any): Promise<void> {
  const { name } = await inquirer.prompt([
    {
      type: 'input',
      name: 'name',
      message: '📝 Migration name:',
      validate: (input) => input.trim() ? true : 'Migration name is required'
    }
  ]);

  const timestamp = new Date().toISOString().replace(/[-:T]/g, '').split('.')[0];
  const fileName = `${timestamp}_${name.toLowerCase().replace(/\s+/g, '_')}.ts`;
  
  const migrationTemplate = `import { DatabaseManager } from 'kilat-db';

/**
 * Migration: ${name}
 * Created: ${new Date().toISOString()}
 */

export async function up(db: DatabaseManager): Promise<void> {
  // Add your migration logic here
  // Example:
  // await db.query(\`
  //   CREATE TABLE users (
  //     id INTEGER PRIMARY KEY AUTOINCREMENT,
  //     name TEXT NOT NULL,
  //     email TEXT UNIQUE NOT NULL,
  //     created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  //   )
  // \`);
}

export async function down(db: DatabaseManager): Promise<void> {
  // Add your rollback logic here
  // Example:
  // await db.query('DROP TABLE IF EXISTS users');
}
`;

  const migrationsDir = join(process.cwd(), 'database', 'migrations');
  await fs.mkdir(migrationsDir, { recursive: true });
  
  const migrationPath = join(migrationsDir, fileName);
  await fs.writeFile(migrationPath, migrationTemplate, 'utf8');

  console.log(formatSuccess(`Migration created: ${fileName}`));
  console.log(formatInfo(`Path: ${migrationPath}`));
  console.log(formatInfo('Run "kilat db migrate" to execute this migration'));
}

// ⏪ Rollback migration
async function rollbackMigration(options: any): Promise<void> {
  console.log(chalk.yellow('⏪ Rolling back last migration...\n'));

  const migrationsDir = join(process.cwd(), 'database', 'migrations');
  const migrationFiles = await getMigrationFiles(migrationsDir);

  if (migrationFiles.length === 0) {
    console.log(formatInfo('No migrations to rollback'));
    return;
  }

  const lastMigration = migrationFiles[migrationFiles.length - 1];
  
  if (!options.force) {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Rollback migration: ${lastMigration}?`,
        default: false
      }
    ]);

    if (!confirm) {
      console.log(formatInfo('Rollback cancelled'));
      return;
    }
  }

  try {
    const migrationPath = join(migrationsDir, lastMigration);
    const migration = await import(migrationPath);
    
    if (migration.down) {
      await migration.down();
      console.log(formatSuccess(`✓ Rolled back: ${lastMigration}`));
    } else {
      console.log(formatWarning(`⚠ ${lastMigration} - No 'down' function found`));
    }
  } catch (error) {
    console.log(formatError(`✗ Rollback failed: ${(error as Error).message}`));
    throw error;
  }
}

// 🆕 Fresh database (reset + migrate + seed)
async function freshDatabase(options: any): Promise<void> {
  console.log(chalk.cyan('🆕 Creating fresh database...\n'));

  // Reset database
  await resetDatabase({ force: true });
  
  // Run migrations
  await runMigrations({ force: true });
  
  // Run seeders
  await runSeeders({ force: true });
  
  console.log(formatSuccess('\n🎉 Fresh database created successfully!'));
}

// 🔧 Helper functions
async function getMigrationFiles(dir: string): Promise<string[]> {
  try {
    const files = await fs.readdir(dir);
    return files
      .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
      .sort();
  } catch (error) {
    return [];
  }
}

async function getSeederFiles(dir: string): Promise<string[]> {
  try {
    const files = await fs.readdir(dir);
    return files
      .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
      .sort();
  } catch (error) {
    return [];
  }
}

async function rollbackAllMigrations(): Promise<void> {
  const migrationsDir = join(process.cwd(), 'database', 'migrations');
  const migrationFiles = await getMigrationFiles(migrationsDir);
  
  // Rollback in reverse order
  for (const file of migrationFiles.reverse()) {
    try {
      const migrationPath = join(migrationsDir, file);
      const migration = await import(migrationPath);
      
      if (migration.down) {
        await migration.down();
        console.log(formatSuccess(`✓ Rolled back: ${file}`));
      }
    } catch (error) {
      console.log(formatWarning(`⚠ Failed to rollback: ${file}`));
    }
  }
}
