import chalk from 'chalk';
import ora from 'ora';
import { spawn } from 'child_process';
import { join } from 'path';
import { pathExists, readFile } from 'fs-extra';
import type { CommandOptions } from '../types';
import { detectPackageManager } from '../utils/package-manager';
import { loadKilatConfig } from '../utils/config';
import { checkDependencies } from '../utils/validation';

/**
 * Dev command - Start development server with hot reload
 * Manages frontend, backend, and database services
 */

export async function devCommand(options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n⚡ Starting Kilat.js Development Server\n'));

  try {
    // Validate project
    await validateProject();
    
    // Load configuration
    const config = await loadKilatConfig();
    
    // Check dependencies
    await checkProjectDependencies();
    
    // Start development services
    await startDevelopmentServices(config, options);
    
  } catch (error) {
    console.error(chalk.red.bold('\n❌ Failed to start development server:'), error.message);
    process.exit(1);
  }
}

// 🔍 Validate project structure
async function validateProject() {
  const spinner = ora('Validating project structure...').start();
  
  try {
    // Check if we're in a Kilat.js project
    const configExists = await pathExists('./kilat.config.ts') || await pathExists('./kilat.config.js');
    if (!configExists) {
      throw new Error('Not a Kilat.js project. Run "kilat create" to create a new project.');
    }

    // Check package.json
    const packageJsonExists = await pathExists('./package.json');
    if (!packageJsonExists) {
      throw new Error('package.json not found. This doesn\'t appear to be a valid Node.js project.');
    }

    spinner.succeed('Project structure validated');
  } catch (error) {
    spinner.fail('Project validation failed');
    throw error;
  }
}

// 📦 Check project dependencies
async function checkProjectDependencies() {
  const spinner = ora('Checking dependencies...').start();
  
  try {
    const issues = await checkDependencies();
    
    if (issues.length > 0) {
      spinner.warn('Some dependencies have issues');
      console.log(chalk.yellow('\n⚠️  Dependency Issues:'));
      issues.forEach(issue => {
        console.log(chalk.yellow(`   • ${issue}`));
      });
      console.log(chalk.gray('\n   Run "kilat doctor" for detailed diagnostics\n'));
    } else {
      spinner.succeed('Dependencies checked');
    }
  } catch (error) {
    spinner.fail('Dependency check failed');
    throw error;
  }
}

// 🚀 Start development services
async function startDevelopmentServices(config: any, options: CommandOptions) {
  const services = [];
  
  // Frontend development server
  services.push({
    name: 'Frontend',
    command: 'vite',
    args: ['--host', options.host || 'localhost', '--port', (options.port || 3000).toString()],
    color: 'cyan',
    icon: '🌐'
  });

  // Backend server (if enabled)
  if (config.backend?.enabled) {
    services.push({
      name: 'Backend',
      command: 'kilat-backend',
      args: ['dev', '--port', (config.backend.port || 8080).toString()],
      color: 'green',
      icon: '🚀'
    });
  }

  // Database (if SQLite)
  if (config.database?.driver === 'sqlite') {
    services.push({
      name: 'Database',
      command: 'echo',
      args: ['SQLite database ready'],
      color: 'blue',
      icon: '🗄️'
    });
  }

  console.log(chalk.cyan('🔄 Starting development services...\n'));

  // Start all services
  const processes = [];
  
  for (const service of services) {
    const spinner = ora(`Starting ${service.name}...`).start();
    
    try {
      const process = spawn(service.command, service.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        cwd: process.cwd()
      });

      // Handle process output
      process.stdout?.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
          console.log(chalk[service.color](`${service.icon} [${service.name}] ${output}`));
        }
      });

      process.stderr?.on('data', (data) => {
        const output = data.toString().trim();
        if (output && !output.includes('warning')) {
          console.log(chalk.red(`${service.icon} [${service.name}] ${output}`));
        }
      });

      process.on('close', (code) => {
        if (code !== 0) {
          console.log(chalk.red(`${service.icon} [${service.name}] Process exited with code ${code}`));
        }
      });

      processes.push({ name: service.name, process });
      spinner.succeed(`${service.name} started`);
      
      // Small delay between service starts
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      spinner.fail(`Failed to start ${service.name}`);
      console.error(chalk.red(`Error: ${error.message}`));
    }
  }

  // Show development URLs
  showDevelopmentInfo(config, options);

  // Handle graceful shutdown
  setupGracefulShutdown(processes);

  // Keep the process alive
  await new Promise(() => {}); // Run indefinitely
}

// 📋 Show development information
function showDevelopmentInfo(config: any, options: CommandOptions) {
  const host = options.host || 'localhost';
  const port = options.port || 3000;
  const backendPort = config.backend?.port || 8080;

  console.log(chalk.green.bold('\n✅ Development server is ready!\n'));
  
  console.log(chalk.white('🌐 Frontend:'));
  console.log(chalk.cyan(`   Local:    http://${host}:${port}`));
  console.log(chalk.cyan(`   Network:  http://*************:${port}`)); // Would detect actual IP
  
  if (config.backend?.enabled) {
    console.log(chalk.white('\n🚀 Backend API:'));
    console.log(chalk.green(`   Local:    http://${host}:${backendPort}${config.backend.apiPrefix || '/api'}`));
    console.log(chalk.green(`   Health:   http://${host}:${backendPort}${config.backend.apiPrefix || '/api'}/health`));
  }

  if (config.database?.driver) {
    console.log(chalk.white('\n🗄️  Database:'));
    console.log(chalk.blue(`   Driver:   ${config.database.driver}`));
    if (config.database.driver === 'sqlite') {
      console.log(chalk.blue(`   File:     ${config.database.connection.sqlite?.file || './data.db'}`));
    }
  }

  console.log(chalk.white('\n🛠️  Development Tools:'));
  console.log(chalk.gray(`   Config:   kilat.config.ts`));
  console.log(chalk.gray(`   Logs:     .kilat/logs/`));
  console.log(chalk.gray(`   Doctor:   kilat doctor`));

  console.log(chalk.yellow('\n⚡ Press Ctrl+C to stop all services\n'));

  // Show helpful tips
  showDevelopmentTips(config);
}

// 💡 Show development tips
function showDevelopmentTips(config: any) {
  const tips = [
    '💡 Use "kilat generate component MyComponent" to create new components',
    '💡 Use "kilat db migrate" to run database migrations',
    '💡 Use "kilat plugin list" to see available plugins',
    '💡 Use "kilat build --analyze" to analyze bundle size'
  ];

  if (config.features?.includes('testing')) {
    tips.push('💡 Use "kilat test" to run your test suite');
  }

  if (config.plugins?.includes('cms')) {
    tips.push('💡 Access CMS admin at /admin');
  }

  console.log(chalk.cyan('💡 Development Tips:'));
  tips.slice(0, 3).forEach(tip => {
    console.log(chalk.gray(`   ${tip}`));
  });
  console.log();
}

// 🛑 Setup graceful shutdown
function setupGracefulShutdown(processes: Array<{ name: string; process: any }>) {
  const shutdown = async (signal: string) => {
    console.log(chalk.yellow(`\n🛑 Received ${signal}, shutting down gracefully...`));
    
    // Kill all child processes
    for (const { name, process } of processes) {
      try {
        console.log(chalk.gray(`   Stopping ${name}...`));
        process.kill('SIGTERM');
        
        // Force kill after 5 seconds
        setTimeout(() => {
          if (!process.killed) {
            process.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.log(chalk.red(`   Failed to stop ${name}: ${error.message}`));
      }
    }

    console.log(chalk.green('✅ All services stopped'));
    process.exit(0);
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  
  // Handle Windows Ctrl+C
  if (process.platform === 'win32') {
    const rl = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.on('SIGINT', () => {
      process.emit('SIGINT' as any);
    });
  }
}

// 🔄 Watch for file changes (if not using Vite's built-in watcher)
async function setupFileWatcher(config: any) {
  // This would implement custom file watching for non-Vite scenarios
  // For now, we rely on Vite's built-in HMR
}

// 🌐 Open browser automatically
async function openBrowser(url: string, options: CommandOptions) {
  if (options.open !== false) {
    try {
      const { default: open } = await import('open');
      await open(url);
      console.log(chalk.green(`🌐 Opened ${url} in browser`));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  Could not open browser automatically`));
      console.log(chalk.gray(`   Please open ${url} manually`));
    }
  }
}

// 📊 Show performance metrics
function showPerformanceMetrics() {
  // This would show real-time performance metrics
  // Like build time, memory usage, etc.
  setInterval(() => {
    const memUsage = process.memoryUsage();
    const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    
    if (memMB > 500) { // Show warning if memory usage is high
      console.log(chalk.yellow(`⚠️  High memory usage: ${memMB}MB`));
    }
  }, 30000); // Check every 30 seconds
}
