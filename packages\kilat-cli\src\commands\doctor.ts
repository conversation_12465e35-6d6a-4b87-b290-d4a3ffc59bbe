import chalk from 'chalk';
import ora from 'ora';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { execa } from 'execa';
import type { CommandOptions, DiagnosticResult, SystemInfo } from '../types';

/**
 * Doctor command - System diagnostics and health check
 * Analyzes the development environment and project configuration
 */

export async function doctorCommand(options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n🩺 Kilat.js Doctor - System Diagnostics\n'));

  const spinner = ora('Running diagnostics...').start();
  
  try {
    const results = await runDiagnostics();
    spinner.stop();
    
    displayResults(results);
    
    const hasErrors = results.some(r => r.status === 'error');
    const hasWarnings = results.some(r => r.status === 'warning');
    
    if (hasErrors) {
      console.log(chalk.red.bold('\n❌ Critical issues found! Please fix the errors above.'));
      process.exit(1);
    } else if (hasWarnings) {
      console.log(chalk.yellow.bold('\n⚠️  Some warnings found. Consider addressing them for optimal performance.'));
    } else {
      console.log(chalk.green.bold('\n✅ All checks passed! Your environment is ready for Kilat.js development.'));
    }
    
  } catch (error) {
    spinner.stop();
    console.error(chalk.red.bold('\n❌ Diagnostics failed:'), error.message);
    process.exit(1);
  }
}

// 🔍 Run all diagnostic checks
async function runDiagnostics(): Promise<DiagnosticResult[]> {
  const results: DiagnosticResult[] = [];
  
  // System checks
  results.push(await checkNodeVersion());
  results.push(await checkBunInstallation());
  results.push(await checkPackageManager());
  results.push(await checkGitInstallation());
  
  // Project checks (if in a Kilat project)
  if (isKilatProject()) {
    results.push(await checkKilatConfig());
    results.push(await checkDependencies());
    results.push(await checkProjectStructure());
    results.push(await checkEnvironmentFiles());
  }
  
  // Development tools
  results.push(await checkVSCodeExtensions());
  results.push(await checkPortAvailability());
  
  return results;
}

// 📊 Display diagnostic results
function displayResults(results: DiagnosticResult[]) {
  console.log(chalk.bold('\n📋 Diagnostic Results:\n'));
  
  results.forEach(result => {
    const icon = getStatusIcon(result.status);
    const color = getStatusColor(result.status);
    
    console.log(`${icon} ${chalk[color](result.name)}`);
    
    if (result.message) {
      console.log(`   ${chalk.gray(result.message)}`);
    }
    
    if (result.details) {
      result.details.forEach(detail => {
        console.log(`   ${chalk.gray('•')} ${chalk.gray(detail)}`);
      });
    }
    
    if (result.suggestion) {
      console.log(`   ${chalk.blue('💡 Suggestion:')} ${chalk.blue(result.suggestion)}`);
    }
    
    console.log();
  });
}

// 🎨 Helper functions for display
function getStatusIcon(status: string): string {
  switch (status) {
    case 'success': return '✅';
    case 'warning': return '⚠️';
    case 'error': return '❌';
    case 'info': return 'ℹ️';
    default: return '❓';
  }
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'success': return 'green';
    case 'warning': return 'yellow';
    case 'error': return 'red';
    case 'info': return 'blue';
    default: return 'gray';
  }
}

// 🔍 Individual diagnostic checks

async function checkNodeVersion(): Promise<DiagnosticResult> {
  try {
    const { stdout } = await execa('node', ['--version']);
    const version = stdout.trim();
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      return {
        name: 'Node.js Version',
        status: 'success',
        message: `${version} (✓ Compatible)`,
        details: ['Node.js 18+ is recommended for Kilat.js']
      };
    } else {
      return {
        name: 'Node.js Version',
        status: 'warning',
        message: `${version} (Outdated)`,
        suggestion: 'Update to Node.js 18+ for better performance and compatibility'
      };
    }
  } catch (error) {
    return {
      name: 'Node.js Version',
      status: 'error',
      message: 'Node.js not found',
      suggestion: 'Install Node.js from https://nodejs.org'
    };
  }
}

async function checkBunInstallation(): Promise<DiagnosticResult> {
  try {
    const { stdout } = await execa('bun', ['--version']);
    return {
      name: 'Bun Runtime',
      status: 'success',
      message: `v${stdout.trim()} (✓ Available)`,
      details: ['Bun provides faster package management and runtime']
    };
  } catch (error) {
    return {
      name: 'Bun Runtime',
      status: 'warning',
      message: 'Bun not found',
      suggestion: 'Install Bun for faster development: curl -fsSL https://bun.sh/install | bash'
    };
  }
}

async function checkPackageManager(): Promise<DiagnosticResult> {
  try {
    const managers = [];
    
    try {
      const { stdout } = await execa('npm', ['--version']);
      managers.push(`npm v${stdout.trim()}`);
    } catch {}
    
    try {
      const { stdout } = await execa('yarn', ['--version']);
      managers.push(`yarn v${stdout.trim()}`);
    } catch {}
    
    try {
      const { stdout } = await execa('pnpm', ['--version']);
      managers.push(`pnpm v${stdout.trim()}`);
    } catch {}
    
    if (managers.length > 0) {
      return {
        name: 'Package Managers',
        status: 'success',
        message: 'Available package managers found',
        details: managers
      };
    } else {
      return {
        name: 'Package Managers',
        status: 'error',
        message: 'No package managers found',
        suggestion: 'Install npm, yarn, or pnpm'
      };
    }
  } catch (error) {
    return {
      name: 'Package Managers',
      status: 'error',
      message: 'Failed to check package managers',
      suggestion: 'Ensure npm, yarn, or pnpm is installed'
    };
  }
}

async function checkGitInstallation(): Promise<DiagnosticResult> {
  try {
    const { stdout } = await execa('git', ['--version']);
    return {
      name: 'Git Version Control',
      status: 'success',
      message: stdout.trim(),
      details: ['Git is available for version control']
    };
  } catch (error) {
    return {
      name: 'Git Version Control',
      status: 'warning',
      message: 'Git not found',
      suggestion: 'Install Git for version control: https://git-scm.com'
    };
  }
}

function isKilatProject(): boolean {
  return existsSync(join(process.cwd(), 'kilat.config.ts')) ||
         existsSync(join(process.cwd(), 'kilat.config.js'));
}

async function checkKilatConfig(): Promise<DiagnosticResult> {
  const configPaths = [
    join(process.cwd(), 'kilat.config.ts'),
    join(process.cwd(), 'kilat.config.js')
  ];
  
  const configPath = configPaths.find(path => existsSync(path));
  
  if (configPath) {
    try {
      const content = readFileSync(configPath, 'utf-8');
      const hasTheme = content.includes('theme:');
      const hasDatabase = content.includes('database:');
      const hasPlugins = content.includes('plugins:');
      
      const features = [];
      if (hasTheme) features.push('Theme configuration');
      if (hasDatabase) features.push('Database configuration');
      if (hasPlugins) features.push('Plugin configuration');
      
      return {
        name: 'Kilat Configuration',
        status: 'success',
        message: 'Configuration file found',
        details: features.length > 0 ? features : ['Basic configuration detected']
      };
    } catch (error) {
      return {
        name: 'Kilat Configuration',
        status: 'warning',
        message: 'Configuration file exists but may have syntax errors',
        suggestion: 'Check your kilat.config.ts file for syntax errors'
      };
    }
  } else {
    return {
      name: 'Kilat Configuration',
      status: 'error',
      message: 'No kilat.config.ts found',
      suggestion: 'Create a kilat.config.ts file in your project root'
    };
  }
}

async function checkDependencies(): Promise<DiagnosticResult> {
  const packageJsonPath = join(process.cwd(), 'package.json');
  
  if (!existsSync(packageJsonPath)) {
    return {
      name: 'Project Dependencies',
      status: 'error',
      message: 'No package.json found',
      suggestion: 'Initialize your project with npm init or yarn init'
    };
  }
  
  try {
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const kilatPackages = Object.keys(deps).filter(dep => dep.startsWith('kilat-'));
    
    if (kilatPackages.length > 0) {
      return {
        name: 'Project Dependencies',
        status: 'success',
        message: `${kilatPackages.length} Kilat packages found`,
        details: kilatPackages
      };
    } else {
      return {
        name: 'Project Dependencies',
        status: 'warning',
        message: 'No Kilat packages found in dependencies',
        suggestion: 'Install Kilat packages: npm install kilat-core kilat-css'
      };
    }
  } catch (error) {
    return {
      name: 'Project Dependencies',
      status: 'error',
      message: 'Invalid package.json',
      suggestion: 'Fix syntax errors in package.json'
    };
  }
}

async function checkProjectStructure(): Promise<DiagnosticResult> {
  const expectedDirs = ['src', 'public'];
  const optionalDirs = ['pages', 'components', 'styles', 'api'];
  
  const existingDirs = expectedDirs.filter(dir => existsSync(join(process.cwd(), dir)));
  const existingOptional = optionalDirs.filter(dir => existsSync(join(process.cwd(), dir)));
  
  if (existingDirs.length === expectedDirs.length) {
    return {
      name: 'Project Structure',
      status: 'success',
      message: 'Standard project structure found',
      details: [...existingDirs, ...existingOptional].map(dir => `${dir}/ directory`)
    };
  } else {
    const missing = expectedDirs.filter(dir => !existingDirs.includes(dir));
    return {
      name: 'Project Structure',
      status: 'warning',
      message: 'Some standard directories missing',
      suggestion: `Create missing directories: ${missing.join(', ')}`
    };
  }
}

async function checkEnvironmentFiles(): Promise<DiagnosticResult> {
  const envFiles = ['.env', '.env.local', '.env.development', '.env.production'];
  const existingEnvFiles = envFiles.filter(file => existsSync(join(process.cwd(), file)));
  
  if (existingEnvFiles.length > 0) {
    return {
      name: 'Environment Files',
      status: 'success',
      message: 'Environment files found',
      details: existingEnvFiles
    };
  } else {
    return {
      name: 'Environment Files',
      status: 'info',
      message: 'No environment files found',
      suggestion: 'Create .env files for environment-specific configuration'
    };
  }
}

async function checkVSCodeExtensions(): Promise<DiagnosticResult> {
  const vscodeDir = join(process.cwd(), '.vscode');
  const extensionsFile = join(vscodeDir, 'extensions.json');
  
  if (existsSync(extensionsFile)) {
    return {
      name: 'VS Code Extensions',
      status: 'success',
      message: 'Recommended extensions configured',
      details: ['Extensions configuration found in .vscode/extensions.json']
    };
  } else {
    return {
      name: 'VS Code Extensions',
      status: 'info',
      message: 'No VS Code extensions configuration',
      suggestion: 'Create .vscode/extensions.json for recommended extensions'
    };
  }
}

async function checkPortAvailability(): Promise<DiagnosticResult> {
  const commonPorts = [3000, 3001, 8080, 8000];
  const availablePorts = [];
  
  for (const port of commonPorts) {
    try {
      const { createServer } = await import('net');
      const server = createServer();
      
      await new Promise((resolve, reject) => {
        server.listen(port, () => {
          server.close();
          availablePorts.push(port);
          resolve(true);
        });
        server.on('error', reject);
      });
    } catch {
      // Port is in use
    }
  }
  
  if (availablePorts.length > 0) {
    return {
      name: 'Port Availability',
      status: 'success',
      message: 'Development ports available',
      details: availablePorts.map(port => `Port ${port} is available`)
    };
  } else {
    return {
      name: 'Port Availability',
      status: 'warning',
      message: 'Common development ports are in use',
      suggestion: 'Stop other development servers or use different ports'
    };
  }
}
