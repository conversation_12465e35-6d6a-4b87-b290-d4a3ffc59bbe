import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { join } from 'path';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { execa } from 'execa';
import type { CommandOptions, KilatPlugin, PluginManifest } from '../types';

/**
 * Plugin command - Manage Kilat.js plugins
 * Install, remove, list, and configure plugins
 */

export async function pluginCommand(action?: string, pluginName?: string, options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n🔌 Kilat.js Plugin Manager\n'));

  if (!isKilatProject()) {
    console.error(chalk.red('❌ Not in a Kilat.js project directory'));
    process.exit(1);
  }

  try {
    switch (action) {
      case 'install':
      case 'add':
        await installPlugin(pluginName, options);
        break;
      case 'remove':
      case 'uninstall':
        await removePlugin(pluginName, options);
        break;
      case 'list':
        await listPlugins(options);
        break;
      case 'search':
        await searchPlugins(pluginName, options);
        break;
      case 'info':
        await showPluginInfo(pluginName, options);
        break;
      case 'enable':
        await togglePlugin(pluginName, true, options);
        break;
      case 'disable':
        await togglePlugin(pluginName, false, options);
        break;
      case 'update':
        await updatePlugin(pluginName, options);
        break;
      default:
        await showPluginMenu();
    }
  } catch (error) {
    console.error(chalk.red.bold('\n❌ Plugin operation failed:'), error.message);
    process.exit(1);
  }
}

// 📋 Show interactive plugin menu
async function showPluginMenu() {
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'What would you like to do?',
      choices: [
        { name: '📦 Install a plugin', value: 'install' },
        { name: '🗑️  Remove a plugin', value: 'remove' },
        { name: '📋 List installed plugins', value: 'list' },
        { name: '🔍 Search for plugins', value: 'search' },
        { name: '🔧 Configure plugins', value: 'configure' },
        { name: '🔄 Update plugins', value: 'update' }
      ]
    }
  ]);

  switch (action) {
    case 'install':
      await promptInstallPlugin();
      break;
    case 'remove':
      await promptRemovePlugin();
      break;
    case 'list':
      await listPlugins();
      break;
    case 'search':
      await promptSearchPlugins();
      break;
    case 'configure':
      await configurePlugins();
      break;
    case 'update':
      await promptUpdatePlugins();
      break;
  }
}

// 📦 Install plugin
async function installPlugin(pluginName?: string, options: CommandOptions = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName('install');
  }

  const spinner = ora(`Installing plugin: ${pluginName}`).start();

  try {
    // Check if plugin exists in registry
    const pluginInfo = await getPluginInfo(pluginName);
    
    if (!pluginInfo) {
      spinner.fail(`Plugin "${pluginName}" not found in registry`);
      return;
    }

    // Install npm package
    await execa('npm', ['install', pluginInfo.package], { cwd: process.cwd() });

    // Update kilat.config.ts
    await addPluginToConfig(pluginName, pluginInfo);

    spinner.succeed(`Plugin "${pluginName}" installed successfully`);
    
    console.log(chalk.green('\n✅ Plugin installed and configured!'));
    
    if (pluginInfo.postInstall) {
      console.log(chalk.blue('\n📝 Post-installation notes:'));
      console.log(chalk.gray(pluginInfo.postInstall));
    }

  } catch (error) {
    spinner.fail(`Failed to install plugin: ${error.message}`);
    throw error;
  }
}

// 🗑️ Remove plugin
async function removePlugin(pluginName?: string, options: CommandOptions = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName('remove');
  }

  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }

  const { confirm } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirm',
      message: `Are you sure you want to remove "${pluginName}"?`,
      default: false
    }
  ]);

  if (!confirm) {
    console.log(chalk.gray('Operation cancelled'));
    return;
  }

  const spinner = ora(`Removing plugin: ${pluginName}`).start();

  try {
    const pluginInfo = config.plugins[pluginName];
    
    // Remove from kilat.config.ts
    await removePluginFromConfig(pluginName);

    // Uninstall npm package
    if (pluginInfo.package) {
      await execa('npm', ['uninstall', pluginInfo.package], { cwd: process.cwd() });
    }

    spinner.succeed(`Plugin "${pluginName}" removed successfully`);

  } catch (error) {
    spinner.fail(`Failed to remove plugin: ${error.message}`);
    throw error;
  }
}

// 📋 List installed plugins
async function listPlugins(options: CommandOptions = {}) {
  const config = getKilatConfig();
  
  if (!config.plugins || Object.keys(config.plugins).length === 0) {
    console.log(chalk.yellow('No plugins installed'));
    return;
  }

  console.log(chalk.bold('📋 Installed Plugins:\n'));

  for (const [name, plugin] of Object.entries(config.plugins)) {
    const status = plugin.enabled !== false ? '✅ Enabled' : '❌ Disabled';
    const version = plugin.version || 'unknown';
    
    console.log(`${chalk.cyan(name)} ${chalk.gray(`(v${version})`)}`);
    console.log(`   Status: ${status}`);
    
    if (plugin.description) {
      console.log(`   ${chalk.gray(plugin.description)}`);
    }
    
    console.log();
  }
}

// 🔍 Search plugins
async function searchPlugins(query?: string, options: CommandOptions = {}) {
  if (!query) {
    const { searchQuery } = await inquirer.prompt([
      {
        type: 'input',
        name: 'searchQuery',
        message: 'Search for plugins:',
        validate: input => input.trim().length > 0 || 'Please enter a search term'
      }
    ]);
    query = searchQuery;
  }

  const spinner = ora(`Searching for plugins: ${query}`).start();

  try {
    const results = await searchPluginRegistry(query);
    spinner.stop();

    if (results.length === 0) {
      console.log(chalk.yellow(`No plugins found matching "${query}"`));
      return;
    }

    console.log(chalk.bold(`\n🔍 Search Results for "${query}":\n`));

    results.forEach(plugin => {
      console.log(`${chalk.cyan(plugin.name)} ${chalk.gray(`(v${plugin.version})`)}`);
      console.log(`   ${chalk.gray(plugin.description)}`);
      console.log(`   ${chalk.blue('Package:')} ${plugin.package}`);
      console.log(`   ${chalk.green('Downloads:')} ${plugin.downloads || 'N/A'}`);
      console.log();
    });

  } catch (error) {
    spinner.fail(`Search failed: ${error.message}`);
  }
}

// ℹ️ Show plugin info
async function showPluginInfo(pluginName?: string, options: CommandOptions = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName('info');
  }

  const spinner = ora(`Getting plugin info: ${pluginName}`).start();

  try {
    const info = await getPluginInfo(pluginName);
    spinner.stop();

    if (!info) {
      console.log(chalk.yellow(`Plugin "${pluginName}" not found`));
      return;
    }

    console.log(chalk.bold(`\nℹ️  Plugin Information: ${info.name}\n`));
    console.log(`${chalk.blue('Version:')} ${info.version}`);
    console.log(`${chalk.blue('Description:')} ${info.description}`);
    console.log(`${chalk.blue('Package:')} ${info.package}`);
    console.log(`${chalk.blue('Author:')} ${info.author || 'Unknown'}`);
    console.log(`${chalk.blue('License:')} ${info.license || 'Unknown'}`);
    
    if (info.homepage) {
      console.log(`${chalk.blue('Homepage:')} ${info.homepage}`);
    }
    
    if (info.repository) {
      console.log(`${chalk.blue('Repository:')} ${info.repository}`);
    }

    if (info.dependencies && info.dependencies.length > 0) {
      console.log(`${chalk.blue('Dependencies:')} ${info.dependencies.join(', ')}`);
    }

    if (info.features && info.features.length > 0) {
      console.log(`${chalk.blue('Features:')}`);
      info.features.forEach(feature => {
        console.log(`   • ${feature}`);
      });
    }

  } catch (error) {
    spinner.fail(`Failed to get plugin info: ${error.message}`);
  }
}

// 🔄 Toggle plugin enable/disable
async function togglePlugin(pluginName?: string, enabled: boolean, options: CommandOptions = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName(enabled ? 'enable' : 'disable');
  }

  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }

  config.plugins[pluginName].enabled = enabled;
  await updateKilatConfig(config);

  const action = enabled ? 'enabled' : 'disabled';
  console.log(chalk.green(`✅ Plugin "${pluginName}" ${action} successfully`));
}

// 🔄 Update plugin
async function updatePlugin(pluginName?: string, options: CommandOptions = {}) {
  if (!pluginName) {
    pluginName = await promptPluginName('update');
  }

  const config = getKilatConfig();
  if (!config.plugins || !config.plugins[pluginName]) {
    console.log(chalk.yellow(`Plugin "${pluginName}" is not installed`));
    return;
  }

  const spinner = ora(`Updating plugin: ${pluginName}`).start();

  try {
    const pluginInfo = config.plugins[pluginName];
    
    if (pluginInfo.package) {
      await execa('npm', ['update', pluginInfo.package], { cwd: process.cwd() });
    }

    // Get latest version info
    const latestInfo = await getPluginInfo(pluginName);
    if (latestInfo) {
      config.plugins[pluginName].version = latestInfo.version;
      await updateKilatConfig(config);
    }

    spinner.succeed(`Plugin "${pluginName}" updated successfully`);

  } catch (error) {
    spinner.fail(`Failed to update plugin: ${error.message}`);
    throw error;
  }
}

// 🛠️ Helper functions

function isKilatProject(): boolean {
  return existsSync(join(process.cwd(), 'kilat.config.ts')) ||
         existsSync(join(process.cwd(), 'kilat.config.js'));
}

function getKilatConfig(): any {
  const configPaths = [
    join(process.cwd(), 'kilat.config.ts'),
    join(process.cwd(), 'kilat.config.js')
  ];

  const configPath = configPaths.find(path => existsSync(path));
  if (!configPath) {
    throw new Error('kilat.config.ts not found');
  }

  // Simple config parsing (in real implementation, use proper TS/JS parser)
  const content = readFileSync(configPath, 'utf-8');
  
  // Mock config for demonstration
  return {
    plugins: {}
  };
}

async function updateKilatConfig(config: any): Promise<void> {
  const configPath = join(process.cwd(), 'kilat.config.ts');
  
  // In real implementation, properly update the config file
  // For now, just log the action
  console.log(chalk.gray('Config updated'));
}

async function addPluginToConfig(pluginName: string, pluginInfo: any): Promise<void> {
  const config = getKilatConfig();
  
  if (!config.plugins) {
    config.plugins = {};
  }
  
  config.plugins[pluginName] = {
    version: pluginInfo.version,
    enabled: true,
    package: pluginInfo.package
  };
  
  await updateKilatConfig(config);
}

async function removePluginFromConfig(pluginName: string): Promise<void> {
  const config = getKilatConfig();
  
  if (config.plugins && config.plugins[pluginName]) {
    delete config.plugins[pluginName];
    await updateKilatConfig(config);
  }
}

async function getPluginInfo(pluginName: string): Promise<any> {
  // Mock plugin registry
  const mockPlugins = {
    'auth': {
      name: 'auth',
      version: '1.0.0',
      description: 'Authentication and authorization plugin',
      package: 'kilat-plugin-auth',
      author: 'Kilat Team',
      features: ['JWT authentication', 'Role-based access', 'OAuth integration']
    },
    'cms': {
      name: 'cms',
      version: '0.9.1',
      description: 'Content management system plugin',
      package: 'kilat-plugin-cms',
      author: 'Kilat Team',
      features: ['Content editor', 'Media management', 'SEO optimization']
    }
  };
  
  return mockPlugins[pluginName] || null;
}

async function searchPluginRegistry(query: string): Promise<any[]> {
  // Mock search results
  const allPlugins = [
    {
      name: 'auth',
      version: '1.0.0',
      description: 'Authentication and authorization plugin',
      package: 'kilat-plugin-auth',
      downloads: '10k+'
    },
    {
      name: 'cms',
      version: '0.9.1',
      description: 'Content management system plugin',
      package: 'kilat-plugin-cms',
      downloads: '5k+'
    },
    {
      name: 'payments',
      version: '1.2.0',
      description: 'Payment processing plugin',
      package: 'kilat-plugin-payments',
      downloads: '8k+'
    }
  ];
  
  return allPlugins.filter(plugin => 
    plugin.name.includes(query.toLowerCase()) ||
    plugin.description.toLowerCase().includes(query.toLowerCase())
  );
}

async function promptPluginName(action: string): Promise<string> {
  const { pluginName } = await inquirer.prompt([
    {
      type: 'input',
      name: 'pluginName',
      message: `Enter plugin name to ${action}:`,
      validate: input => input.trim().length > 0 || 'Please enter a plugin name'
    }
  ]);
  
  return pluginName;
}

async function promptInstallPlugin(): Promise<void> {
  const { searchFirst } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'searchFirst',
      message: 'Would you like to search for plugins first?',
      default: true
    }
  ]);

  if (searchFirst) {
    await promptSearchPlugins();
  }

  const pluginName = await promptPluginName('install');
  await installPlugin(pluginName);
}

async function promptRemovePlugin(): Promise<void> {
  const config = getKilatConfig();
  const installedPlugins = Object.keys(config.plugins || {});

  if (installedPlugins.length === 0) {
    console.log(chalk.yellow('No plugins installed'));
    return;
  }

  const { pluginName } = await inquirer.prompt([
    {
      type: 'list',
      name: 'pluginName',
      message: 'Select plugin to remove:',
      choices: installedPlugins
    }
  ]);

  await removePlugin(pluginName);
}

async function promptSearchPlugins(): Promise<void> {
  const { query } = await inquirer.prompt([
    {
      type: 'input',
      name: 'query',
      message: 'Search for plugins:',
      validate: input => input.trim().length > 0 || 'Please enter a search term'
    }
  ]);

  await searchPlugins(query);
}

async function promptUpdatePlugins(): Promise<void> {
  const config = getKilatConfig();
  const installedPlugins = Object.keys(config.plugins || {});

  if (installedPlugins.length === 0) {
    console.log(chalk.yellow('No plugins installed'));
    return;
  }

  const { plugins } = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'plugins',
      message: 'Select plugins to update:',
      choices: installedPlugins.map(name => ({ name, checked: true }))
    }
  ]);

  for (const pluginName of plugins) {
    await updatePlugin(pluginName);
  }
}

async function configurePlugins(): Promise<void> {
  console.log(chalk.blue('🔧 Plugin configuration is managed in kilat.config.ts'));
  console.log(chalk.gray('Edit your configuration file to customize plugin settings.'));
}
