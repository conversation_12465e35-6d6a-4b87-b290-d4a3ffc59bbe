import chalk from 'chalk';
import inquirer from 'inquirer';
import { promises as fs } from 'fs';
import { join } from 'path';
import { createLogger } from 'kilat-utils';
import { loadKilatConfig, saveKilatConfig } from '../utils/config';
import { formatSuccess, formatError, formatInfo } from '../utils/format';

const logger = createLogger({ prefix: 'ThemeCommand' });

// 🎨 Available themes
const AVAILABLE_THEMES = [
  {
    name: 'cyberpunk',
    description: '🌆 Futuristic cyberpunk with neon effects',
    colors: ['#00ffff', '#ff00ff', '#ffff00'],
    preview: '⚡ Neon glow effects with dark backgrounds'
  },
  {
    name: 'nusantara',
    description: '🏝️ Indonesian archipelago with traditional elements',
    colors: ['#8B4513', '#FFD700', '#50C878'],
    preview: '🌺 Warm earth tones with batik patterns'
  },
  {
    name: 'minimalist',
    description: '⚪ Clean and simple design',
    colors: ['#000000', '#ffffff', '#666666'],
    preview: '🎯 Simple, elegant, and focused'
  },
  {
    name: 'retro',
    description: '📼 80s retro vibes',
    colors: ['#ff6b35', '#004e89', '#ffd23f'],
    preview: '🕹️ Nostalgic 80s aesthetic'
  },
  {
    name: 'aurora',
    description: '🌌 Northern lights inspired',
    colors: ['#00ff88', '#0088ff', '#8800ff'],
    preview: '✨ Ethereal aurora effects'
  }
];

/**
 * 🎨 Theme management command
 */
export async function themeCommand(
  action: string,
  themeName?: string,
  options: any = {}
): Promise<void> {
  try {
    switch (action) {
      case 'list':
        await listThemes();
        break;
      case 'set':
        await setTheme(themeName, options);
        break;
      case 'create':
        await createTheme(themeName, options);
        break;
      case 'preview':
        await previewTheme(themeName, options);
        break;
      case 'export':
        await exportTheme(themeName, options);
        break;
      case 'import':
        await importTheme(themeName, options);
        break;
      default:
        console.log(formatError(`Unknown theme action: ${action}`));
        console.log(formatInfo('Available actions: list, set, create, preview, export, import'));
    }
  } catch (error) {
    logger.error('Theme command failed:', error);
    console.log(formatError(`Theme operation failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

// 📋 List available themes
async function listThemes(): Promise<void> {
  console.log(chalk.cyan.bold('\n🎨 Available Themes:\n'));

  for (const theme of AVAILABLE_THEMES) {
    console.log(chalk.white.bold(`  ${theme.name}`));
    console.log(chalk.gray(`    ${theme.description}`));
    console.log(chalk.gray(`    ${theme.preview}`));
    
    // Show color palette
    const colorBar = theme.colors.map(color => chalk.hex(color)('█')).join('');
    console.log(`    Colors: ${colorBar}\n`);
  }

  // Show current theme
  try {
    const config = await loadKilatConfig();
    if (config.theme) {
      console.log(formatInfo(`Current theme: ${chalk.cyan(config.theme)}`));
    }
  } catch (error) {
    console.log(formatInfo('No theme configured yet'));
  }
}

// 🎯 Set theme
async function setTheme(themeName?: string, options: any = {}): Promise<void> {
  let selectedTheme = themeName;

  // Interactive theme selection if not provided
  if (!selectedTheme) {
    const { theme } = await inquirer.prompt([
      {
        type: 'list',
        name: 'theme',
        message: '🎨 Select a theme:',
        choices: AVAILABLE_THEMES.map(t => ({
          name: `${t.name} - ${t.description}`,
          value: t.name
        }))
      }
    ]);
    selectedTheme = theme;
  }

  // Validate theme
  const theme = AVAILABLE_THEMES.find(t => t.name === selectedTheme);
  if (!theme) {
    throw new Error(`Theme "${selectedTheme}" not found`);
  }

  // Update configuration
  try {
    const config = await loadKilatConfig();
    config.theme = selectedTheme;
    await saveKilatConfig(config);

    console.log(formatSuccess(`Theme set to "${selectedTheme}"`));
    console.log(formatInfo('Restart your development server to see changes'));

    // Show preview if requested
    if (options.preview) {
      await previewTheme(selectedTheme);
    }

  } catch (error) {
    throw new Error(`Failed to update configuration: ${(error as Error).message}`);
  }
}

// 👀 Preview theme
async function previewTheme(themeName?: string, options: any = {}): Promise<void> {
  let selectedTheme = themeName;

  if (!selectedTheme) {
    const { theme } = await inquirer.prompt([
      {
        type: 'list',
        name: 'theme',
        message: '👀 Select theme to preview:',
        choices: AVAILABLE_THEMES.map(t => ({
          name: t.name,
          value: t.name
        }))
      }
    ]);
    selectedTheme = theme;
  }

  const theme = AVAILABLE_THEMES.find(t => t.name === selectedTheme);
  if (!theme) {
    throw new Error(`Theme "${selectedTheme}" not found`);
  }

  // Display theme preview
  console.log(chalk.cyan.bold(`\n🎨 Theme Preview: ${theme.name}\n`));
  console.log(chalk.white(`Description: ${theme.description}`));
  console.log(chalk.white(`Preview: ${theme.preview}`));
  
  // Color palette
  console.log(chalk.white('\nColor Palette:'));
  theme.colors.forEach((color, index) => {
    const colorName = ['Primary', 'Secondary', 'Accent'][index] || `Color ${index + 1}`;
    console.log(`  ${chalk.hex(color)('█')} ${colorName}: ${color}`);
  });

  // Sample components
  console.log(chalk.white('\nSample Components:'));
  console.log(`  ${chalk.hex(theme.colors[0]).bold('Button Primary')}`);
  console.log(`  ${chalk.hex(theme.colors[1])('Button Secondary')}`);
  console.log(`  ${chalk.hex(theme.colors[2])('Accent Text')}`);

  // Generate preview HTML if requested
  if (options.html) {
    await generateHTMLPreview(theme);
  }
}

// 🎨 Create custom theme
async function createTheme(themeName?: string, options: any = {}): Promise<void> {
  let name = themeName;

  if (!name) {
    const { themeName: inputName } = await inquirer.prompt([
      {
        type: 'input',
        name: 'themeName',
        message: '🎨 Enter theme name:',
        validate: (input) => {
          if (!input.trim()) return 'Theme name is required';
          if (AVAILABLE_THEMES.find(t => t.name === input)) {
            return 'Theme name already exists';
          }
          return true;
        }
      }
    ]);
    name = inputName;
  }

  // Collect theme details
  const answers = await inquirer.prompt([
    {
      type: 'input',
      name: 'description',
      message: '📝 Enter theme description:',
      default: `Custom ${name} theme`
    },
    {
      type: 'input',
      name: 'primaryColor',
      message: '🎨 Primary color (hex):',
      default: '#007acc',
      validate: (input) => /^#[0-9A-Fa-f]{6}$/.test(input) || 'Invalid hex color'
    },
    {
      type: 'input',
      name: 'secondaryColor',
      message: '🎨 Secondary color (hex):',
      default: '#6c757d',
      validate: (input) => /^#[0-9A-Fa-f]{6}$/.test(input) || 'Invalid hex color'
    },
    {
      type: 'input',
      name: 'accentColor',
      message: '🎨 Accent color (hex):',
      default: '#28a745',
      validate: (input) => /^#[0-9A-Fa-f]{6}$/.test(input) || 'Invalid hex color'
    },
    {
      type: 'list',
      name: 'mode',
      message: '🌙 Default mode:',
      choices: ['dark', 'light'],
      default: 'dark'
    }
  ]);

  // Generate theme CSS
  const themeCSS = generateThemeCSS(name, answers);
  
  // Save theme file
  const themePath = join(process.cwd(), 'src', 'styles', 'themes', `${name}.css`);
  await fs.mkdir(join(process.cwd(), 'src', 'styles', 'themes'), { recursive: true });
  await fs.writeFile(themePath, themeCSS, 'utf8');

  console.log(formatSuccess(`Custom theme "${name}" created successfully!`));
  console.log(formatInfo(`Theme file: ${themePath}`));
  console.log(formatInfo(`Use "kilat theme set ${name}" to apply this theme`));
}

// 📤 Export theme
async function exportTheme(themeName?: string, options: any = {}): Promise<void> {
  if (!themeName) {
    throw new Error('Theme name is required for export');
  }

  const theme = AVAILABLE_THEMES.find(t => t.name === themeName);
  if (!theme) {
    throw new Error(`Theme "${themeName}" not found`);
  }

  const exportData = {
    name: theme.name,
    description: theme.description,
    colors: theme.colors,
    version: '1.0.0',
    author: 'Kilat.js',
    exportedAt: new Date().toISOString()
  };

  const exportPath = join(process.cwd(), `${themeName}-theme.json`);
  await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2), 'utf8');

  console.log(formatSuccess(`Theme "${themeName}" exported to ${exportPath}`));
}

// 📥 Import theme
async function importTheme(filePath?: string, options: any = {}): Promise<void> {
  if (!filePath) {
    throw new Error('Theme file path is required for import');
  }

  try {
    const themeData = JSON.parse(await fs.readFile(filePath, 'utf8'));
    
    // Validate theme data
    if (!themeData.name || !themeData.colors) {
      throw new Error('Invalid theme file format');
    }

    // Generate CSS from imported theme
    const themeCSS = generateThemeCSS(themeData.name, {
      description: themeData.description,
      primaryColor: themeData.colors[0],
      secondaryColor: themeData.colors[1],
      accentColor: themeData.colors[2],
      mode: 'dark'
    });

    // Save imported theme
    const themePath = join(process.cwd(), 'src', 'styles', 'themes', `${themeData.name}.css`);
    await fs.mkdir(join(process.cwd(), 'src', 'styles', 'themes'), { recursive: true });
    await fs.writeFile(themePath, themeCSS, 'utf8');

    console.log(formatSuccess(`Theme "${themeData.name}" imported successfully!`));
    console.log(formatInfo(`Use "kilat theme set ${themeData.name}" to apply this theme`));

  } catch (error) {
    throw new Error(`Failed to import theme: ${(error as Error).message}`);
  }
}

// 🎨 Generate theme CSS
function generateThemeCSS(name: string, config: any): string {
  return `/**
 * ${name} Theme for Kilat.js
 * ${config.description}
 */

[data-kilat-theme="${name}"] {
  /* Primary Colors */
  --k-primary: ${config.primaryColor};
  --k-secondary: ${config.secondaryColor};
  --k-accent: ${config.accentColor};
  
  /* Background Colors */
  --k-background: ${config.mode === 'dark' ? '#000011' : '#ffffff'};
  --k-surface: ${config.mode === 'dark' ? '#001122' : '#f8f9fa'};
  
  /* Text Colors */
  --k-text: ${config.mode === 'dark' ? '#ffffff' : '#000000'};
  --k-text-muted: ${config.mode === 'dark' ? '#888888' : '#666666'};
  
  /* Border Colors */
  --k-border: ${config.mode === 'dark' ? '#333333' : '#e1e5e9'};
  
  /* Semantic Colors */
  --k-success: #28a745;
  --k-warning: #ffc107;
  --k-error: #dc3545;
  --k-info: #17a2b8;
}

/* Theme-specific components */
[data-kilat-theme="${name}"] .k-btn-primary {
  background: var(--k-primary);
  color: var(--k-background);
  border: 1px solid var(--k-primary);
}

[data-kilat-theme="${name}"] .k-btn-secondary {
  background: var(--k-secondary);
  color: var(--k-background);
  border: 1px solid var(--k-secondary);
}

[data-kilat-theme="${name}"] .k-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  color: var(--k-text);
}
`;
}

// 🌐 Generate HTML preview
async function generateHTMLPreview(theme: any): Promise<void> {
  const html = `<!DOCTYPE html>
<html lang="en" data-kilat-theme="${theme.name}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${theme.name} Theme Preview</title>
  <style>
    ${generateThemeCSS(theme.name, {
      primaryColor: theme.colors[0],
      secondaryColor: theme.colors[1],
      accentColor: theme.colors[2],
      mode: 'dark'
    })}
  </style>
</head>
<body>
  <div class="k-container k-p-8">
    <h1 class="k-text-4xl k-font-bold k-mb-6">${theme.name} Theme Preview</h1>
    <p class="k-text-lg k-mb-8">${theme.description}</p>
    
    <div class="k-grid k-grid-cols-3 k-gap-4">
      <button class="k-btn k-btn-primary">Primary Button</button>
      <button class="k-btn k-btn-secondary">Secondary Button</button>
      <button class="k-btn k-btn-accent">Accent Button</button>
    </div>
    
    <div class="k-card k-p-6 k-mt-8">
      <h2 class="k-text-2xl k-font-semibold k-mb-4">Sample Card</h2>
      <p class="k-text-muted">This is a sample card component with the ${theme.name} theme applied.</p>
    </div>
  </div>
</body>
</html>`;

  const previewPath = join(process.cwd(), `${theme.name}-preview.html`);
  await fs.writeFile(previewPath, html, 'utf8');
  
  console.log(formatSuccess(`HTML preview generated: ${previewPath}`));
  console.log(formatInfo('Open this file in your browser to see the theme preview'));
}
