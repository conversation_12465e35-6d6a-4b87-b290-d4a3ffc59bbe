import { join, resolve } from 'path';
import { existsSync, mkdirSync, writeFileSync, readFileSync, copyFileSync } from 'fs';
import { createLogger } from 'kilat-utils';
import type { 
  ProjectTemplate, 
  CreateOptions, 
  ProjectInfo,
  ComponentOptions,
  PageOptions,
  APIOptions,
  MiddlewareOptions,
  ModelOptions
} from '../types';

/**
 * 🏗️ ProjectGenerator - Generates project structure and files
 * Handles scaffolding for new projects and adding components
 */
export class ProjectGenerator {
  private logger = createLogger({ prefix: 'ProjectGenerator' });
  private templatesDir: string;

  constructor() {
    this.templatesDir = join(__dirname, '../templates');
  }

  // 🚀 Generate complete project
  async generateProject(
    projectName: string, 
    template: ProjectTemplate, 
    options: CreateOptions
  ): Promise<void> {
    const projectPath = resolve(process.cwd(), projectName);

    try {
      // Create project directory
      if (options.force && existsSync(projectPath)) {
        await this.removeDirectory(projectPath);
      }
      
      mkdirSync(projectPath, { recursive: true });

      // Generate base structure
      await this.generateBaseStructure(projectPath, template, options);

      // Generate package.json
      await this.generatePackageJson(projectPath, projectName, template, options);

      // Generate configuration files
      await this.generateConfigFiles(projectPath, template, options);

      // Generate source files
      await this.generateSourceFiles(projectPath, template, options);

      // Generate documentation
      await this.generateDocumentation(projectPath, template, options);

      this.logger.success(`Project "${projectName}" generated successfully`);

    } catch (error) {
      this.logger.error('Failed to generate project:', error);
      throw error;
    }
  }

  // 📁 Generate base directory structure
  private async generateBaseStructure(
    projectPath: string, 
    template: ProjectTemplate, 
    options: CreateOptions
  ): Promise<void> {
    const directories = [
      'src',
      'src/components',
      'src/pages',
      'src/layouts',
      'src/hooks',
      'src/utils',
      'src/types',
      'src/styles',
      'public',
      'docs'
    ];

    // Add template-specific directories
    if (template.features.includes('backend')) {
      directories.push(
        'src/api',
        'src/middleware',
        'src/models',
        'src/services',
        'src/database',
        'src/database/migrations',
        'src/database/seeds'
      );
    }

    if (template.features.includes('testing')) {
      directories.push(
        'tests',
        'tests/unit',
        'tests/integration',
        'tests/e2e'
      );
    }

    if (options.docker) {
      directories.push('.docker');
    }

    // Create directories
    directories.forEach(dir => {
      const fullPath = join(projectPath, dir);
      mkdirSync(fullPath, { recursive: true });
    });
  }

  // 📦 Generate package.json
  private async generatePackageJson(
    projectPath: string,
    projectName: string,
    template: ProjectTemplate,
    options: CreateOptions
  ): Promise<void> {
    const packageJson = {
      name: projectName,
      version: '0.1.0',
      description: `A Kilat.js project using ${template.name} template`,
      type: 'module',
      scripts: {
        dev: 'kilat dev',
        build: 'kilat build',
        start: 'kilat start',
        preview: 'kilat preview',
        lint: 'eslint src --ext .ts,.tsx,.js,.jsx',
        'lint:fix': 'eslint src --ext .ts,.tsx,.js,.jsx --fix',
        format: 'prettier --write src/**/*.{ts,tsx,js,jsx,css,md}',
        'type-check': 'tsc --noEmit',
        test: 'vitest',
        'test:ui': 'vitest --ui',
        'test:coverage': 'vitest --coverage'
      },
      dependencies: {
        'kilat-core': '^1.0.0',
        'kilat-ui': '^1.0.0',
        'kilat-utils': '^1.0.0',
        'kilat-router': '^1.0.0',
        'kilatcss': '^1.0.0',
        react: '^18.2.0',
        'react-dom': '^18.2.0'
      },
      devDependencies: {
        'kilat-cli': '^1.0.0',
        'kilatpack': '^1.0.0',
        '@types/react': '^18.2.0',
        '@types/react-dom': '^18.2.0',
        typescript: '^5.0.0',
        vite: '^4.4.0',
        vitest: '^0.34.0'
      },
      keywords: ['kilat', 'react', 'typescript', 'fullstack'],
      author: '',
      license: 'MIT'
    };

    // Add template-specific dependencies
    if (template.features.includes('backend')) {
      packageJson.dependencies['kilat-backend'] = '^1.0.0';
      packageJson.dependencies['kilat-database'] = '^1.0.0';
    }

    if (options.database === 'postgresql') {
      packageJson.dependencies.pg = '^8.11.0';
      packageJson.devDependencies['@types/pg'] = '^8.10.0';
    } else if (options.database === 'mysql') {
      packageJson.dependencies.mysql2 = '^3.6.0';
    } else if (options.database === 'mongodb') {
      packageJson.dependencies.mongoose = '^7.5.0';
    }

    if (options.auth) {
      packageJson.dependencies['@auth/core'] = '^0.12.0';
      packageJson.dependencies.jsonwebtoken = '^9.0.0';
      packageJson.devDependencies['@types/jsonwebtoken'] = '^9.0.0';
    }

    if (options.eslint) {
      packageJson.devDependencies.eslint = '^8.48.0';
      packageJson.devDependencies['@typescript-eslint/eslint-plugin'] = '^6.7.0';
      packageJson.devDependencies['@typescript-eslint/parser'] = '^6.7.0';
    }

    if (options.prettier) {
      packageJson.devDependencies.prettier = '^3.0.0';
    }

    if (options.docker) {
      packageJson.scripts['docker:build'] = 'docker build -t ' + projectName + ' .';
      packageJson.scripts['docker:run'] = 'docker run -p 3000:3000 ' + projectName;
    }

    writeFileSync(
      join(projectPath, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
  }

  // ⚙️ Generate configuration files
  private async generateConfigFiles(
    projectPath: string,
    template: ProjectTemplate,
    options: CreateOptions
  ): Promise<void> {
    // Kilat config
    const kilatConfig = {
      theme: options.theme || 'cyberpunk',
      database: options.database || 'sqlite',
      auth: options.auth || false,
      features: template.features,
      build: {
        outDir: 'dist',
        sourcemap: true,
        minify: true
      },
      dev: {
        port: 3000,
        host: 'localhost',
        open: true
      }
    };

    writeFileSync(
      join(projectPath, 'kilat.config.ts'),
      `import { defineConfig } from 'kilat-core';\n\nexport default defineConfig(${JSON.stringify(kilatConfig, null, 2)});\n`
    );

    // TypeScript config
    if (options.typescript) {
      const tsConfig = {
        compilerOptions: {
          target: 'ES2020',
          lib: ['ES2020', 'DOM', 'DOM.Iterable'],
          module: 'ESNext',
          skipLibCheck: true,
          moduleResolution: 'bundler',
          allowImportingTsExtensions: true,
          resolveJsonModule: true,
          isolatedModules: true,
          noEmit: true,
          jsx: 'react-jsx',
          strict: true,
          noUnusedLocals: true,
          noUnusedParameters: true,
          noFallthroughCasesInSwitch: true,
          baseUrl: '.',
          paths: {
            '@/*': ['./src/*'],
            '@/components/*': ['./src/components/*'],
            '@/pages/*': ['./src/pages/*'],
            '@/utils/*': ['./src/utils/*'],
            '@/types/*': ['./src/types/*']
          }
        },
        include: ['src'],
        references: [{ path: './tsconfig.node.json' }]
      };

      writeFileSync(
        join(projectPath, 'tsconfig.json'),
        JSON.stringify(tsConfig, null, 2)
      );
    }

    // ESLint config
    if (options.eslint) {
      const eslintConfig = {
        root: true,
        env: { browser: true, es2020: true },
        extends: [
          'eslint:recommended',
          '@typescript-eslint/recommended',
          'plugin:react-hooks/recommended'
        ],
        ignorePatterns: ['dist', '.eslintrc.cjs'],
        parser: '@typescript-eslint/parser',
        plugins: ['react-refresh'],
        rules: {
          'react-refresh/only-export-components': [
            'warn',
            { allowConstantExport: true }
          ]
        }
      };

      writeFileSync(
        join(projectPath, '.eslintrc.json'),
        JSON.stringify(eslintConfig, null, 2)
      );
    }

    // Prettier config
    if (options.prettier) {
      const prettierConfig = {
        semi: true,
        trailingComma: 'es5',
        singleQuote: true,
        printWidth: 80,
        tabWidth: 2,
        useTabs: false
      };

      writeFileSync(
        join(projectPath, '.prettierrc'),
        JSON.stringify(prettierConfig, null, 2)
      );
    }

    // Environment variables
    const envContent = `# Kilat.js Environment Variables
NODE_ENV=development
PORT=3000
HOST=localhost

# Database
DATABASE_URL=${this.getDatabaseUrl(options.database)}

# Authentication
${options.auth ? 'JWT_SECRET=your-super-secret-jwt-key\nAUTH_SECRET=your-auth-secret' : '# JWT_SECRET=your-super-secret-jwt-key'}

# API Keys
# Add your API keys here
`;

    writeFileSync(join(projectPath, '.env.example'), envContent);
    writeFileSync(join(projectPath, '.env'), envContent);

    // Gitignore
    const gitignoreContent = `# Dependencies
node_modules/
.pnp
.pnp.js

# Production
/dist
/build

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Kilat.js
.kilat/
`;

    writeFileSync(join(projectPath, '.gitignore'), gitignoreContent);
  }

  // 📄 Generate source files
  private async generateSourceFiles(
    projectPath: string,
    template: ProjectTemplate,
    options: CreateOptions
  ): Promise<void> {
    // Main App component
    const appContent = `import React from 'react';
import { KilatRouter } from 'kilat-router';
import { ThemeProvider } from 'kilat-utils';
import { KilatUIProvider } from 'kilat-ui';
import './App.css';

function App() {
  return (
    <ThemeProvider initialTheme="${options.theme || 'cyberpunk'}">
      <KilatUIProvider>
        <KilatRouter>
          <div className="min-h-screen bg-background text-foreground">
            <main className="container mx-auto px-4 py-8">
              <h1 className="text-4xl font-bold text-center mb-8">
                Welcome to Kilat.js! ⚡
              </h1>
              <p className="text-center text-muted-foreground">
                Your ${template.name} project is ready to go.
              </p>
            </main>
          </div>
        </KilatRouter>
      </KilatUIProvider>
    </ThemeProvider>
  );
}

export default App;
`;

    writeFileSync(join(projectPath, 'src/App.tsx'), appContent);

    // Main entry point
    const mainContent = `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;

    writeFileSync(join(projectPath, 'src/main.tsx'), mainContent);

    // CSS files
    const indexCssContent = `@import 'kilatcss/dist/kilat.css';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
}
`;

    writeFileSync(join(projectPath, 'src/index.css'), indexCssContent);

    const appCssContent = `/* App-specific styles */
.container {
  max-width: 1200px;
}

/* Theme-specific customizations */
[data-kilat-theme="${options.theme || 'cyberpunk'}"] {
  /* Add custom styles for your theme */
}
`;

    writeFileSync(join(projectPath, 'src/App.css'), appCssContent);

    // HTML template
    const htmlContent = `<!DOCTYPE html>
<html lang="en" data-kilat-theme="${options.theme || 'cyberpunk'}">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/kilat-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kilat.js App</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
`;

    writeFileSync(join(projectPath, 'index.html'), htmlContent);
  }

  // 📚 Generate documentation
  private async generateDocumentation(
    projectPath: string,
    template: ProjectTemplate,
    options: CreateOptions
  ): Promise<void> {
    const readmeContent = `# ${projectPath.split('/').pop()}

A Kilat.js project created with the **${template.name}** template.

## 🚀 Features

${template.features.map(feature => `- ✅ ${feature}`).join('\n')}

## 🛠️ Development

\`\`\`bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
\`\`\`

## 📁 Project Structure

\`\`\`
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── layouts/       # Layout components
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
└── styles/        # Global styles
\`\`\`

## 🎨 Theming

This project uses the **${options.theme || 'cyberpunk'}** theme. You can change themes by updating the \`data-kilat-theme\` attribute in your HTML or using the theme provider.

## 📚 Documentation

- [Kilat.js Documentation](https://kilat-js.pcode.my.id/docs)
- [Component Library](https://kilat-js.pcode.my.id/components)
- [Themes](https://kilat-js.pcode.my.id/themes)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (\`git checkout -b feature/amazing-feature\`)
3. Commit your changes (\`git commit -m 'Add some amazing feature'\`)
4. Push to the branch (\`git push origin feature/amazing-feature\`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.
`;

    writeFileSync(join(projectPath, 'README.md'), readmeContent);
  }

  // 🔧 Helper methods
  private getDatabaseUrl(database: string): string {
    switch (database) {
      case 'postgresql':
        return 'postgresql://username:password@localhost:5432/database_name';
      case 'mysql':
        return 'mysql://username:password@localhost:3306/database_name';
      case 'mongodb':
        return 'mongodb://localhost:27017/database_name';
      case 'sqlite':
      default:
        return 'sqlite:./database.db';
    }
  }

  private async removeDirectory(path: string): Promise<void> {
    const { rmSync } = await import('fs');
    rmSync(path, { recursive: true, force: true });
  }

  // 🔍 Check if current directory is a Kilat.js project
  async isKilatProject(): Promise<boolean> {
    const configPath = join(process.cwd(), 'kilat.config.ts');
    const packagePath = join(process.cwd(), 'package.json');
    
    if (!existsSync(configPath) || !existsSync(packagePath)) {
      return false;
    }
    
    try {
      const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'));
      return packageJson.dependencies && 
             (packageJson.dependencies['kilat-core'] || 
              packageJson.devDependencies?.['kilat-cli']);
    } catch {
      return false;
    }
  }

  // 📊 Get project information
  async getProjectInfo(): Promise<ProjectInfo> {
    const packagePath = join(process.cwd(), 'package.json');
    const configPath = join(process.cwd(), 'kilat.config.ts');
    
    const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'));
    
    // Try to read config (simplified)
    let config: any = {};
    if (existsSync(configPath)) {
      const configContent = readFileSync(configPath, 'utf-8');
      // Simple regex to extract theme (in real implementation, use proper parser)
      const themeMatch = configContent.match(/theme:\s*['"]([^'"]+)['"]/);
      if (themeMatch) {
        config.theme = themeMatch[1];
      }
    }
    
    return {
      name: packageJson.name,
      version: packageJson.version,
      template: 'unknown', // Would need to be stored somewhere
      theme: config.theme || 'cyberpunk'
    };
  }

  // 🧩 Generate component
  async generateComponent(name: string, options: ComponentOptions): Promise<void> {
    // Implementation for generating components
    this.logger.info(`Generating component: ${name}`);
  }

  // 📄 Generate page
  async generatePage(name: string, options: PageOptions): Promise<void> {
    // Implementation for generating pages
    this.logger.info(`Generating page: ${name}`);
  }

  // 🔌 Generate API route
  async generateAPI(name: string, options: APIOptions): Promise<void> {
    // Implementation for generating API routes
    this.logger.info(`Generating API route: ${name}`);
  }

  // 🛡️ Generate middleware
  async generateMiddleware(name: string, options: MiddlewareOptions): Promise<void> {
    // Implementation for generating middleware
    this.logger.info(`Generating middleware: ${name}`);
  }

  // 🗃️ Generate model
  async generateModel(name: string, options: ModelOptions): Promise<void> {
    // Implementation for generating models
    this.logger.info(`Generating model: ${name}`);
  }

  // 📁 Check if directory exists
  async directoryExists(path: string): Promise<boolean> {
    return existsSync(path);
  }
}
