// 🎯 Kilat CLI Types
export interface KilatProject {
  name: string;
  platform: 'web' | 'desktop' | 'mobile' | 'fullstack';
  theme: string;
  database: 'sqlite' | 'mysql' | 'none';
  plugins: string[];
  features: string[];
  template: string;
  directory: string;
}

export interface ProjectTemplate {
  name: string;
  description: string;
  platform: 'web' | 'desktop' | 'mobile' | 'fullstack';
  features: string[];
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
  files: TemplateFile[];
}

export interface TemplateFile {
  path: string;
  content: string;
  template?: boolean; // If true, process with template engine
}

export interface CLIConfig {
  version: string;
  updateCheckInterval: number;
  defaultTheme: string;
  defaultDatabase: string;
  registryUrl: string;
  templatesUrl: string;
}

export interface PluginInfo {
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  repository?: string;
  keywords: string[];
  dependencies: string[];
  enabled: boolean;
  official: boolean;
}

export interface DatabaseConfig {
  driver: 'sqlite' | 'mysql';
  connection: {
    sqlite?: {
      file: string;
      enableWAL?: boolean;
    };
    mysql?: {
      host: string;
      port: number;
      user: string;
      password: string;
      database: string;
    };
  };
  migrations: {
    directory: string;
    autoRun: boolean;
  };
}

export interface BuildConfig {
  engine: 'kilatpack' | 'vite' | 'webpack';
  target: string;
  minify: boolean;
  sourcemap: boolean;
  analyze: boolean;
  outputDir: string;
}

export interface DeployConfig {
  provider: 'vercel' | 'netlify' | 'aws' | 'docker' | 'manual';
  environment: 'development' | 'staging' | 'production';
  buildCommand: string;
  outputDirectory: string;
  environmentVariables: Record<string, string>;
}

export interface DiagnosticResult {
  status: 'healthy' | 'warning' | 'error';
  category: 'dependencies' | 'configuration' | 'files' | 'database' | 'plugins';
  message: string;
  details?: string;
  fix?: string;
}

export interface ProjectStats {
  files: {
    total: number;
    typescript: number;
    javascript: number;
    css: number;
    components: number;
  };
  dependencies: {
    total: number;
    kilat: number;
    outdated: number;
  };
  size: {
    source: number;
    nodeModules: number;
    build?: number;
  };
  performance: {
    buildTime?: number;
    bundleSize?: number;
    testCoverage?: number;
  };
}

export interface CommandOptions {
  verbose?: boolean;
  force?: boolean;
  dry?: boolean;
  yes?: boolean;
  config?: string;
  template?: string;
  theme?: string;
  database?: string;
  plugins?: string[];
  features?: string[];
  port?: number;
  host?: string;
  open?: boolean;
  analyze?: boolean;
  watch?: boolean;
  production?: boolean;
  environment?: string;
}

export interface TaskProgress {
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  error?: Error;
}

export interface GeneratorContext {
  projectName: string;
  projectPath: string;
  template: ProjectTemplate;
  config: KilatProject;
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'bun';
  variables: Record<string, any>;
}

// 🎨 Theme definitions
export const AVAILABLE_THEMES = [
  'cyberpunk',
  'nusantara', 
  'retro',
  'material',
  'neumorphism',
  'carbon',
  'minimalist',
  'asymetric',
  'elemen3d',
  'dana',
  'ark',
  'aurora',
  'unix',
  'classic'
] as const;

export type KilatTheme = typeof AVAILABLE_THEMES[number];

// 🔌 Plugin categories
export const PLUGIN_CATEGORIES = [
  'auth',
  'database',
  'ui',
  'animation',
  'deployment',
  'testing',
  'monitoring',
  'cms',
  'ecommerce',
  'ai',
  'social',
  'analytics'
] as const;

export type PluginCategory = typeof PLUGIN_CATEGORIES[number];

// 🏗️ Project features
export const PROJECT_FEATURES = [
  'ssr',
  'pwa',
  'auth',
  'database',
  'api',
  'testing',
  'docker',
  'ci-cd',
  'monitoring',
  'analytics',
  'i18n',
  'cms',
  'ecommerce',
  'blog',
  'dashboard',
  'landing-page'
] as const;

export type ProjectFeature = typeof PROJECT_FEATURES[number];

// 📱 Platform configurations
export interface PlatformConfig {
  web: {
    framework: 'react' | 'vue' | 'svelte';
    bundler: 'vite' | 'webpack' | 'kilatpack';
    ssr: boolean;
    pwa: boolean;
  };
  desktop: {
    framework: 'electron' | 'tauri';
    target: 'windows' | 'macos' | 'linux' | 'all';
  };
  mobile: {
    framework: 'expo' | 'react-native' | 'capacitor';
    target: 'ios' | 'android' | 'both';
  };
}

// 🔧 CLI Command interface
export interface CLICommand {
  name: string;
  description: string;
  aliases?: string[];
  options?: CommandOption[];
  examples?: string[];
  handler: (args: any, options: CommandOptions) => Promise<void>;
}

export interface CommandOption {
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'array';
  required?: boolean;
  default?: any;
  choices?: string[];
}

// 📊 Analytics and telemetry
export interface TelemetryData {
  command: string;
  platform: string;
  nodeVersion: string;
  kilatVersion: string;
  projectType?: string;
  duration: number;
  success: boolean;
  error?: string;
  timestamp: string;
}
