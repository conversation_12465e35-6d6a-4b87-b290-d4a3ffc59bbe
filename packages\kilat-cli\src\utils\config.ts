import { pathExists, readFile, writeFile } from 'fs-extra';
import { join } from 'path';
import type { KilatProject } from '../types';

/**
 * Configuration Management
 * Handles loading and saving Kilat.js project configuration
 */

export interface KilatConfig {
  // Project info
  name: string;
  version: string;
  platform: 'web' | 'desktop' | 'mobile' | 'fullstack';
  
  // UI Configuration
  theme: string;
  mode: 'dark' | 'light' | 'auto';
  
  // Animation settings
  presetScene: string;
  animation: {
    autoRotate: boolean;
    background: string;
    interactive: boolean;
    ambientSound: boolean;
  };
  
  // Router configuration
  router: {
    basePath: string;
    middleware: string[];
    fileBasedRouting: boolean;
    dynamicRoutes: boolean;
    layoutNesting: boolean;
  };
  
  // Database configuration
  database: {
    driver: 'sqlite' | 'mysql' | 'none';
    connection: {
      sqlite?: {
        file: string;
        enableWAL?: boolean;
        timeout?: number;
      };
      mysql?: {
        host: string;
        port?: number;
        user: string;
        password: string;
        database: string;
        ssl?: boolean;
      };
    };
    migrations: {
      directory: string;
      autoRun: boolean;
    };
  };
  
  // Build configuration
  build: {
    engine: 'kilatpack' | 'vite' | 'webpack';
    target: string;
    minify: boolean;
    sourcemap: boolean;
    debugOverlay: boolean;
    hotReload: boolean;
    analyze: boolean;
    outputDir: string;
    publicDir: string;
    assetsDir: string;
  };
  
  // Backend configuration
  backend: {
    enabled: boolean;
    port: number;
    autoStart: boolean;
    apiPrefix: string;
  };
  
  // Plugin configuration
  plugins: Record<string, {
    version: string;
    enabled: boolean;
    retry?: number;
    onFail?: 'kill' | 'disable' | 'retry';
  }>;
  
  // AI Assistant
  aiAssistant: {
    enabled: boolean;
    endpoint: string;
    model: string;
    features: string[];
  };
  
  // Platform specific
  platform: {
    web: {
      enabled: boolean;
      ssr: boolean;
      pwa: boolean;
    };
    desktop: {
      enabled: boolean;
      electron: boolean;
      tauri: boolean;
    };
    mobile: {
      enabled: boolean;
      expo: boolean;
      capacitor: boolean;
    };
  };
  
  // Development
  dev: {
    port: number;
    host: string;
    open: boolean;
    cors: boolean;
    proxy: Record<string, string>;
    hmr: {
      port: number;
      overlay: boolean;
    };
  };
  
  // Performance
  performance: {
    bundleSplitting: boolean;
    treeshaking: boolean;
    compression: 'gzip' | 'brotli' | 'none';
    caching: {
      enabled: boolean;
      strategy: string;
    };
  };
  
  // Security
  security: {
    csp: {
      enabled: boolean;
      directives: Record<string, string[]>;
    };
    cors: {
      origin: string[];
      credentials: boolean;
    };
  };
  
  // Error handling
  errorHandling: {
    killSwitch: boolean;
    safeMode: boolean;
    crashReport: {
      enabled: boolean;
      webhookURL?: string;
      includeLogs: boolean;
      autoRetry: boolean;
    };
  };
}

// 📖 Load Kilat configuration
export async function loadKilatConfig(configPath?: string): Promise<KilatConfig> {
  const possiblePaths = [
    configPath,
    'kilat.config.ts',
    'kilat.config.js',
    'kilat.config.mjs',
    '.kilatrc.json',
    '.kilatrc.js'
  ].filter(Boolean);

  for (const path of possiblePaths) {
    if (await pathExists(path!)) {
      return await loadConfigFile(path!);
    }
  }

  // Return default configuration if no config file found
  return getDefaultConfig();
}

// 📄 Load configuration from file
async function loadConfigFile(configPath: string): Promise<KilatConfig> {
  try {
    if (configPath.endsWith('.json')) {
      const content = await readFile(configPath, 'utf-8');
      return JSON.parse(content);
    }

    // For .ts/.js files, we need to compile/import them
    // This is a simplified version - in production, we'd use proper TypeScript compilation
    if (configPath.endsWith('.ts') || configPath.endsWith('.js') || configPath.endsWith('.mjs')) {
      // For now, we'll try to read it as a module
      const fullPath = join(process.cwd(), configPath);
      delete require.cache[fullPath]; // Clear cache
      
      const configModule = require(fullPath);
      return configModule.default || configModule;
    }

    throw new Error(`Unsupported config file format: ${configPath}`);
  } catch (error) {
    throw new Error(`Failed to load config from ${configPath}: ${error.message}`);
  }
}

// 💾 Save Kilat configuration
export async function saveKilatConfig(config: KilatConfig, configPath = 'kilat.config.ts'): Promise<void> {
  try {
    if (configPath.endsWith('.json')) {
      await writeFile(configPath, JSON.stringify(config, null, 2));
    } else {
      // Generate TypeScript config file
      const configContent = generateConfigFile(config);
      await writeFile(configPath, configContent);
    }
  } catch (error) {
    throw new Error(`Failed to save config to ${configPath}: ${error.message}`);
  }
}

// 🏗️ Generate configuration file content
function generateConfigFile(config: KilatConfig): string {
  return `import type { KilatConfig } from 'kilat-core';

export default {
  // 🎯 Project Configuration
  name: "${config.name}",
  version: "${config.version}",
  platform: "${config.platform}",
  
  // 🎨 UI Theme
  theme: "${config.theme}",
  mode: "${config.mode}",
  
  // 🌌 Animation Settings
  presetScene: "${config.presetScene}",
  animation: {
    autoRotate: ${config.animation.autoRotate},
    background: "${config.animation.background}",
    interactive: ${config.animation.interactive},
    ambientSound: ${config.animation.ambientSound}
  },
  
  // 🛣️ Router Configuration
  router: {
    basePath: "${config.router.basePath}",
    middleware: ${JSON.stringify(config.router.middleware)},
    fileBasedRouting: ${config.router.fileBasedRouting},
    dynamicRoutes: ${config.router.dynamicRoutes},
    layoutNesting: ${config.router.layoutNesting}
  },
  
  // 🗄️ Database Configuration
  database: {
    driver: "${config.database.driver}",
    connection: ${JSON.stringify(config.database.connection, null, 4)},
    migrations: {
      directory: "${config.database.migrations.directory}",
      autoRun: ${config.database.migrations.autoRun}
    }
  },
  
  // 🏗️ Build Configuration
  build: {
    engine: "${config.build.engine}",
    target: "${config.build.target}",
    minify: ${config.build.minify},
    sourcemap: ${config.build.sourcemap},
    debugOverlay: ${config.build.debugOverlay},
    hotReload: ${config.build.hotReload},
    analyze: ${config.build.analyze},
    outputDir: "${config.build.outputDir}",
    publicDir: "${config.build.publicDir}",
    assetsDir: "${config.build.assetsDir}"
  },
  
  // 🚀 Backend Configuration
  backend: {
    enabled: ${config.backend.enabled},
    port: ${config.backend.port},
    autoStart: ${config.backend.autoStart},
    apiPrefix: "${config.backend.apiPrefix}"
  },
  
  // 🔌 Plugin Configuration
  plugins: ${JSON.stringify(config.plugins, null, 4)},
  
  // 🤖 AI Assistant
  aiAssistant: {
    enabled: ${config.aiAssistant.enabled},
    endpoint: "${config.aiAssistant.endpoint}",
    model: "${config.aiAssistant.model}",
    features: ${JSON.stringify(config.aiAssistant.features)}
  },
  
  // 📱 Platform Configuration
  platform: {
    web: {
      enabled: ${config.platform.web.enabled},
      ssr: ${config.platform.web.ssr},
      pwa: ${config.platform.web.pwa}
    },
    desktop: {
      enabled: ${config.platform.desktop.enabled},
      electron: ${config.platform.desktop.electron},
      tauri: ${config.platform.desktop.tauri}
    },
    mobile: {
      enabled: ${config.platform.mobile.enabled},
      expo: ${config.platform.mobile.expo},
      capacitor: ${config.platform.mobile.capacitor}
    }
  },
  
  // 🔧 Development Configuration
  dev: {
    port: ${config.dev.port},
    host: "${config.dev.host}",
    open: ${config.dev.open},
    cors: ${config.dev.cors},
    proxy: ${JSON.stringify(config.dev.proxy)},
    hmr: {
      port: ${config.dev.hmr.port},
      overlay: ${config.dev.hmr.overlay}
    }
  },
  
  // ⚡ Performance Configuration
  performance: {
    bundleSplitting: ${config.performance.bundleSplitting},
    treeshaking: ${config.performance.treeshaking},
    compression: "${config.performance.compression}",
    caching: {
      enabled: ${config.performance.caching.enabled},
      strategy: "${config.performance.caching.strategy}"
    }
  },
  
  // 🔒 Security Configuration
  security: {
    csp: {
      enabled: ${config.security.csp.enabled},
      directives: ${JSON.stringify(config.security.csp.directives, null, 6)}
    },
    cors: {
      origin: ${JSON.stringify(config.security.cors.origin)},
      credentials: ${config.security.cors.credentials}
    }
  },
  
  // 🛡️ Error Handling
  errorHandling: {
    killSwitch: ${config.errorHandling.killSwitch},
    safeMode: ${config.errorHandling.safeMode},
    crashReport: {
      enabled: ${config.errorHandling.crashReport.enabled},
      ${config.errorHandling.crashReport.webhookURL ? `webhookURL: "${config.errorHandling.crashReport.webhookURL}",` : ''}
      includeLogs: ${config.errorHandling.crashReport.includeLogs},
      autoRetry: ${config.errorHandling.crashReport.autoRetry}
    }
  }
} satisfies KilatConfig;
`;
}

// 🎯 Get default configuration
export function getDefaultConfig(): KilatConfig {
  return {
    name: 'my-kilat-app',
    version: '1.0.0',
    platform: 'web',
    theme: 'cyberpunk',
    mode: 'dark',
    presetScene: 'galaxy',
    animation: {
      autoRotate: true,
      background: '#000000',
      interactive: true,
      ambientSound: false
    },
    router: {
      basePath: '/',
      middleware: [],
      fileBasedRouting: true,
      dynamicRoutes: true,
      layoutNesting: true
    },
    database: {
      driver: 'sqlite',
      connection: {
        sqlite: {
          file: './data.db',
          enableWAL: true,
          timeout: 5000
        }
      },
      migrations: {
        directory: './migrations',
        autoRun: true
      }
    },
    build: {
      engine: 'kilatpack',
      target: 'es2022',
      minify: true,
      sourcemap: true,
      debugOverlay: false,
      hotReload: true,
      analyze: false,
      outputDir: 'dist',
      publicDir: 'public',
      assetsDir: 'assets'
    },
    backend: {
      enabled: true,
      port: 8080,
      autoStart: true,
      apiPrefix: '/api'
    },
    plugins: {},
    aiAssistant: {
      enabled: false,
      endpoint: '/api/ai',
      model: 'gpt-4',
      features: []
    },
    platform: {
      web: {
        enabled: true,
        ssr: true,
        pwa: true
      },
      desktop: {
        enabled: false,
        electron: true,
        tauri: false
      },
      mobile: {
        enabled: false,
        expo: true,
        capacitor: false
      }
    },
    dev: {
      port: 3000,
      host: 'localhost',
      open: true,
      cors: true,
      proxy: {},
      hmr: {
        port: 24678,
        overlay: true
      }
    },
    performance: {
      bundleSplitting: true,
      treeshaking: true,
      compression: 'gzip',
      caching: {
        enabled: true,
        strategy: 'stale-while-revalidate'
      }
    },
    security: {
      csp: {
        enabled: true,
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", "'unsafe-inline'"],
          'style-src': ["'self'", "'unsafe-inline'"],
          'img-src': ["'self'", "data:", "https:"]
        }
      },
      cors: {
        origin: ['http://localhost:3000'],
        credentials: true
      }
    },
    errorHandling: {
      killSwitch: true,
      safeMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true
      }
    }
  };
}

// 🔄 Update configuration
export async function updateKilatConfig(
  updates: Partial<KilatConfig>, 
  configPath = 'kilat.config.ts'
): Promise<void> {
  const currentConfig = await loadKilatConfig(configPath);
  const updatedConfig = { ...currentConfig, ...updates };
  await saveKilatConfig(updatedConfig, configPath);
}

// ✅ Validate configuration
export function validateKilatConfig(config: any): string[] {
  const errors: string[] = [];

  // Required fields
  if (!config.name) errors.push('Project name is required');
  if (!config.platform) errors.push('Platform is required');
  if (!config.theme) errors.push('Theme is required');

  // Validate platform
  const validPlatforms = ['web', 'desktop', 'mobile', 'fullstack'];
  if (!validPlatforms.includes(config.platform)) {
    errors.push(`Invalid platform: ${config.platform}`);
  }

  // Validate database driver
  if (config.database?.driver && !['sqlite', 'mysql', 'none'].includes(config.database.driver)) {
    errors.push(`Invalid database driver: ${config.database.driver}`);
  }

  // Validate build engine
  if (config.build?.engine && !['kilatpack', 'vite', 'webpack'].includes(config.build.engine)) {
    errors.push(`Invalid build engine: ${config.build.engine}`);
  }

  return errors;
}
