/**
 * Formatting utilities for Kilat CLI
 * File size, duration, and other display formatting
 */

// 📏 Format bytes to human readable format
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// ⏱️ Format duration to human readable format
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
  }

  if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  return `${seconds}s`;
}

// 📊 Format percentage
export function formatPercentage(value: number, total: number, decimals = 1): string {
  if (total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
}

// 🔢 Format number with commas
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 📅 Format date
export function formatDate(date: Date | string, format: 'short' | 'long' | 'relative' = 'short'): string {
  const d = typeof date === 'string' ? new Date(date) : date;

  switch (format) {
    case 'short':
      return d.toLocaleDateString();
    case 'long':
      return d.toLocaleString();
    case 'relative':
      return formatRelativeTime(d);
    default:
      return d.toISOString();
  }
}

// 🕐 Format relative time
export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) {
    return 'just now';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

// 📝 Format file path for display
export function formatPath(path: string, maxLength = 50): string {
  if (path.length <= maxLength) {
    return path;
  }

  const parts = path.split('/');
  if (parts.length <= 2) {
    return `...${path.slice(-(maxLength - 3))}`;
  }

  let result = parts[0];
  let remaining = parts.slice(1);
  
  while (remaining.length > 0 && result.length + remaining[remaining.length - 1].length + 4 <= maxLength) {
    result = `${result}/.../${remaining.pop()}`;
    if (remaining.length === 0) {
      result = result.replace('/.../', '/');
      break;
    }
  }

  return result;
}

// 🎨 Format command for display
export function formatCommand(command: string, args: string[] = []): string {
  const fullCommand = [command, ...args].join(' ');
  return `$ ${fullCommand}`;
}

// 📋 Format list with bullets
export function formatList(items: string[], bullet = '•'): string {
  return items.map(item => `  ${bullet} ${item}`).join('\n');
}

// 📊 Format table
export function formatTable(data: Array<Record<string, any>>, options: {
  headers?: string[];
  maxWidth?: number;
  align?: 'left' | 'center' | 'right';
} = {}): string {
  if (data.length === 0) return '';

  const headers = options.headers || Object.keys(data[0]);
  const maxWidth = options.maxWidth || 80;
  const align = options.align || 'left';

  // Calculate column widths
  const columnWidths: Record<string, number> = {};
  
  headers.forEach(header => {
    columnWidths[header] = Math.max(
      header.length,
      ...data.map(row => String(row[header] || '').length)
    );
  });

  // Adjust widths if total exceeds maxWidth
  const totalWidth = Object.values(columnWidths).reduce((sum, width) => sum + width, 0) + (headers.length - 1) * 3;
  if (totalWidth > maxWidth) {
    const scale = (maxWidth - (headers.length - 1) * 3) / (totalWidth - (headers.length - 1) * 3);
    headers.forEach(header => {
      columnWidths[header] = Math.floor(columnWidths[header] * scale);
    });
  }

  // Format rows
  const formatRow = (row: Record<string, any>) => {
    return headers.map(header => {
      const value = String(row[header] || '');
      const width = columnWidths[header];
      
      if (value.length > width) {
        return value.slice(0, width - 3) + '...';
      }
      
      switch (align) {
        case 'center':
          return value.padStart((width + value.length) / 2).padEnd(width);
        case 'right':
          return value.padStart(width);
        default:
          return value.padEnd(width);
      }
    }).join(' | ');
  };

  // Build table
  const headerRow = formatRow(Object.fromEntries(headers.map(h => [h, h])));
  const separator = headers.map(header => '-'.repeat(columnWidths[header])).join('-|-');
  const dataRows = data.map(formatRow);

  return [headerRow, separator, ...dataRows].join('\n');
}

// 🏷️ Format version
export function formatVersion(version: string, prefix = 'v'): string {
  return version.startsWith(prefix) ? version : `${prefix}${version}`;
}

// 🔗 Format URL
export function formatUrl(url: string, maxLength = 50): string {
  if (url.length <= maxLength) {
    return url;
  }

  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;
    const path = urlObj.pathname + urlObj.search;
    
    if (domain.length + 10 >= maxLength) {
      return `${domain.slice(0, maxLength - 6)}...`;
    }
    
    const availableLength = maxLength - domain.length - 3;
    if (path.length > availableLength) {
      return `${domain}...${path.slice(-(availableLength - 3))}`;
    }
    
    return url;
  } catch {
    return url.slice(0, maxLength - 3) + '...';
  }
}

// 📈 Format progress bar
export function formatProgressBar(
  current: number, 
  total: number, 
  width = 20, 
  chars = { complete: '█', incomplete: '░' }
): string {
  const percentage = Math.min(current / total, 1);
  const completed = Math.floor(percentage * width);
  const remaining = width - completed;
  
  const bar = chars.complete.repeat(completed) + chars.incomplete.repeat(remaining);
  const percent = formatPercentage(current, total, 0);
  
  return `${bar} ${percent}`;
}

// 🎯 Format status
export function formatStatus(status: 'success' | 'error' | 'warning' | 'info' | 'pending'): string {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    pending: '⏳'
  };
  
  return icons[status] || '•';
}

// 🔤 Format case
export function formatCase(str: string, caseType: 'camel' | 'pascal' | 'snake' | 'kebab' | 'constant'): string {
  const words = str.replace(/[^a-zA-Z0-9]/g, ' ').split(/\s+/).filter(Boolean);
  
  switch (caseType) {
    case 'camel':
      return words[0].toLowerCase() + words.slice(1).map(w => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase()).join('');
    case 'pascal':
      return words.map(w => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase()).join('');
    case 'snake':
      return words.map(w => w.toLowerCase()).join('_');
    case 'kebab':
      return words.map(w => w.toLowerCase()).join('-');
    case 'constant':
      return words.map(w => w.toUpperCase()).join('_');
    default:
      return str;
  }
}

// 🎨 Format code block
export function formatCodeBlock(code: string, language = ''): string {
  const lines = code.split('\n');
  const maxLineLength = Math.max(...lines.map(line => line.length));
  const border = '─'.repeat(Math.min(maxLineLength + 4, 80));
  
  return [
    `┌${border}┐`,
    ...lines.map(line => `│ ${line.padEnd(maxLineLength)} │`),
    `└${border}┘`
  ].join('\n');
}

// 📊 Format diff
export function formatDiff(oldValue: string, newValue: string): string {
  if (oldValue === newValue) {
    return `  ${oldValue}`;
  }
  
  return [
    `- ${oldValue}`,
    `+ ${newValue}`
  ].join('\n');
}
