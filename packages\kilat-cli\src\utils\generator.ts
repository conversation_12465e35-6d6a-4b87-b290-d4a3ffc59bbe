import { join } from 'path';
import { ensureDir, writeFile, copy } from 'fs-extra';
import type { GeneratorContext, TemplateFile } from '../types';

/**
 * Project Generator
 * Creates project files from templates with variable substitution
 */

// 🏗️ Generate project files
export async function generateProjectFiles(context: GeneratorContext): Promise<void> {
  const { projectPath, template, config, variables } = context;

  // Create basic project structure
  await createProjectStructure(projectPath, config.platform);

  // Generate package.json
  await generatePackageJson(context);

  // Generate kilat.config.ts
  await generateKilatConfig(context);

  // Generate main application files
  await generateAppFiles(context);

  // Generate additional files based on features
  await generateFeatureFiles(context);

  // Copy static assets
  await copyStaticAssets(context);
}

// 📁 Create project directory structure
async function createProjectStructure(projectPath: string, platform: string): Promise<void> {
  const directories = [
    'src',
    'src/components',
    'src/pages',
    'src/styles',
    'src/utils',
    'public',
    'public/assets',
    '.kilat',
    '.kilat/logs'
  ];

  // Platform-specific directories
  if (platform === 'fullstack' || platform === 'web') {
    directories.push('api', 'api/routes', 'api/middleware');
  }

  if (platform === 'desktop') {
    directories.push('electron', 'electron/main', 'electron/preload');
  }

  if (platform === 'mobile') {
    directories.push('mobile', 'mobile/components', 'mobile/screens');
  }

  for (const dir of directories) {
    await ensureDir(join(projectPath, dir));
  }
}

// 📦 Generate package.json
async function generatePackageJson(context: GeneratorContext): Promise<void> {
  const { projectPath, config, template, variables } = context;

  const packageJson = {
    name: config.name,
    version: '0.1.0',
    description: `Kilat.js ${config.platform} application`,
    type: 'module',
    scripts: {
      dev: 'kilat dev',
      build: 'kilat build',
      start: 'kilat start',
      test: 'kilat test',
      lint: 'kilat lint',
      'type-check': 'tsc --noEmit',
      ...template.scripts
    },
    dependencies: {
      'kilat-core': '^1.0.0',
      'kilatcss': '^1.0.0',
      'kilatanim.js': '^1.0.0',
      'kilat-router': '^1.0.0',
      'kilat-utils': '^1.0.0',
      ...getDependenciesForPlatform(config.platform),
      ...getDependenciesForFeatures(config.features),
      ...getDependenciesForPlugins(config.plugins)
    },
    devDependencies: {
      typescript: '^5.3.0',
      '@types/node': '^20.0.0',
      vite: '^5.0.0',
      vitest: '^1.0.0',
      ...template.devDependencies
    },
    keywords: [
      'kilat',
      'kilatjs',
      config.platform,
      config.theme,
      ...config.features
    ],
    author: '',
    license: 'MIT',
    engines: {
      node: '>=18.0.0'
    }
  };

  await writeFile(
    join(projectPath, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
}

// ⚙️ Generate kilat.config.ts
async function generateKilatConfig(context: GeneratorContext): Promise<void> {
  const { projectPath, config } = context;

  const configContent = `import type { KilatConfig } from 'kilat-core';

export default {
  // 🎯 Project Configuration
  name: "${config.name}",
  version: "0.1.0",
  platform: "${config.platform}",
  
  // 🎨 UI Theme
  theme: "${config.theme}",
  mode: "dark",
  
  // 🌌 Animation Settings
  presetScene: "galaxy",
  animation: {
    autoRotate: true,
    background: "#000000",
    interactive: true,
    ambientSound: false
  },
  
  // 🛣️ Router Configuration
  router: {
    basePath: "/",
    middleware: [],
    fileBasedRouting: true,
    dynamicRoutes: true,
    layoutNesting: true
  },
  
  // 🗄️ Database Configuration
  database: {
    driver: "${config.database}",
    connection: ${config.database === 'sqlite' 
      ? `{
      sqlite: {
        file: "./data.db",
        enableWAL: true,
        timeout: 5000
      }
    }`
      : `{
      mysql: {
        host: "localhost",
        port: 3306,
        user: "root",
        password: "",
        database: "${config.name}"
      }
    }`
    },
    migrations: {
      directory: "./migrations",
      autoRun: true
    }
  },
  
  // 🏗️ Build Configuration
  build: {
    engine: "kilatpack",
    target: "es2022",
    minify: true,
    sourcemap: true,
    debugOverlay: false,
    hotReload: true,
    analyze: false,
    outputDir: "dist",
    publicDir: "public",
    assetsDir: "assets"
  },
  
  // 🚀 Backend Configuration
  backend: {
    enabled: ${config.platform === 'fullstack' || config.features.includes('api')},
    port: 8080,
    autoStart: true,
    apiPrefix: "/api"
  },
  
  // 🔌 Plugin Configuration
  plugins: {${config.plugins.map(plugin => `
    "${plugin}": {
      version: "^1.0.0",
      enabled: true
    }`).join(',')}
  },
  
  // 🤖 AI Assistant
  aiAssistant: {
    enabled: ${config.plugins.includes('ai')},
    endpoint: "/api/ai",
    model: "gpt-4",
    features: []
  },
  
  // 📱 Platform Configuration
  platform: {
    web: {
      enabled: ${config.platform === 'web' || config.platform === 'fullstack'},
      ssr: ${config.features.includes('ssr')},
      pwa: ${config.features.includes('pwa')}
    },
    desktop: {
      enabled: ${config.platform === 'desktop'},
      electron: true,
      tauri: false
    },
    mobile: {
      enabled: ${config.platform === 'mobile'},
      expo: true,
      capacitor: false
    }
  },
  
  // 🔧 Development Configuration
  dev: {
    port: 3000,
    host: "localhost",
    open: true,
    cors: true,
    proxy: {},
    hmr: {
      port: 24678,
      overlay: true
    }
  },
  
  // ⚡ Performance Configuration
  performance: {
    bundleSplitting: true,
    treeshaking: true,
    compression: "gzip",
    caching: {
      enabled: true,
      strategy: "stale-while-revalidate"
    }
  },
  
  // 🔒 Security Configuration
  security: {
    csp: {
      enabled: true,
      directives: {
        "default-src": ["'self'"],
        "script-src": ["'self'", "'unsafe-inline'"],
        "style-src": ["'self'", "'unsafe-inline'"],
        "img-src": ["'self'", "data:", "https:"]
      }
    },
    cors: {
      origin: ["http://localhost:3000"],
      credentials: true
    }
  },
  
  // 🛡️ Error Handling
  errorHandling: {
    killSwitch: true,
    safeMode: true,
    crashReport: {
      enabled: true,
      includeLogs: true,
      autoRetry: true
    }
  }
} satisfies KilatConfig;
`;

  await writeFile(join(projectPath, 'kilat.config.ts'), configContent);
}

// 📱 Generate main application files
async function generateAppFiles(context: GeneratorContext): Promise<void> {
  const { projectPath, config } = context;

  // Generate main App component
  const appContent = generateAppComponent(config);
  await writeFile(join(projectPath, 'src/App.tsx'), appContent);

  // Generate main entry point
  const mainContent = generateMainEntry(config);
  await writeFile(join(projectPath, 'src/main.tsx'), mainContent);

  // Generate index.html
  const htmlContent = generateIndexHtml(config);
  await writeFile(join(projectPath, 'index.html'), htmlContent);

  // Generate TypeScript config
  const tsConfigContent = generateTsConfig(config);
  await writeFile(join(projectPath, 'tsconfig.json'), tsConfigContent);

  // Generate Vite config
  const viteConfigContent = generateViteConfig(config);
  await writeFile(join(projectPath, 'vite.config.ts'), viteConfigContent);
}

// 🎨 Generate App component
function generateAppComponent(config: any): string {
  return `import React from 'react';
import { KilatProvider } from 'kilat-core';
import { KilatScene } from 'kilatanim.js';
import './styles/main.css';

function App() {
  return (
    <KilatProvider theme="${config.theme}">
      <div className="k-min-h-screen k-bg-background k-text-text">
        {/* 🌌 3D Background Animation */}
        <div className="k-fixed k-inset-0 k-z-0">
          <KilatScene 
            preset="galaxy" 
            interactive={true}
            autoRotate={true}
            background="#000000"
          />
        </div>
        
        {/* 📱 Main Content */}
        <div className="k-relative k-z-10">
          <header className="k-p-8 k-text-center">
            <h1 className="k-text-4xl k-font-bold k-text-glow-${config.theme === 'cyberpunk' ? 'cyber' : 'primary'} k-mb-4">
              ⚡ Welcome to ${config.name}
            </h1>
            <p className="k-text-xl k-text-muted k-mb-8">
              Built with Kilat.js - Framework Glow Futuristik Nusantara
            </p>
          </header>
          
          <main className="k-container k-mx-auto k-px-4">
            <div className="k-grid k-grid-cols-1 k-md:grid-cols-3 k-gap-6">
              {/* Feature Cards */}
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-primary">
                  🎨 ${config.theme.charAt(0).toUpperCase() + config.theme.slice(1)} Theme
                </h3>
                <p className="k-text-muted">
                  Beautiful ${config.theme} UI with glow effects and smooth animations.
                </p>
              </div>
              
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-secondary">
                  🌌 3D Animations
                </h3>
                <p className="k-text-muted">
                  Interactive 3D scenes powered by Three.js and KilatAnim.js.
                </p>
              </div>
              
              <div className="k-card-${config.theme} k-p-6">
                <h3 className="k-text-xl k-font-semibold k-mb-3 k-text-glow-accent">
                  ⚡ Fast Development
                </h3>
                <p className="k-text-muted">
                  Hot reload, TypeScript, and modern tooling for rapid development.
                </p>
              </div>
            </div>
            
            <div className="k-text-center k-mt-12">
              <button className="k-btn-${config.theme} k-px-8 k-py-3 k-text-lg">
                Get Started
              </button>
            </div>
          </main>
        </div>
      </div>
    </KilatProvider>
  );
}

export default App;
`;
}

// 🚀 Generate main entry point
function generateMainEntry(config: any): string {
  return `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Initialize Kilat.js
import 'kilatcss/dist/kilat.css';
import 'kilatcss/dist/themes/${config.theme}.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;
}

// 📄 Generate index.html
function generateIndexHtml(config: any): string {
  return `<!DOCTYPE html>
<html lang="en" data-kilat-theme="${config.theme}">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/kilat-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${config.name} - Kilat.js App</title>
    <meta name="description" content="Built with Kilat.js - Framework Glow Futuristik Nusantara" />
  </head>
  <body class="k-${config.theme}">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
`;
}

// 📝 Generate TypeScript config
function generateTsConfig(config: any): string {
  return JSON.stringify({
    compilerOptions: {
      target: 'ES2022',
      lib: ['ES2022', 'DOM', 'DOM.Iterable'],
      module: 'ESNext',
      skipLibCheck: true,
      moduleResolution: 'bundler',
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'react-jsx',
      strict: true,
      noUnusedLocals: true,
      noUnusedParameters: true,
      noFallthroughCasesInSwitch: true,
      baseUrl: '.',
      paths: {
        '@/*': ['./src/*'],
        '@/components/*': ['./src/components/*'],
        '@/pages/*': ['./src/pages/*'],
        '@/utils/*': ['./src/utils/*']
      }
    },
    include: ['src'],
    references: [{ path: './tsconfig.node.json' }]
  }, null, 2);
}

// ⚡ Generate Vite config
function generateViteConfig(config: any): string {
  return `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/pages': resolve(__dirname, './src/pages'),
      '@/utils': resolve(__dirname, './src/utils')
    }
  },
  server: {
    port: 3000,
    host: true,
    open: true
  },
  build: {
    target: 'es2022',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          kilat: ['kilat-core', 'kilatcss', 'kilatanim.js']
        }
      }
    }
  }
});
`;
}

// 🎯 Generate feature-specific files
async function generateFeatureFiles(context: GeneratorContext): Promise<void> {
  const { projectPath, config } = context;

  // Generate auth files if auth feature is enabled
  if (config.features.includes('auth')) {
    await generateAuthFiles(projectPath);
  }

  // Generate API files if backend is enabled
  if (config.platform === 'fullstack' || config.features.includes('api')) {
    await generateApiFiles(projectPath);
  }

  // Generate test files if testing is enabled
  if (config.features.includes('testing')) {
    await generateTestFiles(projectPath);
  }

  // Generate Docker files if docker is enabled
  if (config.features.includes('docker')) {
    await generateDockerFiles(projectPath);
  }
}

// 🔐 Generate auth files
async function generateAuthFiles(projectPath: string): Promise<void> {
  // This would generate auth-related components and utilities
  // For brevity, just creating placeholder
  await writeFile(
    join(projectPath, 'src/utils/auth.ts'),
    '// Auth utilities will be generated here'
  );
}

// 🚀 Generate API files
async function generateApiFiles(projectPath: string): Promise<void> {
  // Generate basic API structure
  await writeFile(
    join(projectPath, 'api/routes/health.ts'),
    `export async function GET() {
  return Response.json({ status: 'healthy', timestamp: new Date().toISOString() });
}`
  );
}

// 🧪 Generate test files
async function generateTestFiles(projectPath: string): Promise<void> {
  await writeFile(
    join(projectPath, 'src/App.test.tsx'),
    `import { render, screen } from '@testing-library/react';
import App from './App';

test('renders welcome message', () => {
  render(<App />);
  const welcomeElement = screen.getByText(/Welcome to/i);
  expect(welcomeElement).toBeInTheDocument();
});`
  );
}

// 🐳 Generate Docker files
async function generateDockerFiles(projectPath: string): Promise<void> {
  await writeFile(
    join(projectPath, 'Dockerfile'),
    `FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]`
  );
}

// 📁 Copy static assets
async function copyStaticAssets(context: GeneratorContext): Promise<void> {
  const { projectPath } = context;
  
  // Create basic favicon and logo
  // In a real implementation, these would be actual files
  await writeFile(
    join(projectPath, 'public/kilat-logo.svg'),
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="50">⚡</text></svg>'
  );
}

// 🔧 Helper functions
function getDependenciesForPlatform(platform: string): Record<string, string> {
  const deps: Record<string, string> = {};
  
  switch (platform) {
    case 'web':
    case 'fullstack':
      deps.react = '^18.2.0';
      deps['react-dom'] = '^18.2.0';
      break;
    case 'desktop':
      deps.electron = '^27.0.0';
      break;
    case 'mobile':
      deps.expo = '^49.0.0';
      deps['react-native'] = '^0.72.0';
      break;
  }
  
  return deps;
}

function getDependenciesForFeatures(features: string[]): Record<string, string> {
  const deps: Record<string, string> = {};
  
  if (features.includes('auth')) {
    deps.jsonwebtoken = '^9.0.0';
    deps.bcryptjs = '^2.4.3';
  }
  
  if (features.includes('testing')) {
    deps['@testing-library/react'] = '^13.4.0';
    deps['@testing-library/jest-dom'] = '^6.1.0';
  }
  
  return deps;
}

function getDependenciesForPlugins(plugins: string[]): Record<string, string> {
  const deps: Record<string, string> = {};
  
  plugins.forEach(plugin => {
    deps[`kilat-plugin-${plugin}`] = '^1.0.0';
  });
  
  return deps;
}
