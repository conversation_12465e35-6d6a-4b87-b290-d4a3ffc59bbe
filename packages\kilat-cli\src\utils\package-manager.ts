import { pathExists, readFile } from 'fs-extra';
import { execa } from 'execa';
import chalk from 'chalk';
import ora from 'ora';

/**
 * Package Manager Detection and Operations
 * Supports npm, yarn, pnpm, and bun
 */

export type PackageManager = 'npm' | 'yarn' | 'pnpm' | 'bun';

// 🔍 Detect package manager
export async function detectPackageManager(): Promise<PackageManager> {
  // Check for lock files
  if (await pathExists('bun.lockb')) return 'bun';
  if (await pathExists('pnpm-lock.yaml')) return 'pnpm';
  if (await pathExists('yarn.lock')) return 'yarn';
  if (await pathExists('package-lock.json')) return 'npm';

  // Check for package manager in package.json
  try {
    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    if (packageJson.packageManager) {
      const pm = packageJson.packageManager.split('@')[0];
      if (['npm', 'yarn', 'pnpm', 'bun'].includes(pm)) {
        return pm as PackageManager;
      }
    }
  } catch {
    // Ignore errors
  }

  // Check which package managers are available
  const available = await getAvailablePackageManagers();
  
  // Prefer bun > pnpm > yarn > npm
  if (available.includes('bun')) return 'bun';
  if (available.includes('pnpm')) return 'pnpm';
  if (available.includes('yarn')) return 'yarn';
  return 'npm';
}

// 📦 Get available package managers
export async function getAvailablePackageManagers(): Promise<PackageManager[]> {
  const managers: PackageManager[] = ['npm', 'yarn', 'pnpm', 'bun'];
  const available: PackageManager[] = [];

  for (const manager of managers) {
    try {
      await execa(manager, ['--version'], { stdio: 'pipe' });
      available.push(manager);
    } catch {
      // Manager not available
    }
  }

  return available;
}

// 📥 Install dependencies
export async function installDependencies(
  projectPath: string, 
  packageManager: PackageManager = 'npm',
  options: { dev?: boolean; exact?: boolean } = {}
): Promise<void> {
  const spinner = ora('Installing dependencies...').start();

  try {
    const args = getInstallArgs(packageManager, options);
    
    await execa(packageManager, args, {
      cwd: projectPath,
      stdio: 'pipe'
    });

    spinner.succeed('Dependencies installed successfully');
  } catch (error) {
    spinner.fail('Failed to install dependencies');
    throw new Error(`Installation failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 📦 Add package
export async function addPackage(
  packageName: string | string[],
  packageManager: PackageManager = 'npm',
  options: { dev?: boolean; exact?: boolean; global?: boolean } = {}
): Promise<void> {
  const packages = Array.isArray(packageName) ? packageName : [packageName];
  const spinner = ora(`Adding ${packages.join(', ')}...`).start();

  try {
    const args = getAddArgs(packageManager, packages, options);
    
    await execa(packageManager, args, {
      stdio: 'pipe'
    });

    spinner.succeed(`Added ${packages.join(', ')}`);
  } catch (error) {
    spinner.fail(`Failed to add ${packages.join(', ')}`);
    throw new Error(`Add package failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 🗑️ Remove package
export async function removePackage(
  packageName: string | string[],
  packageManager: PackageManager = 'npm'
): Promise<void> {
  const packages = Array.isArray(packageName) ? packageName : [packageName];
  const spinner = ora(`Removing ${packages.join(', ')}...`).start();

  try {
    const args = getRemoveArgs(packageManager, packages);
    
    await execa(packageManager, args, {
      stdio: 'pipe'
    });

    spinner.succeed(`Removed ${packages.join(', ')}`);
  } catch (error) {
    spinner.fail(`Failed to remove ${packages.join(', ')}`);
    throw new Error(`Remove package failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 🔄 Update packages
export async function updatePackages(
  packageManager: PackageManager = 'npm',
  packageName?: string
): Promise<void> {
  const spinner = ora('Updating packages...').start();

  try {
    const args = getUpdateArgs(packageManager, packageName);
    
    await execa(packageManager, args, {
      stdio: 'pipe'
    });

    spinner.succeed('Packages updated successfully');
  } catch (error) {
    spinner.fail('Failed to update packages');
    throw new Error(`Update failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 🏃 Run script
export async function runScript(
  scriptName: string,
  packageManager: PackageManager = 'npm',
  args: string[] = []
): Promise<void> {
  const runArgs = getRunArgs(packageManager, scriptName, args);
  
  await execa(packageManager, runArgs, {
    stdio: 'inherit'
  });
}

// 🔧 Get install arguments
function getInstallArgs(packageManager: PackageManager, options: { dev?: boolean; exact?: boolean }): string[] {
  switch (packageManager) {
    case 'npm':
      const npmArgs = ['install'];
      if (options.exact) npmArgs.push('--save-exact');
      return npmArgs;

    case 'yarn':
      const yarnArgs = ['install'];
      if (options.exact) yarnArgs.push('--exact');
      return yarnArgs;

    case 'pnpm':
      const pnpmArgs = ['install'];
      if (options.exact) pnpmArgs.push('--save-exact');
      return pnpmArgs;

    case 'bun':
      const bunArgs = ['install'];
      if (options.exact) bunArgs.push('--exact');
      return bunArgs;

    default:
      return ['install'];
  }
}

// 🔧 Get add arguments
function getAddArgs(
  packageManager: PackageManager, 
  packages: string[], 
  options: { dev?: boolean; exact?: boolean; global?: boolean }
): string[] {
  switch (packageManager) {
    case 'npm':
      const npmArgs = ['install', ...packages];
      if (options.dev) npmArgs.push('--save-dev');
      if (options.exact) npmArgs.push('--save-exact');
      if (options.global) npmArgs.push('--global');
      return npmArgs;

    case 'yarn':
      const yarnArgs = options.global ? ['global', 'add', ...packages] : ['add', ...packages];
      if (options.dev && !options.global) yarnArgs.push('--dev');
      if (options.exact) yarnArgs.push('--exact');
      return yarnArgs;

    case 'pnpm':
      const pnpmArgs = ['add', ...packages];
      if (options.dev) pnpmArgs.push('--save-dev');
      if (options.exact) pnpmArgs.push('--save-exact');
      if (options.global) pnpmArgs.push('--global');
      return pnpmArgs;

    case 'bun':
      const bunArgs = ['add', ...packages];
      if (options.dev) bunArgs.push('--dev');
      if (options.exact) bunArgs.push('--exact');
      if (options.global) bunArgs.push('--global');
      return bunArgs;

    default:
      return ['add', ...packages];
  }
}

// 🔧 Get remove arguments
function getRemoveArgs(packageManager: PackageManager, packages: string[]): string[] {
  switch (packageManager) {
    case 'npm':
      return ['uninstall', ...packages];
    case 'yarn':
      return ['remove', ...packages];
    case 'pnpm':
      return ['remove', ...packages];
    case 'bun':
      return ['remove', ...packages];
    default:
      return ['remove', ...packages];
  }
}

// 🔧 Get update arguments
function getUpdateArgs(packageManager: PackageManager, packageName?: string): string[] {
  const packages = packageName ? [packageName] : [];
  
  switch (packageManager) {
    case 'npm':
      return ['update', ...packages];
    case 'yarn':
      return ['upgrade', ...packages];
    case 'pnpm':
      return ['update', ...packages];
    case 'bun':
      return ['update', ...packages];
    default:
      return ['update', ...packages];
  }
}

// 🔧 Get run arguments
function getRunArgs(packageManager: PackageManager, scriptName: string, args: string[]): string[] {
  switch (packageManager) {
    case 'npm':
      return ['run', scriptName, '--', ...args];
    case 'yarn':
      return ['run', scriptName, ...args];
    case 'pnpm':
      return ['run', scriptName, ...args];
    case 'bun':
      return ['run', scriptName, ...args];
    default:
      return ['run', scriptName, ...args];
  }
}

// 📊 Get package info
export async function getPackageInfo(packageName: string): Promise<any> {
  try {
    const result = await execa('npm', ['view', packageName, '--json'], {
      stdio: 'pipe'
    });
    
    return JSON.parse(result.stdout);
  } catch (error) {
    throw new Error(`Failed to get package info: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 🔍 Check if package is installed
export async function isPackageInstalled(packageName: string, global = false): Promise<boolean> {
  try {
    const args = global ? ['list', '--global', '--depth=0'] : ['list', '--depth=0'];
    const result = await execa('npm', args, { stdio: 'pipe' });
    
    return result.stdout.includes(packageName);
  } catch {
    return false;
  }
}

// 📋 Get installed packages
export async function getInstalledPackages(): Promise<Record<string, string>> {
  try {
    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    return {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };
  } catch {
    return {};
  }
}

// 🔄 Get outdated packages
export async function getOutdatedPackages(packageManager: PackageManager = 'npm'): Promise<any[]> {
  try {
    let result;
    
    switch (packageManager) {
      case 'npm':
        result = await execa('npm', ['outdated', '--json'], { stdio: 'pipe' });
        break;
      case 'yarn':
        result = await execa('yarn', ['outdated', '--json'], { stdio: 'pipe' });
        break;
      case 'pnpm':
        result = await execa('pnpm', ['outdated', '--format', 'json'], { stdio: 'pipe' });
        break;
      case 'bun':
        result = await execa('bun', ['outdated'], { stdio: 'pipe' });
        break;
    }
    
    return JSON.parse(result.stdout);
  } catch {
    return [];
  }
}

// 💡 Get package manager recommendations
export function getPackageManagerRecommendations(): string[] {
  const recommendations = [];
  
  recommendations.push('🚀 Bun - Fastest package manager and runtime');
  recommendations.push('⚡ pnpm - Fast, disk space efficient');
  recommendations.push('🧶 Yarn - Reliable with good workspace support');
  recommendations.push('📦 npm - Default Node.js package manager');
  
  return recommendations;
}
