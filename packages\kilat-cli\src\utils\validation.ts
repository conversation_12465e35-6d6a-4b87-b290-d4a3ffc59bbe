import { pathExists, readFile } from 'fs-extra';
import { join } from 'path';
import semver from 'semver';

/**
 * Validation utilities for Kilat CLI
 * Project validation, dependency checking, and name validation
 */

// 📝 Validate project name
export function validateProjectName(name: string): boolean | string {
  if (!name) {
    return 'Project name is required';
  }

  if (name.length < 1) {
    return 'Project name must be at least 1 character';
  }

  if (name.length > 214) {
    return 'Project name must be less than 214 characters';
  }

  if (name.toLowerCase() !== name) {
    return 'Project name must be lowercase';
  }

  if (/^[._]/.test(name)) {
    return 'Project name cannot start with . or _';
  }

  if (!/^[a-z0-9._-]+$/.test(name)) {
    return 'Project name can only contain lowercase letters, numbers, dots, hyphens, and underscores';
  }

  // Check against reserved names
  const reservedNames = [
    'node_modules',
    'favicon.ico',
    'package.json',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    'bun.lockb',
    '.git',
    '.gitignore',
    '.env',
    'dist',
    'build',
    'public',
    'src',
    'index',
    'main',
    'app',
    'www',
    'web',
    'api',
    'server',
    'client',
    'admin',
    'dashboard',
    'config',
    'lib',
    'libs',
    'utils',
    'helpers',
    'components',
    'pages',
    'routes',
    'middleware',
    'plugins',
    'assets',
    'static',
    'uploads',
    'temp',
    'tmp',
    'cache',
    'logs',
    'test',
    'tests',
    'spec',
    'specs',
    'docs',
    'documentation'
  ];

  if (reservedNames.includes(name)) {
    return `Project name "${name}" is reserved`;
  }

  // Check against npm package names that might conflict
  const npmReserved = [
    'kilat',
    'kilatjs',
    'kilat-core',
    'kilatcss',
    'kilatanim',
    'kilat-router',
    'kilat-cli',
    'kilat-backend',
    'kilat-db'
  ];

  if (npmReserved.includes(name)) {
    return `Project name "${name}" conflicts with Kilat.js package names`;
  }

  return true;
}

// 🔍 Check project dependencies
export async function checkDependencies(): Promise<string[]> {
  const issues: string[] = [];

  try {
    // Check if package.json exists
    if (!(await pathExists('package.json'))) {
      issues.push('package.json not found');
      return issues;
    }

    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    const dependencies = {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };

    // Check for required Kilat.js packages
    const requiredPackages = [
      'kilat-core',
      'kilatcss',
      'kilat-router'
    ];

    for (const pkg of requiredPackages) {
      if (!dependencies[pkg]) {
        issues.push(`Missing required package: ${pkg}`);
      }
    }

    // Check for version compatibility
    const kilatCoreVersion = dependencies['kilat-core'];
    if (kilatCoreVersion && !semver.satisfies('1.0.0', kilatCoreVersion)) {
      issues.push(`Incompatible kilat-core version: ${kilatCoreVersion}`);
    }

    // Check for conflicting packages
    const conflictingPackages = [
      'create-react-app',
      'vue-cli',
      'angular-cli',
      '@nuxt/cli'
    ];

    for (const pkg of conflictingPackages) {
      if (dependencies[pkg]) {
        issues.push(`Conflicting package detected: ${pkg}`);
      }
    }

    // Check Node.js version compatibility
    const nodeVersion = process.version;
    if (!semver.gte(nodeVersion, '18.0.0')) {
      issues.push(`Node.js version ${nodeVersion} is not supported. Minimum required: 18.0.0`);
    }

  } catch (error) {
    issues.push(`Failed to check dependencies: ${error.message}`);
  }

  return issues;
}

// 🏗️ Validate project structure
export async function validateProjectStructure(): Promise<string[]> {
  const issues: string[] = [];

  // Required files
  const requiredFiles = [
    'package.json',
    'kilat.config.ts'
  ];

  for (const file of requiredFiles) {
    if (!(await pathExists(file))) {
      issues.push(`Missing required file: ${file}`);
    }
  }

  // Required directories
  const requiredDirs = [
    'src',
    'public'
  ];

  for (const dir of requiredDirs) {
    if (!(await pathExists(dir))) {
      issues.push(`Missing required directory: ${dir}`);
    }
  }

  // Check for common issues
  if (await pathExists('node_modules') && !(await pathExists('package-lock.json') || await pathExists('yarn.lock') || await pathExists('pnpm-lock.yaml') || await pathExists('bun.lockb'))) {
    issues.push('node_modules exists but no lock file found');
  }

  return issues;
}

// 🔧 Validate configuration
export async function validateConfiguration(): Promise<string[]> {
  const issues: string[] = [];

  try {
    // Check kilat.config.ts
    if (await pathExists('kilat.config.ts')) {
      const { loadKilatConfig, validateKilatConfig } = await import('./config');
      const config = await loadKilatConfig();
      const configIssues = validateKilatConfig(config);
      issues.push(...configIssues);
    }

    // Check package.json scripts
    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    const scripts = packageJson.scripts || {};

    const recommendedScripts = [
      'dev',
      'build',
      'start',
      'test'
    ];

    for (const script of recommendedScripts) {
      if (!scripts[script]) {
        issues.push(`Missing recommended script: ${script}`);
      }
    }

  } catch (error) {
    issues.push(`Configuration validation failed: ${error.message}`);
  }

  return issues;
}

// 🌐 Validate environment
export async function validateEnvironment(): Promise<string[]> {
  const issues: string[] = [];

  // Check Node.js version
  const nodeVersion = process.version;
  if (!semver.gte(nodeVersion, '18.0.0')) {
    issues.push(`Node.js ${nodeVersion} is not supported. Please upgrade to 18.0.0 or higher.`);
  }

  // Check for required global tools
  const { execa } = await import('execa');
  
  const tools = [
    { name: 'git', command: 'git --version' },
    { name: 'node', command: 'node --version' }
  ];

  for (const tool of tools) {
    try {
      await execa(tool.name, ['--version'], { stdio: 'pipe' });
    } catch {
      issues.push(`${tool.name} is not installed or not in PATH`);
    }
  }

  // Check available package managers
  const { getAvailablePackageManagers } = await import('./package-manager');
  const availableManagers = await getAvailablePackageManagers();
  
  if (availableManagers.length === 0) {
    issues.push('No package manager found (npm, yarn, pnpm, or bun)');
  }

  // Check disk space (simplified check)
  try {
    const { statSync } = await import('fs');
    const stats = statSync('.');
    // This is a very basic check - in production you'd use a proper disk space library
  } catch {
    // Ignore disk space check errors
  }

  return issues;
}

// 🔒 Validate security
export async function validateSecurity(): Promise<string[]> {
  const issues: string[] = [];

  try {
    // Check for .env files in git
    if (await pathExists('.git') && await pathExists('.env')) {
      const { execa } = await import('execa');
      try {
        const result = await execa('git', ['ls-files', '.env'], { stdio: 'pipe' });
        if (result.stdout.includes('.env')) {
          issues.push('.env file is tracked by git (security risk)');
        }
      } catch {
        // Git command failed, skip this check
      }
    }

    // Check for hardcoded secrets in config
    if (await pathExists('kilat.config.ts')) {
      const configContent = await readFile('kilat.config.ts', 'utf-8');
      
      const secretPatterns = [
        /password\s*[:=]\s*['"][^'"]+['"]/i,
        /secret\s*[:=]\s*['"][^'"]+['"]/i,
        /key\s*[:=]\s*['"][^'"]+['"]/i,
        /token\s*[:=]\s*['"][^'"]+['"]/i
      ];

      for (const pattern of secretPatterns) {
        if (pattern.test(configContent)) {
          issues.push('Potential hardcoded secrets found in configuration');
          break;
        }
      }
    }

    // Check package.json for security issues
    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    
    // Check for scripts that might be dangerous
    const scripts = packageJson.scripts || {};
    const dangerousPatterns = [
      /rm\s+-rf/,
      /sudo/,
      /curl.*\|.*sh/,
      /wget.*\|.*sh/
    ];

    for (const [scriptName, scriptContent] of Object.entries(scripts)) {
      for (const pattern of dangerousPatterns) {
        if (pattern.test(scriptContent as string)) {
          issues.push(`Potentially dangerous script detected: ${scriptName}`);
        }
      }
    }

  } catch (error) {
    issues.push(`Security validation failed: ${error.message}`);
  }

  return issues;
}

// 📊 Validate performance
export async function validatePerformance(): Promise<string[]> {
  const issues: string[] = [];

  try {
    const packageJson = JSON.parse(await readFile('package.json', 'utf-8'));
    const dependencies = {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };

    // Check for heavy dependencies
    const heavyPackages = [
      'lodash', // Suggest lodash-es instead
      'moment', // Suggest date-fns or dayjs instead
      'jquery', // Usually not needed in modern frameworks
      'bootstrap' // Suggest using kilatcss instead
    ];

    for (const pkg of heavyPackages) {
      if (dependencies[pkg]) {
        issues.push(`Heavy dependency detected: ${pkg} (consider alternatives)`);
      }
    }

    // Check for duplicate functionality
    const duplicates = [
      ['axios', 'fetch'], // Both for HTTP requests
      ['lodash', 'ramda'], // Both for utility functions
      ['moment', 'date-fns', 'dayjs'] // All for date manipulation
    ];

    for (const group of duplicates) {
      const found = group.filter(pkg => dependencies[pkg]);
      if (found.length > 1) {
        issues.push(`Duplicate functionality: ${found.join(', ')}`);
      }
    }

  } catch (error) {
    issues.push(`Performance validation failed: ${error.message}`);
  }

  return issues;
}

// 🎯 Run all validations
export async function runAllValidations(): Promise<{
  dependencies: string[];
  structure: string[];
  configuration: string[];
  environment: string[];
  security: string[];
  performance: string[];
}> {
  const [
    dependencies,
    structure,
    configuration,
    environment,
    security,
    performance
  ] = await Promise.all([
    checkDependencies(),
    validateProjectStructure(),
    validateConfiguration(),
    validateEnvironment(),
    validateSecurity(),
    validatePerformance()
  ]);

  return {
    dependencies,
    structure,
    configuration,
    environment,
    security,
    performance
  };
}
