{"name": "kilat-core", "version": "1.0.0", "description": "⚡ Kilat.js Core Engine - Layout Manager, SSR, Context Providers", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run clean && bun run build:js && bun run build:types", "build:js": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.js --external:react --external:react-dom --external:zustand --external:react-error-boundary", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "core", "react", "ssr", "layout", "context", "framework"], "author": "KangPCode", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"zustand": "^4.4.7", "react-error-boundary": "^4.0.11"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "esbuild": "^0.19.0", "typescript": "^5.3.0"}}