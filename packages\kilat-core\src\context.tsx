import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  KilatConfig,
  KilatTheme,
  KilatMode,
  KilatContextValue
} from './types';
import { initializeErrorRecovery } from './error-recovery';
import { getPluginSystem } from './plugin-system';
import { kilatPlatform } from './platform';

// 🏪 Zustand Store for Global State
interface KilatStore {
  config: KilatConfig | null;
  theme: KilatTheme;
  mode: KilatMode;
  isLoading: boolean;
  error: Error | null;
  setConfig: (config: KilatConfig) => void;
  setTheme: (theme: KilatTheme) => void;
  setMode: (mode: KilatMode) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
}

export const useKilatStore = create<KilatStore>()(
  persist(
    (set, get) => ({
      config: null,
      theme: 'cyberpunk',
      mode: 'dark',
      isLoading: false,
      error: null,
      
      setConfig: (config) => set({ config }),
      setTheme: (theme) => {
        set({ theme });
        // Apply theme to document
        document.documentElement.setAttribute('data-kilat-theme', theme);
      },
      setMode: (mode) => {
        set({ mode });
        // Apply mode to document
        document.documentElement.setAttribute('data-kilat-mode', mode);
      },
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
    }),
    {
      name: 'kilat-store',
      partialize: (state) => ({ 
        theme: state.theme, 
        mode: state.mode 
      }),
    }
  )
);

// 🎯 React Context
const KilatContext = createContext<KilatContextValue | null>(null);

// 🎯 Context Provider
interface KilatProviderProps {
  children: ReactNode;
  config: KilatConfig;
}

export function KilatProvider({ children, config }: KilatProviderProps) {
  const store = useKilatStore();

  // Initialize config on mount
  useEffect(() => {
    store.setConfig(config);
    store.setTheme(config.theme);
    store.setMode(config.mode);

    // Initialize error recovery if configured
    if (config.errorRecovery?.enabled) {
      initializeErrorRecovery(config.errorRecovery);
    }

    // Initialize plugin system
    const pluginSystem = getPluginSystem();
    const contextValue: KilatContextValue = {
      config,
      theme: config.theme,
      mode: config.mode,
      setTheme: store.setTheme,
      setMode: store.setMode,
      isLoading: store.isLoading,
      error: store.error,
    };
    pluginSystem.setContext(contextValue);

  }, [config, store]);

  // Apply theme and mode to document
  useEffect(() => {
    document.documentElement.setAttribute('data-kilat-theme', store.theme);
    document.documentElement.setAttribute('data-kilat-mode', store.mode);
    
    // Add kilat class for global styling
    document.documentElement.classList.add('kilat');
    
    return () => {
      document.documentElement.classList.remove('kilat');
    };
  }, [store.theme, store.mode]);

  // Auto detect system theme preference
  useEffect(() => {
    if (store.mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        document.documentElement.setAttribute(
          'data-kilat-mode', 
          e.matches ? 'dark' : 'light'
        );
      };
      
      mediaQuery.addEventListener('change', handleChange);
      // Set initial value
      document.documentElement.setAttribute(
        'data-kilat-mode', 
        mediaQuery.matches ? 'dark' : 'light'
      );
      
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [store.mode]);

  const contextValue: KilatContextValue = {
    config: store.config || config,
    theme: store.theme,
    mode: store.mode,
    setTheme: store.setTheme,
    setMode: store.setMode,
    isLoading: store.isLoading,
    error: store.error,
  };

  return (
    <KilatContext.Provider value={contextValue}>
      {children}
    </KilatContext.Provider>
  );
}

// 🪝 Hook to use Kilat context
export function useKilat(): KilatContextValue {
  const context = useContext(KilatContext);
  if (!context) {
    throw new Error('useKilat must be used within a KilatProvider');
  }
  return context;
}

// 🪝 Hook for theme management
export function useTheme() {
  const { theme, setTheme, mode, setMode } = useKilat();
  
  return {
    theme,
    mode,
    setTheme,
    setMode,
    isDark: mode === 'dark' || (mode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches),
    isLight: mode === 'light' || (mode === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches),
  };
}

// 🪝 Hook for loading state
export function useLoading() {
  const store = useKilatStore();
  
  return {
    isLoading: store.isLoading,
    setLoading: store.setLoading,
  };
}

// 🪝 Hook for error handling
export function useError() {
  const store = useKilatStore();
  
  return {
    error: store.error,
    setError: store.setError,
    clearError: () => store.setError(null),
  };
}
