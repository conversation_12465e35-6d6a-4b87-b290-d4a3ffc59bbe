import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import type { KilatConfig, KilatContextValue, KilatState, KilatAction } from '../types';
import { createLogger } from '../utils/logger';
import { detectPlatform } from '../utils/platform';
import { initializeErrorRecovery } from '../error-recovery';
import { initializePlugins } from '../plugin-system';

const logger = createLogger({ prefix: 'KilatContext' });

/**
 * 🚀 Kilat Context
 * Main context for Kilat.js framework
 */
export const KilatContext = createContext<KilatContextValue | null>(null);

/**
 * 🔄 Kilat State Reducer
 */
function kilatReducer(state: KilatState, action: KilatAction): KilatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'SET_THEME':
      return { 
        ...state, 
        theme: action.payload,
        config: { ...state.config, theme: action.payload }
      };
    
    case 'SET_MODE':
      return { 
        ...state, 
        mode: action.payload,
        config: { ...state.config, mode: action.payload }
      };
    
    case 'UPDATE_CONFIG':
      return { 
        ...state, 
        config: { ...state.config, ...action.payload }
      };
    
    case 'ADD_PLUGIN':
      return {
        ...state,
        plugins: [...state.plugins, action.payload]
      };
    
    case 'REMOVE_PLUGIN':
      return {
        ...state,
        plugins: state.plugins.filter(p => p.name !== action.payload)
      };
    
    case 'UPDATE_STATS':
      return {
        ...state,
        stats: { ...state.stats, ...action.payload }
      };
    
    case 'SET_PLATFORM':
      return {
        ...state,
        platform: action.payload
      };
    
    default:
      return state;
  }
}

/**
 * 🎯 Initial State
 */
function createInitialState(config: KilatConfig): KilatState {
  return {
    isLoading: false,
    error: null,
    theme: config.theme || 'cyberpunk',
    mode: config.mode || 'dark',
    config,
    plugins: [],
    stats: {
      startTime: Date.now(),
      renderCount: 0,
      errorCount: 0,
      pluginCount: 0,
      memoryUsage: 0,
      performanceScore: 100
    },
    platform: detectPlatform(),
    isInitialized: false
  };
}

/**
 * 🎯 Kilat Provider Props
 */
interface KilatProviderProps {
  children: React.ReactNode;
  config: KilatConfig;
  plugins?: any[];
  onError?: (error: Error) => void;
  onReady?: () => void;
}

/**
 * 🚀 Kilat Provider Component
 */
export function KilatProvider({ 
  children, 
  config, 
  plugins = [],
  onError,
  onReady 
}: KilatProviderProps) {
  const [state, dispatch] = useReducer(kilatReducer, createInitialState(config));
  
  // Initialize framework
  useEffect(() => {
    async function initialize() {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        logger.info('Initializing Kilat.js framework...');
        
        // Initialize error recovery
        if (config.errorHandling?.enabled !== false) {
          await initializeErrorRecovery(config.errorHandling);
          logger.info('Error recovery initialized');
        }
        
        // Initialize plugins
        if (plugins.length > 0) {
          await initializePlugins(plugins, config);
          plugins.forEach(plugin => {
            dispatch({ type: 'ADD_PLUGIN', payload: plugin });
          });
          logger.info(`${plugins.length} plugins initialized`);
        }
        
        // Update platform info
        const platformInfo = detectPlatform();
        dispatch({ type: 'SET_PLATFORM', payload: platformInfo });
        
        // Update stats
        dispatch({ 
          type: 'UPDATE_STATS', 
          payload: { 
            pluginCount: plugins.length,
            memoryUsage: getMemoryUsage()
          }
        });
        
        // Mark as initialized
        dispatch({ type: 'SET_LOADING', payload: false });
        
        logger.success('Kilat.js framework initialized successfully');
        onReady?.();
        
      } catch (error) {
        logger.error('Failed to initialize Kilat.js:', error);
        dispatch({ type: 'SET_ERROR', payload: error as Error });
        onError?.(error as Error);
      }
    }
    
    initialize();
  }, [config, plugins, onError, onReady]);
  
  // Performance monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      dispatch({ 
        type: 'UPDATE_STATS', 
        payload: { 
          memoryUsage: getMemoryUsage(),
          renderCount: state.stats.renderCount + 1
        }
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, [state.stats.renderCount]);
  
  // Context value
  const contextValue: KilatContextValue = {
    // State
    ...state,
    
    // Actions
    setLoading: useCallback((loading: boolean) => {
      dispatch({ type: 'SET_LOADING', payload: loading });
    }, []),
    
    setError: useCallback((error: Error | null) => {
      if (error) {
        dispatch({ type: 'SET_ERROR', payload: error });
        logger.error('Error set:', error);
      } else {
        dispatch({ type: 'CLEAR_ERROR' });
      }
    }, []),
    
    setTheme: useCallback((theme: string) => {
      dispatch({ type: 'SET_THEME', payload: theme });
      logger.info(`Theme changed to: ${theme}`);
    }, []),
    
    setMode: useCallback((mode: 'light' | 'dark' | 'system') => {
      dispatch({ type: 'SET_MODE', payload: mode });
      logger.info(`Mode changed to: ${mode}`);
    }, []),
    
    updateConfig: useCallback((updates: Partial<KilatConfig>) => {
      dispatch({ type: 'UPDATE_CONFIG', payload: updates });
      logger.info('Config updated:', updates);
    }, []),
    
    addPlugin: useCallback((plugin: any) => {
      dispatch({ type: 'ADD_PLUGIN', payload: plugin });
      logger.info(`Plugin added: ${plugin.name}`);
    }, []),
    
    removePlugin: useCallback((pluginName: string) => {
      dispatch({ type: 'REMOVE_PLUGIN', payload: pluginName });
      logger.info(`Plugin removed: ${pluginName}`);
    }, []),
    
    // Utilities
    restart: useCallback(async () => {
      logger.info('Restarting Kilat.js...');
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate restart
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      dispatch({ type: 'SET_LOADING', payload: false });
      logger.success('Kilat.js restarted');
    }, []),
    
    getStats: useCallback(() => {
      return {
        ...state.stats,
        uptime: Date.now() - state.stats.startTime,
        memoryUsage: getMemoryUsage()
      };
    }, [state.stats])
  };
  
  return (
    <KilatContext.Provider value={contextValue}>
      {children}
    </KilatContext.Provider>
  );
}

/**
 * 🪝 useKilat Hook
 */
export function useKilat(): KilatContextValue {
  const context = useContext(KilatContext);
  
  if (!context) {
    throw new Error(
      'useKilat must be used within a KilatProvider. ' +
      'Make sure to wrap your app with <KilatProvider>.'
    );
  }
  
  return context;
}

/**
 * 📊 Memory Usage Helper
 */
function getMemoryUsage(): number {
  if (typeof performance !== 'undefined' && 'memory' in performance) {
    return (performance as any).memory.usedJSHeapSize || 0;
  }
  return 0;
}
