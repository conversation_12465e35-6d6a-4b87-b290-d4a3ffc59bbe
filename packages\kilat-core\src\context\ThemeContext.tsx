import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  muted: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface Theme {
  name: string;
  colors: ThemeColors;
}

export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeContextValue {
  theme: string;
  mode: ThemeMode;
  colors: ThemeColors;
  setTheme: (theme: string) => void;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  availableThemes: string[];
  customThemes: Record<string, Theme>;
  addCustomTheme: (name: string, theme: Theme) => void;
  removeCustomTheme: (name: string) => void;
}

const defaultColors: ThemeColors = {
  primary: '#00ffff',
  secondary: '#ff00ff',
  accent: '#ffff00',
  background: '#000011',
  surface: '#001122',
  text: '#ffffff',
  muted: '#888888',
  border: '#333333',
  error: '#ff4757',
  warning: '#ffa502',
  success: '#2ed573',
  info: '#3742fa'
};

const defaultThemes = {
  cyberpunk: {
    name: 'cyberpunk',
    colors: {
      primary: '#00ffff',
      secondary: '#ff00ff',
      accent: '#ffff00',
      background: '#000011',
      surface: '#001122',
      text: '#ffffff',
      muted: '#888888',
      border: '#333333',
      error: '#ff4757',
      warning: '#ffa502',
      success: '#2ed573',
      info: '#3742fa'
    }
  },
  nusantara: {
    name: 'nusantara',
    colors: {
      primary: '#d4af37',
      secondary: '#8b4513',
      accent: '#ff6b35',
      background: '#1a0f0a',
      surface: '#2a1f1a',
      text: '#f5f5dc',
      muted: '#8b7355',
      border: '#654321',
      error: '#dc143c',
      warning: '#ff8c00',
      success: '#228b22',
      info: '#4169e1'
    }
  }
};

export const ThemeContext = createContext<ThemeContextValue | null>(null);

export interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: string;
  defaultMode?: ThemeMode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'cyberpunk',
  defaultMode = 'dark'
}) => {
  const [theme, setTheme] = useState(defaultTheme);
  const [mode, setMode] = useState<ThemeMode>(defaultMode);
  const [customThemes, setCustomThemes] = useState<Record<string, Theme>>({});

  const availableThemes = Object.keys(defaultThemes);
  const currentTheme = defaultThemes[theme as keyof typeof defaultThemes] || defaultThemes.cyberpunk;

  const toggleMode = () => {
    setMode(prev => prev === 'light' ? 'dark' : 'light');
  };

  const addCustomTheme = (name: string, themeData: Theme) => {
    setCustomThemes(prev => ({
      ...prev,
      [name]: themeData
    }));
  };

  const removeCustomTheme = (name: string) => {
    setCustomThemes(prev => {
      const { [name]: removed, ...rest } = prev;
      return rest;
    });
  };

  // Apply theme to document
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-kilat-theme', theme);
      document.documentElement.setAttribute('data-kilat-mode', mode);

      // Apply CSS custom properties
      const root = document.documentElement;
      Object.entries(currentTheme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--k-${key}`, value);
      });
    }
  }, [theme, mode, currentTheme]);

  const value: ThemeContextValue = {
    theme,
    mode,
    colors: currentTheme.colors,
    setTheme,
    setMode,
    toggleMode,
    availableThemes,
    customThemes,
    addCustomTheme,
    removeCustomTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useThemeContext = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};
