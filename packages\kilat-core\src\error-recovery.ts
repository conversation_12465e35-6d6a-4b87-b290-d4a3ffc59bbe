import { KilatErrorRecoveryConfig } from './types';

// 🛡️ Error Recovery System for Kilat.js
export class KilatErrorRecovery {
  private config: KilatErrorRecoveryConfig;
  private retryCount = new Map<string, number>();
  private crashReports: CrashReport[] = [];
  private isInSafeMode = false;

  constructor(config: KilatErrorRecoveryConfig) {
    this.config = {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1000,
      fallbackMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true,
      },
      killSwitch: true,
      safeMode: true,
      ...config,
    };

    this.setupGlobalErrorHandlers();
  }

  // 🚨 Setup global error handlers
  private setupGlobalErrorHandlers() {
    if (typeof window !== 'undefined') {
      // Handle unhandled promise rejections
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(new Error(event.reason), 'unhandledrejection');
      });

      // Handle JavaScript errors
      window.addEventListener('error', (event) => {
        this.handleError(event.error || new Error(event.message), 'javascript');
      });

      // Handle React error boundaries
      window.addEventListener('react-error', (event: any) => {
        this.handleError(event.detail.error, 'react');
      });
    }
  }

  // 🔄 Handle errors with retry logic
  async handleError(error: Error, source: string, context?: any): Promise<boolean> {
    if (!this.config.enabled) {
      console.error('Kilat.js Error:', error);
      return false;
    }

    const errorKey = `${source}:${error.message}`;
    const currentRetries = this.retryCount.get(errorKey) || 0;

    // Create crash report
    const crashReport = this.createCrashReport(error, source, context);
    this.crashReports.push(crashReport);

    // Check if we should retry
    if (currentRetries < this.config.maxRetries) {
      this.retryCount.set(errorKey, currentRetries + 1);
      
      console.warn(`Kilat.js: Retrying after error (${currentRetries + 1}/${this.config.maxRetries}):`, error.message);
      
      // Wait before retry
      await this.delay(this.config.retryDelay * (currentRetries + 1));
      
      return true; // Indicate retry
    }

    // Max retries reached
    console.error(`Kilat.js: Max retries reached for error:`, error);
    
    // Send crash report
    if (this.config.crashReport.enabled) {
      await this.sendCrashReport(crashReport);
    }

    // Activate safe mode if configured
    if (this.config.safeMode && !this.isInSafeMode) {
      this.activateSafeMode();
    }

    // Kill switch - disable problematic features
    if (this.config.killSwitch) {
      this.activateKillSwitch(source);
    }

    return false; // No more retries
  }

  // 📊 Create crash report
  private createCrashReport(error: Error, source: string, context?: any): CrashReport {
    return {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack || '',
      },
      source,
      context,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      retryCount: this.retryCount.get(`${source}:${error.message}`) || 0,
      logs: this.config.crashReport.includeLogs ? this.getRecentLogs() : [],
    };
  }

  // 📤 Send crash report
  private async sendCrashReport(report: CrashReport): Promise<void> {
    try {
      const { endpoint, webhookURL } = this.config.crashReport;
      
      if (endpoint) {
        await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(report),
        });
      }

      if (webhookURL) {
        await fetch(webhookURL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 Kilat.js Crash Report`,
            attachments: [{
              color: 'danger',
              title: `Error: ${report.error.name}`,
              text: report.error.message,
              fields: [
                { title: 'Source', value: report.source, short: true },
                { title: 'Retries', value: report.retryCount.toString(), short: true },
                { title: 'URL', value: report.url, short: false },
              ],
            }],
          }),
        });
      }
    } catch (sendError) {
      console.error('Failed to send crash report:', sendError);
    }
  }

  // 🛡️ Activate safe mode
  private activateSafeMode(): void {
    this.isInSafeMode = true;
    console.warn('Kilat.js: Activating safe mode due to repeated errors');
    
    // Disable non-essential features
    document.documentElement.setAttribute('data-kilat-safe-mode', 'true');
    
    // Emit safe mode event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('kilat:safe-mode', {
        detail: { active: true },
      }));
    }
  }

  // ⚡ Activate kill switch for specific source
  private activateKillSwitch(source: string): void {
    console.warn(`Kilat.js: Activating kill switch for source: ${source}`);
    
    // Disable specific features based on source
    const killSwitches: Record<string, () => void> = {
      'animation': () => {
        document.documentElement.setAttribute('data-kilat-disable-animations', 'true');
      },
      'plugin': () => {
        document.documentElement.setAttribute('data-kilat-disable-plugins', 'true');
      },
      'router': () => {
        document.documentElement.setAttribute('data-kilat-disable-router', 'true');
      },
    };

    const killSwitch = killSwitches[source];
    if (killSwitch) {
      killSwitch();
    }
  }

  // 🔄 Reset error recovery state
  resetRecovery(): void {
    this.retryCount.clear();
    this.crashReports = [];
    this.isInSafeMode = false;
    
    // Remove safe mode attributes
    document.documentElement.removeAttribute('data-kilat-safe-mode');
    document.documentElement.removeAttribute('data-kilat-disable-animations');
    document.documentElement.removeAttribute('data-kilat-disable-plugins');
    document.documentElement.removeAttribute('data-kilat-disable-router');
    
    console.log('Kilat.js: Error recovery state reset');
  }

  // 📊 Get crash reports
  getCrashReports(): CrashReport[] {
    return [...this.crashReports];
  }

  // 🛡️ Check if in safe mode
  isInSafeModeActive(): boolean {
    return this.isInSafeMode;
  }

  // 🔧 Utility methods
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private getRecentLogs(): string[] {
    // Implementation would collect recent console logs
    // This is a simplified version
    return [];
  }
}

// 📊 Crash Report Interface
export interface CrashReport {
  id: string;
  timestamp: string;
  error: {
    name: string;
    message: string;
    stack: string;
  };
  source: string;
  context?: any;
  userAgent: string;
  url: string;
  retryCount: number;
  logs: string[];
}

// 🛡️ Create global error recovery instance
let globalErrorRecovery: KilatErrorRecovery | null = null;

export function initializeErrorRecovery(config: KilatErrorRecoveryConfig): KilatErrorRecovery {
  if (!globalErrorRecovery) {
    globalErrorRecovery = new KilatErrorRecovery(config);
  }
  return globalErrorRecovery;
}

export function getErrorRecovery(): KilatErrorRecovery | null {
  return globalErrorRecovery;
}
