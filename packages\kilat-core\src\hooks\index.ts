// 🪝 Kilat.js Core Hooks
// Comprehensive collection of React hooks for Kilat.js

// Core Hooks
export { useKilat } from './useKilat';
export { useTheme } from './useTheme';
export { usePlatform } from './usePlatform';
export { useLogger } from './useLogger';
export { useRouter } from './useRouter';
// export { useAuth } from './useAuth';

// Storage Hooks (Coming Soon)
// export { useLocalStorage } from './useLocalStorage';
// export { useSessionStorage } from './useSessionStorage';
// export { useCookies } from './useCookies';
// export { useIndexedDB } from './useIndexedDB';

// Performance Hooks (Coming Soon)
// export { useDebounce } from './useDebounce';
// export { useThrottle } from './useThrottle';
// export { useAsync } from './useAsync';
// export { useMemo } from './useMemo';
// export { useCallback } from './useCallback';
// export { useOptimistic } from './useOptimistic';
// export { useDeferredValue } from './useDeferredValue';
// export { useTransition } from './useTransition';

// Timer Hooks (Coming Soon)
// export { useInterval } from './useInterval';
// export { useTimeout } from './useTimeout';
// export { useCountdown } from './useCountdown';
// export { useStopwatch } from './useStopwatch';

// Event Hooks (Coming Soon)
// export { useEventListener } from './useEventListener';
// export { useClickOutside } from './useClickOutside';
// export { useKeyboard } from './useKeyboard';
// export { useHotkeys } from './useHotkeys';
// export { useWindowEvent } from './useWindowEvent';
// export { useDocumentEvent } from './useDocumentEvent';

// Media Hooks (Coming Soon)
// export { useMediaQuery } from './useMediaQuery';
// export { useBreakpoint } from './useBreakpoint';
// export { useViewport } from './useViewport';
// export { useOrientation } from './useOrientation';
// export { usePreferredColorScheme } from './usePreferredColorScheme';

// Intersection & Scroll Hooks (Coming Soon)
// export { useIntersectionObserver } from './useIntersectionObserver';
// export { useScrollPosition } from './useScrollPosition';
// export { useScrollDirection } from './useScrollDirection';
// export { useScrollLock } from './useScrollLock';
// export { useVirtualScroll } from './useVirtualScroll';

// Device & Browser Hooks (Coming Soon)
// export { useGeolocation } from './useGeolocation';
// export { useOnlineStatus } from './useOnlineStatus';
// export { useBattery } from './useBattery';
// export { useClipboard } from './useClipboard';
// export { useFullscreen } from './useFullscreen';
// export { usePermissions } from './usePermissions';
// export { useDeviceMotion } from './useDeviceMotion';
// export { useDeviceOrientation } from './useDeviceOrientation';
// export { useNetworkInformation } from './useNetworkInformation';
// export { useWakeLock } from './useWakeLock';

// Form Hooks (Coming Soon)
// export { useForm } from './useForm';
// export { useFormField } from './useFormField';
// export { useValidation } from './useValidation';
// export { useFileUpload } from './useFileUpload';

// State Management Hooks (Coming Soon)
// export { useToggle } from './useToggle';
// export { useCounter } from './useCounter';
// export { useArray } from './useArray';
// export { useMap } from './useMap';
// export { useSet } from './useSet';
// export { useQueue } from './useQueue';
// export { useStack } from './useStack';
// export { useHistory } from './useHistory';
// export { useUndo } from './useUndo';

// Animation Hooks (Coming Soon)
// export { useSpring } from './useSpring';
// export { useTransition as useAnimationTransition } from './useAnimationTransition';
// export { useGesture } from './useGesture';
// export { useDrag } from './useDrag';
// export { useSwipe } from './useSwipe';
// export { usePinch } from './usePinch';

// Utility Hooks (Coming Soon)
// export { usePrevious } from './usePrevious';
// export { useForceUpdate } from './useForceUpdate';
// export { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';
// export { useEffectOnce } from './useEffectOnce';
// export { useUpdateEffect } from './useUpdateEffect';
// export { useDeepCompareEffect } from './useDeepCompareEffect';
// export { useWhyDidYouUpdate } from './useWhyDidYouUpdate';
// export { useRenderCount } from './useRenderCount';

// API Hooks (Coming Soon)
// export { useFetch } from './useFetch';
// export { useApi } from './useApi';
// export { useWebSocket } from './useWebSocket';
// export { useSSE } from './useSSE';
// export { useGraphQL } from './useGraphQL';

// Kilat.js Specific Hooks (Coming Soon)
// export { usePlugin } from './usePlugin';
// export { useMonitoring } from './useMonitoring';
// export { useErrorRecovery } from './useErrorRecovery';
// export { usePerformance } from './usePerformance';
// export { useAnalytics } from './useAnalytics';
// export { useFeatureFlag } from './useFeatureFlag';
// export { useExperiment } from './useExperiment';

// Types (Coming Soon)
// export type { UseAsyncReturn } from './useAsync';
// export type { UseFormReturn } from './useForm';
// export type { UseApiReturn } from './useApi';
// export type { UseWebSocketReturn } from './useWebSocket';
// export type { UseGeolocationReturn } from './useGeolocation';
// export type { UseBatteryReturn } from './useBattery';
// export type { UsePermissionsReturn } from './usePermissions';
// export type { UseMediaQueryReturn } from './useMediaQuery';
// export type { UseIntersectionObserverReturn } from './useIntersectionObserver';
