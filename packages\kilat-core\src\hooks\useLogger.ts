/**
 * 🪝 useLogger Hook
 * Logging utilities for Kilat.js applications
 */

import { useCallback, useContext } from 'react';

interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
}

interface Logger {
  debug: (message: string, ...args: any[]) => void;
  info: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;
  log: (level: keyof LogLevel, message: string, ...args: any[]) => void;
}

const LOG_LEVELS: LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

const createLogger = (context?: string): Logger => {
  const formatMessage = (level: string, message: string, ctx?: string) => {
    const timestamp = new Date().toISOString();
    const contextStr = ctx ? `[${ctx}]` : '';
    return `[${timestamp}] [${level}] ${contextStr} ${message}`;
  };

  const shouldLog = (level: keyof LogLevel): boolean => {
    const currentLevel = process.env.NODE_ENV === 'development' ? 'DEBUG' : 'INFO';
    return LOG_LEVELS[level] >= LOG_LEVELS[currentLevel as keyof LogLevel];
  };

  return {
    debug: (message: string, ...args: any[]) => {
      if (shouldLog('DEBUG')) {
        console.debug(formatMessage('DEBUG', message, context), ...args);
      }
    },

    info: (message: string, ...args: any[]) => {
      if (shouldLog('INFO')) {
        console.info(formatMessage('INFO', message, context), ...args);
      }
    },

    warn: (message: string, ...args: any[]) => {
      if (shouldLog('WARN')) {
        console.warn(formatMessage('WARN', message, context), ...args);
      }
    },

    error: (message: string, ...args: any[]) => {
      if (shouldLog('ERROR')) {
        console.error(formatMessage('ERROR', message, context), ...args);
      }
    },

    log: (level: keyof LogLevel, message: string, ...args: any[]) => {
      if (shouldLog(level)) {
        const logMethod = level.toLowerCase() as 'debug' | 'info' | 'warn' | 'error';
        console[logMethod](formatMessage(level, message, context), ...args);
      }
    }
  };
};

export const useLogger = (context?: string): Logger => {
  const debug = useCallback((message: string, ...args: any[]) => {
    createLogger(context).debug(message, ...args);
  }, [context]);

  const info = useCallback((message: string, ...args: any[]) => {
    createLogger(context).info(message, ...args);
  }, [context]);

  const warn = useCallback((message: string, ...args: any[]) => {
    createLogger(context).warn(message, ...args);
  }, [context]);

  const error = useCallback((message: string, ...args: any[]) => {
    createLogger(context).error(message, ...args);
  }, [context]);

  const log = useCallback((level: keyof LogLevel, message: string, ...args: any[]) => {
    createLogger(context).log(level, message, ...args);
  }, [context]);

  return {
    debug,
    info,
    warn,
    error,
    log
  };
};
