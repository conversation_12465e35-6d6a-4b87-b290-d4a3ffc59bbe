import { useState, useEffect, useCallback } from 'react';
import type { Platform, PlatformInfo } from '../types';

/**
 * 📱 usePlatform Hook
 * Comprehensive platform detection and information
 */
export function usePlatform(): PlatformInfo {
  const [platformInfo, setPlatformInfo] = useState<PlatformInfo>(() => 
    detectPlatform()
  );
  
  // Update platform info on window resize or orientation change
  useEffect(() => {
    const updatePlatformInfo = () => {
      setPlatformInfo(detectPlatform());
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updatePlatformInfo);
      window.addEventListener('orientationchange', updatePlatformInfo);
      
      return () => {
        window.removeEventListener('resize', updatePlatformInfo);
        window.removeEventListener('orientationchange', updatePlatformInfo);
      };
    }
  }, []);
  
  return platformInfo;
}

/**
 * 🔍 Platform Detection Function
 */
function detectPlatform(): PlatformInfo {
  if (typeof window === 'undefined') {
    return {
      type: 'server',
      isWeb: false,
      isDesktop: false,
      isMobile: false,
      isTablet: false,
      isServer: true,
      os: 'unknown',
      browser: 'unknown',
      userAgent: '',
      viewport: { width: 0, height: 0 },
      orientation: 'portrait',
      pixelRatio: 1,
      touchSupport: false,
      onlineStatus: true,
      cookieEnabled: false,
      localStorage: false,
      sessionStorage: false,
      indexedDB: false,
      webGL: false,
      webWorkers: false,
      serviceWorkers: false,
      pushNotifications: false,
      geolocation: false,
      camera: false,
      microphone: false,
      battery: false,
      vibration: false,
      fullscreen: false,
      clipboard: false,
      share: false
    };
  }
  
  const userAgent = navigator.userAgent;
  const platform = navigator.platform;
  const vendor = navigator.vendor;
  
  // Detect platform type
  let type: Platform = 'web';
  if (window.electronAPI || window.require) {
    type = 'desktop';
  } else if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    type = 'mobile';
  }
  
  // Detect OS
  let os = 'unknown';
  if (/Windows/i.test(userAgent)) os = 'windows';
  else if (/Mac/i.test(userAgent)) os = 'macos';
  else if (/Linux/i.test(userAgent)) os = 'linux';
  else if (/Android/i.test(userAgent)) os = 'android';
  else if (/iPhone|iPad|iPod/i.test(userAgent)) os = 'ios';
  
  // Detect browser
  let browser = 'unknown';
  if (/Chrome/i.test(userAgent) && !/Edge/i.test(userAgent)) browser = 'chrome';
  else if (/Firefox/i.test(userAgent)) browser = 'firefox';
  else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) browser = 'safari';
  else if (/Edge/i.test(userAgent)) browser = 'edge';
  else if (/Opera/i.test(userAgent)) browser = 'opera';
  
  // Detect device type
  const isTablet = /iPad/i.test(userAgent) || 
    (type === 'mobile' && Math.min(window.innerWidth, window.innerHeight) > 600);
  
  // Viewport info
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };
  
  // Orientation
  const orientation = viewport.width > viewport.height ? 'landscape' : 'portrait';
  
  // Device capabilities
  const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const pixelRatio = window.devicePixelRatio || 1;
  
  // Feature detection
  const features = {
    onlineStatus: navigator.onLine,
    cookieEnabled: navigator.cookieEnabled,
    localStorage: !!window.localStorage,
    sessionStorage: !!window.sessionStorage,
    indexedDB: !!window.indexedDB,
    webGL: !!window.WebGLRenderingContext,
    webWorkers: !!window.Worker,
    serviceWorkers: 'serviceWorker' in navigator,
    pushNotifications: 'PushManager' in window,
    geolocation: 'geolocation' in navigator,
    camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
    microphone: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
    battery: 'getBattery' in navigator,
    vibration: 'vibrate' in navigator,
    fullscreen: 'requestFullscreen' in document.documentElement,
    clipboard: 'clipboard' in navigator,
    share: 'share' in navigator
  };
  
  return {
    type,
    isWeb: type === 'web',
    isDesktop: type === 'desktop',
    isMobile: type === 'mobile',
    isTablet,
    isServer: false,
    os,
    browser,
    userAgent,
    viewport,
    orientation,
    pixelRatio,
    touchSupport,
    ...features
  };
}

/**
 * 📱 useIsMobile Hook
 * Quick mobile detection
 */
export function useIsMobile(): boolean {
  const { isMobile } = usePlatform();
  return isMobile;
}

/**
 * 🖥️ useIsDesktop Hook
 * Quick desktop detection
 */
export function useIsDesktop(): boolean {
  const { isDesktop } = usePlatform();
  return isDesktop;
}

/**
 * 📱 useIsTablet Hook
 * Quick tablet detection
 */
export function useIsTablet(): boolean {
  const { isTablet } = usePlatform();
  return isTablet;
}

/**
 * 🌐 useIsOnline Hook
 * Online status detection
 */
export function useIsOnline(): boolean {
  const [isOnline, setIsOnline] = useState(() => 
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);
  
  return isOnline;
}

/**
 * 📐 useViewport Hook
 * Viewport dimensions and orientation
 */
export function useViewport() {
  const { viewport, orientation } = usePlatform();
  
  return {
    width: viewport.width,
    height: viewport.height,
    orientation,
    isLandscape: orientation === 'landscape',
    isPortrait: orientation === 'portrait'
  };
}

/**
 * 🔧 usePlatformFeatures Hook
 * Platform feature detection
 */
export function usePlatformFeatures() {
  const platform = usePlatform();
  
  return {
    touchSupport: platform.touchSupport,
    localStorage: platform.localStorage,
    sessionStorage: platform.sessionStorage,
    indexedDB: platform.indexedDB,
    webGL: platform.webGL,
    webWorkers: platform.webWorkers,
    serviceWorkers: platform.serviceWorkers,
    pushNotifications: platform.pushNotifications,
    geolocation: platform.geolocation,
    camera: platform.camera,
    microphone: platform.microphone,
    battery: platform.battery,
    vibration: platform.vibration,
    fullscreen: platform.fullscreen,
    clipboard: platform.clipboard,
    share: platform.share
  };
}
