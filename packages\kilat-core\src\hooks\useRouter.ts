/**
 * 🪝 useRouter Hook
 * Router utilities for Kilat.js applications
 */

import { useCallback, useEffect, useState } from 'react';

interface RouterState {
  pathname: string;
  search: string;
  hash: string;
  state: any;
}

interface Router {
  pathname: string;
  search: string;
  hash: string;
  state: any;
  push: (path: string, state?: any) => void;
  replace: (path: string, state?: any) => void;
  go: (delta: number) => void;
  back: () => void;
  forward: () => void;
  listen: (listener: (location: RouterState) => void) => () => void;
}

const getLocationState = (): RouterState => {
  if (typeof window === 'undefined') {
    return {
      pathname: '/',
      search: '',
      hash: '',
      state: null
    };
  }

  return {
    pathname: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash,
    state: window.history.state
  };
};

export const useRouter = (): Router => {
  const [location, setLocation] = useState<RouterState>(getLocationState);

  useEffect(() => {
    const handlePopState = () => {
      setLocation(getLocationState());
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handlePopState);
      return () => window.removeEventListener('popstate', handlePopState);
    }
  }, []);

  const push = useCallback((path: string, state?: any) => {
    if (typeof window !== 'undefined') {
      window.history.pushState(state, '', path);
      setLocation(getLocationState());
    }
  }, []);

  const replace = useCallback((path: string, state?: any) => {
    if (typeof window !== 'undefined') {
      window.history.replaceState(state, '', path);
      setLocation(getLocationState());
    }
  }, []);

  const go = useCallback((delta: number) => {
    if (typeof window !== 'undefined') {
      window.history.go(delta);
    }
  }, []);

  const back = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.history.back();
    }
  }, []);

  const forward = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.history.forward();
    }
  }, []);

  const listen = useCallback((listener: (location: RouterState) => void) => {
    const handlePopState = () => {
      listener(getLocationState());
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handlePopState);
      return () => window.removeEventListener('popstate', handlePopState);
    }

    return () => {};
  }, []);

  return {
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    state: location.state,
    push,
    replace,
    go,
    back,
    forward,
    listen
  };
};
