// 🚀 Kilat.js Core - Production Ready Framework
// Complete export of all core functionality

// 🎯 Core Exports (Basic)
export * from './context';
export * from './hooks';
export * from './components';
export * from './utils';
// export * from './types';
// export * from './layout';
// export * from './error-recovery';
// export * from './platform';
// export * from './plugin-system';
// export * from './ssr';
// export * from './performance';
// export * from './testing';

// 🧱 Main Kilat.js Core Components
export {
  KilatProvider,
  useKilat,
  useTheme,
  useLoading,
  useError,
  useKilatStore
} from './context';

// export {
//   KilatLayout,
//   LayoutManager,
//   useLayout,
//   DevLayout
// } from './layout';

// // 🛡️ Error Recovery
// export {
//   KilatErrorRecovery,
//   initializeErrorRecovery,
//   getErrorRecovery,
//   type CrashReport
// } from './error-recovery';

// // 🌐 Platform Detection
// export {
//   KilatPlatform,
//   usePlatform,
//   kilatPlatform,
//   withPlatform,
//   PlatformRenderer
// } from './platform';

// // 🔌 Plugin System
// export {
//   KilatPluginSystem,
//   getPluginSystem,
//   usePluginSystem,
//   type PluginState
// } from './plugin-system';

// 🎨 Theme utilities
export const KILAT_THEMES = [
  'cyberpunk',
  'nusantara', 
  'retro',
  'material',
  'neumorphism',
  'carbon',
  'minimalist',
  'asymetric',
  'elemen3d',
  'dana',
  'ark',
  'aurora',
  'unix',
  'classic'
] as const;

export const KILAT_ANIMATION_PRESETS = [
  'galaxy',
  'matrix', 
  'neonTunnel',
  'cyberwave',
  'glowParticles'
] as const;

// 🔧 Utility functions (Coming Soon)
// export function createKilatConfig(config: Partial<import('./types').KilatConfig>): import('./types').KilatConfig {
  const defaultConfig: import('./types').KilatConfig = {
    theme: 'cyberpunk',
    mode: 'dark',
    presetScene: 'galaxy',
    animation: {
      autoRotate: true,
      background: '#000',
      interactive: true,
      ambientSound: false
    },
    router: {
      basePath: '/',
      middleware: [],
      fileBasedRouting: true,
      dynamicRoutes: true,
      layoutNesting: true
    },
    database: {
      driver: 'sqlite',
      connection: {
        sqlite: { 
          file: './data.db',
          enableWAL: true,
          timeout: 5000
        }
      },
      migrations: {
        directory: './migrations',
        autoRun: true
      }
    },
    build: {
      engine: 'kilatpack',
      target: 'es2022',
      minify: true,
      sourcemap: true,
      debugOverlay: false,
      hotReload: true,
      analyze: false,
      outputDir: 'dist',
      publicDir: 'public',
      assetsDir: 'assets'
    },
    plugins: [],
    aiAssistant: {
      enabled: false,
      endpoint: '/api/ai',
      model: 'gpt-4',
      features: []
    },
    platform: {
      web: {
        enabled: true,
        ssr: true,
        pwa: true
      },
      desktop: {
        enabled: false,
        electron: true,
        tauri: false
      },
      mobile: {
        enabled: false,
        expo: true,
        capacitor: false
      }
    },
    dev: {
      port: 3000,
      host: 'localhost',
      open: true,
      cors: true,
      proxy: {},
      hmr: {
        port: 24678,
        overlay: true
      }
    },
    performance: {
      bundleSplitting: true,
      treeshaking: true,
      compression: 'gzip',
      caching: {
        enabled: true,
        strategy: 'stale-while-revalidate'
      }
    },
    security: {
      csp: {
        enabled: true,
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", "'unsafe-inline'"],
          'style-src': ["'self'", "'unsafe-inline'"],
          'img-src': ["'self'", "data:", "https:"]
        }
      },
      cors: {
        origin: ['http://localhost:3000'],
        credentials: true
      }
    },
    errorRecovery: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1000,
      fallbackMode: true,
      crashReport: {
        enabled: true,
        includeLogs: true,
        autoRetry: true,
      },
      killSwitch: true,
      safeMode: true,
    },
    monitoring: {
      enabled: false,
      performance: true,
      errors: true,
      analytics: false,
      sampleRate: 0.1,
    }
  };

  return { ...defaultConfig, ...config };
}

// 🌐 Platform detection utilities
export function detectPlatform(): import('./types').KilatPlatformType {
  if (typeof window === 'undefined') return 'web'; // SSR
  
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/electron/.test(userAgent)) return 'desktop';
  if (/mobile|android|iphone|ipad/.test(userAgent)) return 'mobile';
  
  return 'web';
}

export function getPlatformInfo(): import('./types').KilatPlatformInfo {
  const type = detectPlatform();
  
  return {
    type,
    isWeb: type === 'web',
    isDesktop: type === 'desktop', 
    isMobile: type === 'mobile',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    viewport: {
      width: typeof window !== 'undefined' ? window.innerWidth : 0,
      height: typeof window !== 'undefined' ? window.innerHeight : 0
    }
  };
}

// 🎯 Version info
export const KILAT_VERSION = '1.0.0';
export const KILAT_CORE_VERSION = '1.0.0';
