import React, { Suspense, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useKilat, useTheme } from './context';
import type { KilatLayoutProps } from './types';

// 🚨 Error Fallback Component
function ErrorFallback({ error, resetErrorBoundary }: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) {
  return (
    <div className="k-error-boundary k-min-h-screen k-flex k-items-center k-justify-center k-bg-dark k-text-light">
      <div className="k-text-center k-p-8">
        <div className="k-text-6xl k-mb-4">⚡</div>
        <h1 className="k-text-2xl k-font-bold k-mb-4 k-text-glow-red">
          Oops! Something went wrong
        </h1>
        <p className="k-text-gray-400 k-mb-6 k-max-w-md">
          {error.message || 'An unexpected error occurred'}
        </p>
        <button
          onClick={resetErrorBoundary}
          className="k-btn k-bg-neon-blue k-text-dark k-px-6 k-py-3 k-rounded-lg k-font-semibold k-transition-all k-duration-300 hover:k-scale-105"
        >
          Try Again
        </button>
        {process.env.NODE_ENV === 'development' && (
          <details className="k-mt-6 k-text-left k-bg-gray-900 k-p-4 k-rounded k-text-sm">
            <summary className="k-cursor-pointer k-text-yellow-400">
              Error Details (Development)
            </summary>
            <pre className="k-mt-2 k-text-red-400 k-overflow-auto">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

// 🔄 Loading Component
function LoadingFallback() {
  return (
    <div className="k-loading-fallback k-min-h-screen k-flex k-items-center k-justify-center k-bg-dark">
      <div className="k-text-center">
        <div className="k-animate-spin k-text-6xl k-mb-4 k-text-glow-blue">⚡</div>
        <p className="k-text-light k-text-lg k-animate-pulse">
          Loading Kilat.js...
        </p>
      </div>
    </div>
  );
}

// 🧱 Base Layout Component
export function KilatLayout({ 
  children, 
  theme, 
  mode, 
  className = '' 
}: KilatLayoutProps) {
  const { config } = useKilat();
  const { theme: currentTheme, mode: currentMode } = useTheme();
  
  const appliedTheme = theme || currentTheme;
  const appliedMode = mode || currentMode;

  return (
    <div 
      className={`kilat-layout ${className}`}
      data-kilat-theme={appliedTheme}
      data-kilat-mode={appliedMode}
    >
      <ErrorBoundary
        FallbackComponent={ErrorFallback}
        onError={(error, errorInfo) => {
          console.error('Kilat.js Error:', error, errorInfo);
          // Send to error reporting service if configured
          if (config?.dev?.hmr?.overlay) {
            // Show development overlay
          }
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          {children}
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}

// 🌐 SSR-aware Layout Manager
interface LayoutManagerProps {
  children: ReactNode;
  isSSR?: boolean;
  layoutComponent?: React.ComponentType<any>;
  layoutProps?: Record<string, any>;
}

export function LayoutManager({ 
  children, 
  isSSR = false,
  layoutComponent: CustomLayout,
  layoutProps = {}
}: LayoutManagerProps) {
  const { config } = useKilat();
  
  // SSR optimization
  if (isSSR) {
    return (
      <div className="kilat-ssr-layout" suppressHydrationWarning>
        {CustomLayout ? (
          <CustomLayout {...layoutProps}>
            {children}
          </CustomLayout>
        ) : (
          <KilatLayout {...layoutProps}>
            {children}
          </KilatLayout>
        )}
      </div>
    );
  }

  // Client-side rendering
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset application state if needed
        window.location.reload();
      }}
    >
      {CustomLayout ? (
        <CustomLayout {...layoutProps}>
          <Suspense fallback={<LoadingFallback />}>
            {children}
          </Suspense>
        </CustomLayout>
      ) : (
        <KilatLayout {...layoutProps}>
          {children}
        </KilatLayout>
      )}
    </ErrorBoundary>
  );
}

// 🎯 Layout Hook for dynamic layout switching
export function useLayout() {
  const { config } = useKilat();
  
  return {
    setLayout: (layoutComponent: React.ComponentType<any>, props?: Record<string, any>) => {
      // Implementation for dynamic layout switching
      // This would integrate with the router system
    },
    getCurrentLayout: () => {
      // Return current layout component
    },
    isSSR: typeof window === 'undefined',
    config: config?.platform,
  };
}

// 🔧 Development Layout with Debug Info
export function DevLayout({ children }: { children: ReactNode }) {
  const { config, theme, mode } = useKilat();
  
  if (process.env.NODE_ENV !== 'development') {
    return <>{children}</>;
  }

  return (
    <div className="kilat-dev-layout">
      {config?.build?.debugOverlay && (
        <div className="k-fixed k-top-0 k-right-0 k-z-50 k-bg-black k-bg-opacity-80 k-text-white k-p-2 k-text-xs k-font-mono">
          <div>Theme: {theme}</div>
          <div>Mode: {mode}</div>
          <div>Platform: {config.platform.web.enabled ? 'Web' : 'Unknown'}</div>
          <div>HMR: {config.build.hotReload ? '🟢' : '🔴'}</div>
        </div>
      )}
      <KilatLayout>
        {children}
      </KilatLayout>
    </div>
  );
}
