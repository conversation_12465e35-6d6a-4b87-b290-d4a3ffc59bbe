import React from 'react';
import { KilatPlatformType, KilatPlatformInfo } from './types';

// 🌐 Platform Detection Utilities for Kilat.js
export class KilatPlatform {
  private static instance: KilatPlatform;
  private platformInfo: KilatPlatformInfo;

  private constructor() {
    this.platformInfo = this.detectPlatform();
    this.setupPlatformAttributes();
  }

  static getInstance(): KilatPlatform {
    if (!KilatPlatform.instance) {
      KilatPlatform.instance = new KilatPlatform();
    }
    return KilatPlatform.instance;
  }

  // 🔍 Detect current platform
  private detectPlatform(): KilatPlatformInfo {
    const isSSR = typeof window === 'undefined';
    
    if (isSSR) {
      return {
        type: 'web',
        isWeb: true,
        isDesktop: false,
        isMobile: false,
        userAgent: 'SSR',
        viewport: { width: 0, height: 0 },
      };
    }

    const userAgent = navigator.userAgent;
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    // Detect Electron
    const isElectron = !!(window as any).require || 
                      !!(window as any).process?.type ||
                      !!(window as any).electronAPI;

    // Detect mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
                     (window.innerWidth <= 768 && 'ontouchstart' in window);

    // Detect desktop
    const isDesktop = !isMobile || isElectron;

    // Determine platform type
    let type: KilatPlatformType = 'web';
    if (isElectron) {
      type = 'desktop';
    } else if (isMobile) {
      type = 'mobile';
    }

    return {
      type,
      isWeb: !isElectron && !isMobile,
      isDesktop,
      isMobile,
      userAgent,
      viewport,
    };
  }

  // 🏷️ Setup platform attributes on document
  private setupPlatformAttributes(): void {
    if (typeof document === 'undefined') return;

    const { type, isWeb, isDesktop, isMobile } = this.platformInfo;
    
    document.documentElement.setAttribute('data-kilat-platform', type);
    
    if (isWeb) document.documentElement.setAttribute('data-kilat-web', 'true');
    if (isDesktop) document.documentElement.setAttribute('data-kilat-desktop', 'true');
    if (isMobile) document.documentElement.setAttribute('data-kilat-mobile', 'true');

    // Add viewport classes for responsive design
    this.updateViewportClasses();
    
    // Listen for viewport changes
    window.addEventListener('resize', () => {
      this.updateViewport();
      this.updateViewportClasses();
    });
  }

  // 📱 Update viewport information
  private updateViewport(): void {
    if (typeof window === 'undefined') return;
    
    this.platformInfo.viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  }

  // 📐 Update viewport classes for responsive design
  private updateViewportClasses(): void {
    if (typeof document === 'undefined') return;

    const { width } = this.platformInfo.viewport;
    const root = document.documentElement;

    // Remove existing viewport classes
    root.classList.remove('k-xs', 'k-sm', 'k-md', 'k-lg', 'k-xl', 'k-2xl');

    // Add appropriate viewport class
    if (width < 640) {
      root.classList.add('k-xs');
    } else if (width < 768) {
      root.classList.add('k-sm');
    } else if (width < 1024) {
      root.classList.add('k-md');
    } else if (width < 1280) {
      root.classList.add('k-lg');
    } else if (width < 1536) {
      root.classList.add('k-xl');
    } else {
      root.classList.add('k-2xl');
    }
  }

  // 🔍 Get platform information
  getPlatformInfo(): KilatPlatformInfo {
    return { ...this.platformInfo };
  }

  // 🌐 Platform check methods
  isWeb(): boolean {
    return this.platformInfo.isWeb;
  }

  isDesktop(): boolean {
    return this.platformInfo.isDesktop;
  }

  isMobile(): boolean {
    return this.platformInfo.isMobile;
  }

  getType(): KilatPlatformType {
    return this.platformInfo.type;
  }

  // 📱 Device-specific checks
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(this.platformInfo.userAgent);
  }

  isAndroid(): boolean {
    return /Android/.test(this.platformInfo.userAgent);
  }

  isElectron(): boolean {
    return this.platformInfo.type === 'desktop' && 
           (typeof window !== 'undefined' && 
            (!!(window as any).require || 
             !!(window as any).process?.type ||
             !!(window as any).electronAPI));
  }

  // 🔧 Browser detection
  getBrowser(): string {
    const userAgent = this.platformInfo.userAgent;
    
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    if (userAgent.includes('Opera')) return 'opera';
    
    return 'unknown';
  }

  // 📐 Viewport utilities
  getViewport() {
    return { ...this.platformInfo.viewport };
  }

  isPortrait(): boolean {
    const { width, height } = this.platformInfo.viewport;
    return height > width;
  }

  isLandscape(): boolean {
    return !this.isPortrait();
  }

  // 🎯 Feature detection
  supportsTouch(): boolean {
    return typeof window !== 'undefined' && 'ontouchstart' in window;
  }

  supportsWebGL(): boolean {
    if (typeof window === 'undefined') return false;
    
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  }

  supportsWebAssembly(): boolean {
    return typeof WebAssembly !== 'undefined';
  }

  supportsServiceWorker(): boolean {
    return typeof navigator !== 'undefined' && 'serviceWorker' in navigator;
  }

  // 🔄 Refresh platform detection
  refresh(): void {
    this.platformInfo = this.detectPlatform();
    this.setupPlatformAttributes();
  }
}

// 🪝 React Hook for platform detection
export function usePlatform() {
  const platform = KilatPlatform.getInstance();
  
  return {
    ...platform.getPlatformInfo(),
    isWeb: platform.isWeb(),
    isDesktop: platform.isDesktop(),
    isMobile: platform.isMobile(),
    isIOS: platform.isIOS(),
    isAndroid: platform.isAndroid(),
    isElectron: platform.isElectron(),
    browser: platform.getBrowser(),
    viewport: platform.getViewport(),
    isPortrait: platform.isPortrait(),
    isLandscape: platform.isLandscape(),
    supportsTouch: platform.supportsTouch(),
    supportsWebGL: platform.supportsWebGL(),
    supportsWebAssembly: platform.supportsWebAssembly(),
    supportsServiceWorker: platform.supportsServiceWorker(),
    refresh: platform.refresh.bind(platform),
  };
}

// 🌐 Global platform instance
export const kilatPlatform = KilatPlatform.getInstance();

// 🔧 Platform-aware component wrapper
export function withPlatform<T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function PlatformAwareComponent(props: T) {
    const platform = usePlatform();

    return React.createElement(Component, {
      ...props,
      platform,
    } as T);
  };
}

// 📱 Platform-specific rendering
export function PlatformRenderer({ 
  web, 
  desktop, 
  mobile, 
  fallback 
}: {
  web?: React.ReactNode;
  desktop?: React.ReactNode;
  mobile?: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const platform = usePlatform();
  
  if (platform.isWeb && web) return web;
  if (platform.isDesktop && desktop) return desktop;
  if (platform.isMobile && mobile) return mobile;
  
  return fallback || null;
}
