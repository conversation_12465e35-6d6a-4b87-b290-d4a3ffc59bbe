import { KilatPlugin, KilatContextValue } from './types';
import { getErrorRecovery } from './error-recovery';

// 🔌 Plugin System for Kilat.js
export class KilatPluginSystem {
  private plugins = new Map<string, KilatPlugin>();
  private pluginStates = new Map<string, PluginState>();
  private hooks = new Map<string, PluginHook[]>();
  private context: KilatContextValue | null = null;

  constructor() {
    this.setupDefaultHooks();
  }

  // 🎯 Setup default plugin hooks
  private setupDefaultHooks(): void {
    this.hooks.set('init', []);
    this.hooks.set('request', []);
    this.hooks.set('response', []);
    this.hooks.set('error', []);
    this.hooks.set('destroy', []);
  }

  // 📦 Register a plugin
  async registerPlugin(plugin: KilatPlugin): Promise<boolean> {
    try {
      // Validate plugin
      if (!plugin.name) {
        throw new Error('Plugin must have a name');
      }

      if (this.plugins.has(plugin.name)) {
        console.warn(`Plugin ${plugin.name} is already registered`);
        return false;
      }

      // Initialize plugin state
      this.pluginStates.set(plugin.name, {
        name: plugin.name,
        version: plugin.version || '1.0.0',
        enabled: true,
        retryCount: 0,
        maxRetries: 3,
        lastError: null,
        status: 'initializing',
      });

      // Register plugin
      this.plugins.set(plugin.name, plugin);

      // Initialize plugin
      await this.initializePlugin(plugin);

      console.log(`✅ Plugin ${plugin.name} registered successfully`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to register plugin ${plugin.name}:`, error);
      await this.handlePluginError(plugin.name, error as Error);
      return false;
    }
  }

  // 🚀 Initialize a plugin
  private async initializePlugin(plugin: KilatPlugin): Promise<void> {
    const state = this.pluginStates.get(plugin.name);
    if (!state) return;

    try {
      state.status = 'initializing';

      // Call plugin init hook
      if (plugin.onInit && this.context) {
        await plugin.onInit(this.context);
      }

      // Register plugin hooks
      this.registerPluginHooks(plugin);

      state.status = 'active';
      state.lastError = null;
      
      console.log(`🚀 Plugin ${plugin.name} initialized`);
    } catch (error) {
      state.status = 'error';
      state.lastError = error as Error;
      throw error;
    }
  }

  // 🪝 Register plugin hooks
  private registerPluginHooks(plugin: KilatPlugin): void {
    const hooks = [
      { name: 'request', handler: plugin.onRequest },
      { name: 'response', handler: plugin.onResponse },
      { name: 'error', handler: plugin.onError },
    ];

    hooks.forEach(({ name, handler }) => {
      if (handler) {
        const hookList = this.hooks.get(name) || [];
        hookList.push({
          pluginName: plugin.name,
          handler,
        });
        this.hooks.set(name, hookList);
      }
    });
  }

  // 🔄 Execute hooks
  async executeHooks(hookName: string, context?: any): Promise<void> {
    const hooks = this.hooks.get(hookName) || [];
    
    for (const hook of hooks) {
      const state = this.pluginStates.get(hook.pluginName);
      
      // Skip disabled or errored plugins
      if (!state || !state.enabled || state.status === 'error') {
        continue;
      }

      try {
        await hook.handler(context);
      } catch (error) {
        console.error(`Hook ${hookName} failed for plugin ${hook.pluginName}:`, error);
        await this.handlePluginError(hook.pluginName, error as Error);
      }
    }
  }

  // 🚨 Handle plugin errors with retry logic
  private async handlePluginError(pluginName: string, error: Error): Promise<void> {
    const state = this.pluginStates.get(pluginName);
    if (!state) return;

    state.lastError = error;
    state.retryCount++;

    // Use global error recovery if available
    const errorRecovery = getErrorRecovery();
    if (errorRecovery) {
      const shouldRetry = await errorRecovery.handleError(error, 'plugin', { pluginName });
      
      if (shouldRetry && state.retryCount < state.maxRetries) {
        console.log(`🔄 Retrying plugin ${pluginName} (${state.retryCount}/${state.maxRetries})`);
        
        // Attempt to reinitialize plugin
        const plugin = this.plugins.get(pluginName);
        if (plugin) {
          try {
            await this.initializePlugin(plugin);
            return;
          } catch (retryError) {
            console.error(`Retry failed for plugin ${pluginName}:`, retryError);
          }
        }
      }
    }

    // Max retries reached or no error recovery
    if (state.retryCount >= state.maxRetries) {
      console.error(`❌ Plugin ${pluginName} disabled after ${state.maxRetries} failed attempts`);
      state.enabled = false;
      state.status = 'disabled';
      
      // Emit plugin disabled event
      this.emitPluginEvent('disabled', { pluginName, error });
    }
  }

  // 📤 Emit plugin events
  private emitPluginEvent(eventName: string, data: any): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`kilat:plugin:${eventName}`, {
        detail: data,
      }));
    }
  }

  // 🔧 Plugin management methods
  enablePlugin(pluginName: string): boolean {
    const state = this.pluginStates.get(pluginName);
    if (!state) return false;

    state.enabled = true;
    state.retryCount = 0;
    state.lastError = null;
    
    console.log(`✅ Plugin ${pluginName} enabled`);
    return true;
  }

  disablePlugin(pluginName: string): boolean {
    const state = this.pluginStates.get(pluginName);
    if (!state) return false;

    state.enabled = false;
    console.log(`❌ Plugin ${pluginName} disabled`);
    return true;
  }

  unregisterPlugin(pluginName: string): boolean {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) return false;

    // Remove from hooks
    this.hooks.forEach((hooks, hookName) => {
      const filtered = hooks.filter(h => h.pluginName !== pluginName);
      this.hooks.set(hookName, filtered);
    });

    // Remove plugin and state
    this.plugins.delete(pluginName);
    this.pluginStates.delete(pluginName);

    console.log(`🗑️ Plugin ${pluginName} unregistered`);
    return true;
  }

  // 📊 Get plugin information
  getPluginState(pluginName: string): PluginState | null {
    return this.pluginStates.get(pluginName) || null;
  }

  getAllPluginStates(): PluginState[] {
    return Array.from(this.pluginStates.values());
  }

  getActivePlugins(): string[] {
    return Array.from(this.pluginStates.entries())
      .filter(([_, state]) => state.enabled && state.status === 'active')
      .map(([name]) => name);
  }

  getFailedPlugins(): string[] {
    return Array.from(this.pluginStates.entries())
      .filter(([_, state]) => state.status === 'error' || state.status === 'disabled')
      .map(([name]) => name);
  }

  // 🎯 Set context for plugins
  setContext(context: KilatContextValue): void {
    this.context = context;
  }

  // 🔄 Restart all failed plugins
  async restartFailedPlugins(): Promise<void> {
    const failedPlugins = this.getFailedPlugins();
    
    for (const pluginName of failedPlugins) {
      const plugin = this.plugins.get(pluginName);
      const state = this.pluginStates.get(pluginName);
      
      if (plugin && state) {
        console.log(`🔄 Restarting plugin ${pluginName}`);
        
        // Reset state
        state.enabled = true;
        state.retryCount = 0;
        state.lastError = null;
        state.status = 'initializing';
        
        try {
          await this.initializePlugin(plugin);
        } catch (error) {
          console.error(`Failed to restart plugin ${pluginName}:`, error);
        }
      }
    }
  }

  // 🧹 Cleanup all plugins
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up plugins...');
    
    // Execute destroy hooks
    await this.executeHooks('destroy');
    
    // Clear all data
    this.plugins.clear();
    this.pluginStates.clear();
    this.hooks.clear();
    this.context = null;
    
    // Setup default hooks again
    this.setupDefaultHooks();
  }
}

// 🔌 Plugin State Interface
export interface PluginState {
  name: string;
  version: string;
  enabled: boolean;
  retryCount: number;
  maxRetries: number;
  lastError: Error | null;
  status: 'initializing' | 'active' | 'error' | 'disabled';
}

// 🪝 Plugin Hook Interface
interface PluginHook {
  pluginName: string;
  handler: (context?: any) => void | Promise<void>;
}

// 🌐 Global plugin system instance
let globalPluginSystem: KilatPluginSystem | null = null;

export function getPluginSystem(): KilatPluginSystem {
  if (!globalPluginSystem) {
    globalPluginSystem = new KilatPluginSystem();
  }
  return globalPluginSystem;
}

// 🪝 React Hook for plugin system
export function usePluginSystem() {
  const pluginSystem = getPluginSystem();
  
  return {
    registerPlugin: pluginSystem.registerPlugin.bind(pluginSystem),
    enablePlugin: pluginSystem.enablePlugin.bind(pluginSystem),
    disablePlugin: pluginSystem.disablePlugin.bind(pluginSystem),
    unregisterPlugin: pluginSystem.unregisterPlugin.bind(pluginSystem),
    getPluginState: pluginSystem.getPluginState.bind(pluginSystem),
    getAllPluginStates: pluginSystem.getAllPluginStates.bind(pluginSystem),
    getActivePlugins: pluginSystem.getActivePlugins.bind(pluginSystem),
    getFailedPlugins: pluginSystem.getFailedPlugins.bind(pluginSystem),
    restartFailedPlugins: pluginSystem.restartFailedPlugins.bind(pluginSystem),
  };
}
