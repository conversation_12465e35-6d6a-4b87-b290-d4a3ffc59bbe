/**
 * 🧪 Act Testing Utilities
 * React act utility for testing
 */

// Simple act implementation for testing
export const act = async (callback: () => void | Promise<void>): Promise<void> => {
  // If React's act is available, use it
  if (typeof window !== 'undefined' && (window as any).React?.act) {
    return (window as any).React.act(callback);
  }

  // Otherwise, provide a simple implementation
  try {
    const result = callback();
    if (result && typeof result.then === 'function') {
      await result;
    }
  } catch (error) {
    throw error;
  }

  // Allow any pending updates to flush
  await new Promise(resolve => setTimeout(resolve, 0));
};
