/**
 * 🧪 Cleanup Testing Utilities
 * Test cleanup and teardown utilities
 */

let cleanupCallbacks: (() => void)[] = [];

export const cleanup = (): void => {
  // Run all cleanup callbacks
  cleanupCallbacks.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  });

  // Clear the callbacks array
  cleanupCallbacks = [];

  // Clean up DOM if in browser environment
  if (typeof document !== 'undefined') {
    // Remove all elements with data-testid
    const testElements = document.querySelectorAll('[data-testid]');
    testElements.forEach(element => {
      element.remove();
    });

    // Clear any test containers
    const testContainers = document.querySelectorAll('[data-test-container]');
    testContainers.forEach(container => {
      container.remove();
    });

    // Reset document title
    document.title = '';

    // Clear any inline styles added during tests
    const styleElements = document.querySelectorAll('style[data-test-style]');
    styleElements.forEach(style => {
      style.remove();
    });
  }

  // Clear any global test state
  if (typeof window !== 'undefined') {
    // Clear any test data from window object
    Object.keys(window).forEach(key => {
      if (key.startsWith('__TEST_')) {
        delete (window as any)[key];
      }
    });

    // Clear localStorage test data
    if (window.localStorage) {
      Object.keys(window.localStorage).forEach(key => {
        if (key.startsWith('test_') || key.startsWith('__TEST_')) {
          window.localStorage.removeItem(key);
        }
      });
    }

    // Clear sessionStorage test data
    if (window.sessionStorage) {
      Object.keys(window.sessionStorage).forEach(key => {
        if (key.startsWith('test_') || key.startsWith('__TEST_')) {
          window.sessionStorage.removeItem(key);
        }
      });
    }
  }
};

export const addCleanupCallback = (callback: () => void): void => {
  cleanupCallbacks.push(callback);
};

export const removeCleanupCallback = (callback: () => void): void => {
  const index = cleanupCallbacks.indexOf(callback);
  if (index > -1) {
    cleanupCallbacks.splice(index, 1);
  }
};

// Auto cleanup on process exit (Node.js environment)
if (typeof process !== 'undefined' && process.on) {
  process.on('exit', cleanup);
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
}

// Auto cleanup on page unload (browser environment)
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanup);
}
