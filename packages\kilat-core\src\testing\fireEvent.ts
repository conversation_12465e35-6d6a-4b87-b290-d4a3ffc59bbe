/**
 * 🧪 Fire Event Testing Utilities
 * Event simulation for testing
 */

export const fireEvent = {
  click: (element: Element) => {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
  },

  change: (element: Element, value: string) => {
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      element.value = value;
    }
    
    const event = new Event('change', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  input: (element: Element, value: string) => {
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      element.value = value;
    }
    
    const event = new Event('input', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  keyDown: (element: Element, key: string) => {
    const event = new KeyboardEvent('keydown', {
      key,
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  keyUp: (element: Element, key: string) => {
    const event = new KeyboardEvent('keyup', {
      key,
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  mouseOver: (element: Element) => {
    const event = new MouseEvent('mouseover', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
  },

  mouseOut: (element: Element) => {
    const event = new MouseEvent('mouseout', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
  },

  focus: (element: Element) => {
    if (element instanceof HTMLElement) {
      element.focus();
    }
    
    const event = new FocusEvent('focus', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  },

  blur: (element: Element) => {
    if (element instanceof HTMLElement) {
      element.blur();
    }
    
    const event = new FocusEvent('blur', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  }
};
