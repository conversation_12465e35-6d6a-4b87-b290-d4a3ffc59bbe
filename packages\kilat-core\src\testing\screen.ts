/**
 * 🧪 Screen Testing Utilities
 * DOM query utilities for testing
 */

export const screen = {
  getByText: (text: string | RegExp) => {
    if (typeof document === 'undefined') {
      throw new Error('screen.getByText can only be used in browser environment');
    }
    
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      const textContent = el.textContent || '';
      return typeof text === 'string' 
        ? textContent.includes(text)
        : text.test(textContent);
    });
    
    if (elements.length === 0) {
      throw new Error(`Unable to find element with text: ${text}`);
    }
    
    return elements[0];
  },

  getByTestId: (testId: string) => {
    if (typeof document === 'undefined') {
      throw new Error('screen.getByTestId can only be used in browser environment');
    }
    
    const element = document.querySelector(`[data-testid="${testId}"]`);
    if (!element) {
      throw new Error(`Unable to find element with testId: ${testId}`);
    }
    
    return element;
  },

  getByRole: (role: string) => {
    if (typeof document === 'undefined') {
      throw new Error('screen.getByRole can only be used in browser environment');
    }
    
    const element = document.querySelector(`[role="${role}"]`);
    if (!element) {
      throw new Error(`Unable to find element with role: ${role}`);
    }
    
    return element;
  },

  queryByText: (text: string | RegExp) => {
    if (typeof document === 'undefined') {
      return null;
    }
    
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      const textContent = el.textContent || '';
      return typeof text === 'string' 
        ? textContent.includes(text)
        : text.test(textContent);
    });
    
    return elements[0] || null;
  },

  queryByTestId: (testId: string) => {
    if (typeof document === 'undefined') {
      return null;
    }
    
    return document.querySelector(`[data-testid="${testId}"]`);
  },

  queryByRole: (role: string) => {
    if (typeof document === 'undefined') {
      return null;
    }
    
    return document.querySelector(`[role="${role}"]`);
  }
};
