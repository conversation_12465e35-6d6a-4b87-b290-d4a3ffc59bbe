/**
 * 🧪 Wait For Testing Utilities
 * Async waiting utilities for testing
 */

interface WaitForOptions {
  timeout?: number;
  interval?: number;
}

export const waitFor = async (
  callback: () => void | Promise<void>,
  options: WaitForOptions = {}
): Promise<void> => {
  const { timeout = 5000, interval = 50 } = options;
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      await callback();
      return;
    } catch (error) {
      // Continue waiting
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }

  // Final attempt
  await callback();
};

export const waitForElement = async (
  selector: string,
  options: WaitForOptions = {}
): Promise<Element> => {
  let element: Element | null = null;

  await waitFor(() => {
    element = document.querySelector(selector);
    if (!element) {
      throw new Error(`Element with selector "${selector}" not found`);
    }
  }, options);

  return element!;
};

export const waitForElementToBeRemoved = async (
  selector: string,
  options: WaitForOptions = {}
): Promise<void> => {
  await waitFor(() => {
    const element = document.querySelector(selector);
    if (element) {
      throw new Error(`Element with selector "${selector}" still exists`);
    }
  }, options);
};

export const waitForText = async (
  text: string | RegExp,
  options: WaitForOptions = {}
): Promise<Element> => {
  let element: Element | null = null;

  await waitFor(() => {
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      const textContent = el.textContent || '';
      return typeof text === 'string' 
        ? textContent.includes(text)
        : text.test(textContent);
    });

    element = elements[0] || null;
    if (!element) {
      throw new Error(`Element with text "${text}" not found`);
    }
  }, options);

  return element!;
};
