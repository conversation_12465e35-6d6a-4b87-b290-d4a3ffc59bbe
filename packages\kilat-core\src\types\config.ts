// 🔧 Kilat.js Configuration Types

import type { ThemeMode } from './theme';
import type { DatabaseConfig } from './database';
import type { ServerConfig } from './server';
import type { SecurityConfig } from './security';
import type { PerformanceConfig } from './performance';
import type { ErrorRecoveryConfig } from './error';
import type { I18nConfig } from './i18n';
import type { PWAConfig } from './pwa';
import type { SEOConfig } from './seo';
import type { MonitoringConfig } from './monitoring';
import type { AnimationConfig } from './animation';
import type { PluginConfig } from './plugin';

/**
 * 🎯 Main Kilat.js Configuration
 */
export interface KilatConfig {
  // 🎨 Theme Configuration
  theme?: string;
  mode?: ThemeMode;
  
  // 🧭 Router Configuration
  router?: RouterConfig;
  
  // 🗃️ Database Configuration
  database?: DatabaseConfig;
  
  // 🖥️ Backend Configuration
  backend?: ServerConfig;
  
  // 🌌 Animation Configuration
  animation?: AnimationConfig;
  
  // 🔌 Plugin Configuration
  plugins?: Record<string, PluginConfig>;
  
  // 🛡️ Error Handling Configuration
  errorHandling?: ErrorRecoveryConfig;
  
  // 🔧 Development Configuration
  development?: DevelopmentConfig;
  
  // 🚀 Build Configuration
  build?: BuildConfig;
  
  // 🌐 Internationalization
  i18n?: I18nConfig;
  
  // 📱 PWA Configuration
  pwa?: PWAConfig;
  
  // 🔒 Security Configuration
  security?: SecurityConfig;
  
  // 📊 Analytics Configuration
  analytics?: AnalyticsConfig;
  
  // 📊 Monitoring Configuration
  monitoring?: MonitoringConfig;
  
  // 🎯 SEO Configuration
  seo?: SEOConfig;
  
  // ⚡ Performance Configuration
  performance?: PerformanceConfig;
  
  // 🧪 Testing Configuration
  testing?: TestingConfig;
  
  // 📦 Package Configuration
  package?: PackageConfig;
}

/**
 * 🧭 Router Configuration
 */
export interface RouterConfig {
  basePath?: string;
  middleware?: string[];
  transitions?: {
    type?: 'fade' | 'slide' | 'scale' | 'none';
    duration?: number;
    easing?: string;
  };
  preload?: boolean;
  trailingSlash?: boolean;
  caseSensitive?: boolean;
  strictMode?: boolean;
  hashRouter?: boolean;
  scrollRestoration?: boolean;
  prefetch?: boolean | 'hover' | 'visible';
}

/**
 * 🔧 Development Configuration
 */
export interface DevelopmentConfig {
  hotReload?: boolean;
  debugMode?: boolean;
  sourceMap?: boolean;
  devtools?: boolean;
  mockData?: boolean;
  apiMocking?: boolean;
  liveReload?: boolean;
  overlay?: boolean;
  notifications?: boolean;
  openBrowser?: boolean;
  port?: number;
  host?: string;
  https?: boolean;
  proxy?: Record<string, string>;
}

/**
 * 🚀 Build Configuration
 */
export interface BuildConfig {
  target?: 'es5' | 'es2015' | 'es2017' | 'es2018' | 'es2019' | 'es2020' | 'esnext';
  outDir?: string;
  sourcemap?: boolean | 'inline' | 'hidden';
  minify?: boolean | 'terser' | 'esbuild';
  splitting?: boolean;
  treeshaking?: boolean;
  compression?: {
    gzip?: boolean;
    brotli?: boolean;
  };
  bundleAnalyzer?: boolean;
  publicPath?: string;
  assetsDir?: string;
  chunkSizeWarningLimit?: number;
  rollupOptions?: any;
  esbuildOptions?: any;
  terserOptions?: any;
}

/**
 * 📊 Analytics Configuration
 */
export interface AnalyticsConfig {
  enabled?: boolean;
  providers?: AnalyticsProvider[];
  events?: {
    pageViews?: boolean;
    userInteractions?: boolean;
    errors?: boolean;
    performance?: boolean;
  };
  privacy?: {
    anonymizeIp?: boolean;
    respectDNT?: boolean;
    cookieConsent?: boolean;
  };
}

export interface AnalyticsProvider {
  name: string;
  trackingId: string;
  config?: Record<string, any>;
}

/**
 * 🧪 Testing Configuration
 */
export interface TestingConfig {
  framework?: 'vitest' | 'jest' | 'playwright';
  coverage?: {
    enabled?: boolean;
    threshold?: {
      statements?: number;
      branches?: number;
      functions?: number;
      lines?: number;
    };
    exclude?: string[];
  };
  e2e?: {
    baseUrl?: string;
    browsers?: string[];
    headless?: boolean;
    video?: boolean;
    screenshots?: boolean;
  };
  mocks?: {
    api?: boolean;
    localStorage?: boolean;
    sessionStorage?: boolean;
    fetch?: boolean;
  };
}

/**
 * 📦 Package Configuration
 */
export interface PackageConfig {
  name?: string;
  version?: string;
  description?: string;
  author?: string;
  license?: string;
  repository?: string;
  homepage?: string;
  keywords?: string[];
  exports?: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  peerDependencies?: Record<string, string>;
}

/**
 * 🎯 Environment Configuration
 */
export interface EnvironmentConfig {
  NODE_ENV?: 'development' | 'production' | 'test';
  KILAT_ENV?: 'development' | 'staging' | 'production';
  DEBUG?: boolean;
  LOG_LEVEL?: 'debug' | 'info' | 'warn' | 'error';
  API_URL?: string;
  CDN_URL?: string;
  SENTRY_DSN?: string;
  GA_TRACKING_ID?: string;
}

/**
 * 🔧 Runtime Configuration
 */
export interface RuntimeConfig {
  ssr?: boolean;
  hydration?: boolean;
  streaming?: boolean;
  prerendering?: boolean;
  staticGeneration?: boolean;
  serverComponents?: boolean;
  clientComponents?: boolean;
  edgeRuntime?: boolean;
}

/**
 * 📱 Platform Configuration
 */
export interface PlatformConfig {
  web?: WebPlatformConfig;
  mobile?: MobilePlatformConfig;
  desktop?: DesktopPlatformConfig;
  server?: ServerPlatformConfig;
}

export interface WebPlatformConfig {
  spa?: boolean;
  ssr?: boolean;
  ssg?: boolean;
  pwa?: boolean;
  amp?: boolean;
}

export interface MobilePlatformConfig {
  expo?: boolean;
  reactNative?: boolean;
  capacitor?: boolean;
  cordova?: boolean;
}

export interface DesktopPlatformConfig {
  electron?: boolean;
  tauri?: boolean;
  neutralino?: boolean;
}

export interface ServerPlatformConfig {
  node?: boolean;
  deno?: boolean;
  bun?: boolean;
  edge?: boolean;
}

/**
 * 🎨 Theme Configuration
 */
export interface ThemeConfig {
  default?: string;
  available?: string[];
  custom?: Record<string, any>;
  cssVariables?: boolean;
  darkMode?: 'class' | 'media' | 'manual';
  colorScheme?: 'light' | 'dark' | 'auto';
}

/**
 * 🔄 Configuration Validation
 */
export interface ConfigValidation {
  required?: string[];
  optional?: string[];
  deprecated?: string[];
  schema?: any;
  validate?: (config: KilatConfig) => boolean | string[];
}

/**
 * 📝 Configuration Metadata
 */
export interface ConfigMetadata {
  version: string;
  schema: string;
  generated: string;
  environment: string;
  platform: string;
  checksum: string;
}
