// 🎯 Kilat.js Core Types
// Comprehensive type definitions for the framework

// Core Types
export type { KilatConfig } from './config';
export type { KilatContextValue, KilatState, KilatAction } from './context';
export type { Theme, ThemeMode, ThemeColors, ThemeContextValue } from './theme';
export type { Platform, PlatformInfo, PlatformType } from './platform';
export type { Logger, LogLevel, LogEntry } from './logger';
export type { Component, ComponentProps, ComponentRef } from './component';

// Authentication Types
export type { 
  User, 
  AuthState, 
  AuthContextValue, 
  LoginCredentials, 
  RegisterData,
  AuthProvider,
  AuthSession,
  AuthTokens
} from './auth';

// Router Types
export type { 
  Route, 
  RouteParams, 
  RouteQuery, 
  RouterContextValue,
  NavigationOptions,
  RouteComponent,
  RouteGuard,
  RouteMiddleware
} from './router';

// Plugin Types
export type { 
  Plugin, 
  PluginConfig, 
  PluginContext, 
  PluginLifecycle,
  PluginManifest,
  PluginRegistry
} from './plugin';

// Error Types
export type { 
  ErrorInfo, 
  ErrorBoundaryState, 
  CrashReport,
  ErrorRecoveryConfig,
  ErrorHandler
} from './error';

// Performance Types
export type { 
  PerformanceMetrics, 
  PerformanceConfig,
  PerformanceEntry,
  PerformanceObserver
} from './performance';

// API Types
export type { 
  ApiResponse, 
  ApiError, 
  ApiConfig,
  RequestOptions,
  ResponseInterceptor,
  RequestInterceptor
} from './api';

// Storage Types
export type { 
  StorageAdapter, 
  StorageConfig,
  StorageItem,
  StorageEvent
} from './storage';

// Animation Types
export type { 
  AnimationConfig, 
  AnimationPreset,
  AnimationScene,
  AnimationControls
} from './animation';

// Form Types
export type { 
  FormConfig, 
  FormField, 
  FormValidation,
  FormState,
  FormErrors,
  FormSubmission
} from './form';

// Utility Types
export type { 
  DeepPartial, 
  DeepRequired, 
  Prettify,
  UnionToIntersection,
  NonEmptyArray,
  ValueOf,
  KeyOf,
  Optional,
  Required,
  Nullable,
  NonNullable
} from './utility';

// Event Types
export type { 
  EventHandler, 
  EventListener, 
  CustomEvent,
  EventEmitter,
  EventSubscription
} from './event';

// Hook Types
export type { 
  UseAsyncReturn, 
  UseFormReturn, 
  UseApiReturn,
  UseWebSocketReturn,
  UseGeolocationReturn,
  UseBatteryReturn,
  UsePermissionsReturn,
  UseMediaQueryReturn,
  UseIntersectionObserverReturn,
  UseLocalStorageReturn,
  UseSessionStorageReturn
} from './hooks';

// Testing Types
export type { 
  TestConfig, 
  TestSuite, 
  TestCase,
  MockFunction,
  TestRenderer,
  TestUtils
} from './testing';

// Build Types
export type { 
  BuildConfig, 
  BuildTarget, 
  BuildMode,
  BuildOptimization,
  BuildAsset,
  BuildManifest
} from './build';

// Monitoring Types
export type { 
  MonitoringConfig, 
  Metric, 
  Alert,
  Dashboard,
  MetricCollector,
  AlertRule
} from './monitoring';

// Internationalization Types
export type { 
  I18nConfig, 
  Locale, 
  Translation,
  TranslationKey,
  TranslationFunction,
  LocaleDetector
} from './i18n';

// SEO Types
export type { 
  SEOConfig, 
  MetaTags, 
  StructuredData,
  OpenGraph,
  TwitterCard,
  Sitemap
} from './seo';

// PWA Types
export type { 
  PWAConfig, 
  ServiceWorkerConfig,
  ManifestConfig,
  CacheStrategy,
  PushNotification
} from './pwa';

// Security Types
export type { 
  SecurityConfig, 
  CSPDirectives,
  CORSConfig,
  RateLimitConfig,
  AuthenticationConfig
} from './security';

// Database Types
export type { 
  DatabaseConfig, 
  Model, 
  Schema,
  Migration,
  Seed,
  QueryBuilder,
  Relationship
} from './database';

// Server Types
export type { 
  ServerConfig, 
  Middleware, 
  RequestHandler,
  ResponseHandler,
  WebSocketHandler,
  GraphQLConfig
} from './server';
