import { createLogger } from 'kilat-utils';
import type { 
  DatabaseConfig, 
  Model, 
  QueryBuilder, 
  Transaction, 
  Migration,
  DatabaseAdapter,
  ModelDefinition,
  QueryOptions,
  RelationDefinition
} from '../types';

/**
 * 🗃️ KilatORM - Universal ORM for multiple databases
 * Supports PostgreSQL, MySQL, SQLite, MongoDB, and more
 */
export class KilatORM {
  private config: DatabaseConfig;
  private adapter: DatabaseAdapter;
  private models = new Map<string, Model>();
  private logger = createLogger({ prefix: 'KilatORM' });
  private isConnected = false;
  private connectionPool?: any;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.adapter = this.createAdapter(config);
  }

  // 🔌 Create database adapter
  private createAdapter(config: DatabaseConfig): DatabaseAdapter {
    switch (config.type) {
      case 'postgresql':
        return new PostgreSQLAdapter(config);
      case 'mysql':
        return new MySQLAdapter(config);
      case 'sqlite':
        return new SQLiteAdapter(config);
      case 'mongodb':
        return new MongoDBAdapter(config);
      case 'redis':
        return new RedisAdapter(config);
      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }
  }

  // 🚀 Connect to database
  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      this.logger.info(`Connecting to ${this.config.type} database...`);
      
      await this.adapter.connect();
      this.isConnected = true;
      
      this.logger.success(`Connected to ${this.config.type} database`);
      
      // Run pending migrations if enabled
      if (this.config.migrations?.auto) {
        await this.runMigrations();
      }
      
    } catch (error) {
      this.logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  // 🔌 Disconnect from database
  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.adapter.disconnect();
      this.isConnected = false;
      this.logger.info('Disconnected from database');
    } catch (error) {
      this.logger.error('Failed to disconnect from database', error);
      throw error;
    }
  }

  // 📊 Define model
  define<T = any>(name: string, definition: ModelDefinition): Model<T> {
    const model = new KilatModel<T>(name, definition, this.adapter, this.logger);
    this.models.set(name, model);
    
    this.logger.debug(`Model defined: ${name}`);
    return model;
  }

  // 🔍 Get model
  model<T = any>(name: string): Model<T> {
    const model = this.models.get(name);
    
    if (!model) {
      throw new Error(`Model not found: ${name}`);
    }
    
    return model as Model<T>;
  }

  // 🔄 Create transaction
  async transaction<T>(callback: (trx: Transaction) => Promise<T>): Promise<T> {
    const trx = await this.adapter.beginTransaction();
    
    try {
      const result = await callback(trx);
      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  // 🏃‍♂️ Run migrations
  async runMigrations(): Promise<void> {
    if (!this.config.migrations?.directory) {
      return;
    }

    try {
      this.logger.info('Running database migrations...');
      
      const migrator = new KilatMigrator(this.adapter, this.config.migrations);
      await migrator.run();
      
      this.logger.success('Migrations completed');
    } catch (error) {
      this.logger.error('Migration failed', error);
      throw error;
    }
  }

  // 📊 Execute raw query
  async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    return this.adapter.query(sql, params);
  }

  // 🔧 Get query builder
  queryBuilder(): QueryBuilder {
    return new KilatQueryBuilder(this.adapter);
  }

  // 📊 Database health check
  async healthCheck(): Promise<{
    connected: boolean;
    latency: number;
    version?: string;
    stats?: any;
  }> {
    const startTime = Date.now();
    
    try {
      const result = await this.adapter.healthCheck();
      const latency = Date.now() - startTime;
      
      return {
        connected: true,
        latency,
        ...result
      };
    } catch (error) {
      return {
        connected: false,
        latency: Date.now() - startTime
      };
    }
  }

  // 📊 Get database statistics
  async getStats(): Promise<{
    tables: number;
    totalSize: string;
    connections: number;
    queries: number;
    slowQueries: number;
  }> {
    return this.adapter.getStats();
  }

  // 🧹 Cleanup resources
  async cleanup(): Promise<void> {
    await this.disconnect();
    this.models.clear();
  }
}

/**
 * 📋 KilatModel - Model implementation
 */
class KilatModel<T = any> implements Model<T> {
  constructor(
    public name: string,
    public definition: ModelDefinition,
    private adapter: DatabaseAdapter,
    private logger: any
  ) {}

  // 🔍 Find records
  async find(options: QueryOptions = {}): Promise<T[]> {
    const query = this.buildQuery('SELECT', options);
    return this.adapter.query(query.sql, query.params);
  }

  // 🔍 Find one record
  async findOne(options: QueryOptions = {}): Promise<T | null> {
    const results = await this.find({ ...options, limit: 1 });
    return results[0] || null;
  }

  // 🔍 Find by ID
  async findById(id: any): Promise<T | null> {
    return this.findOne({ where: { [this.definition.primaryKey || 'id']: id } });
  }

  // ➕ Create record
  async create(data: Partial<T>): Promise<T> {
    const query = this.buildInsertQuery(data);
    const result = await this.adapter.query(query.sql, query.params);
    
    // Return the created record
    if (result.insertId) {
      return this.findById(result.insertId);
    }
    
    return data as T;
  }

  // ➕ Create multiple records
  async createMany(data: Partial<T>[]): Promise<T[]> {
    const results: T[] = [];
    
    for (const item of data) {
      const created = await this.create(item);
      results.push(created);
    }
    
    return results;
  }

  // 🔄 Update records
  async update(where: any, data: Partial<T>): Promise<number> {
    const query = this.buildUpdateQuery(where, data);
    const result = await this.adapter.query(query.sql, query.params);
    return result.affectedRows || 0;
  }

  // 🔄 Update by ID
  async updateById(id: any, data: Partial<T>): Promise<T | null> {
    const updated = await this.update({ [this.definition.primaryKey || 'id']: id }, data);
    
    if (updated > 0) {
      return this.findById(id);
    }
    
    return null;
  }

  // 🗑️ Delete records
  async delete(where: any): Promise<number> {
    const query = this.buildDeleteQuery(where);
    const result = await this.adapter.query(query.sql, query.params);
    return result.affectedRows || 0;
  }

  // 🗑️ Delete by ID
  async deleteById(id: any): Promise<boolean> {
    const deleted = await this.delete({ [this.definition.primaryKey || 'id']: id });
    return deleted > 0;
  }

  // 📊 Count records
  async count(options: QueryOptions = {}): Promise<number> {
    const query = this.buildQuery('COUNT', options);
    const result = await this.adapter.query(query.sql, query.params);
    return result[0]?.count || 0;
  }

  // 📄 Paginate records
  async paginate(page: number = 1, limit: number = 10, options: QueryOptions = {}): Promise<{
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const offset = (page - 1) * limit;
    const total = await this.count(options);
    const data = await this.find({ ...options, limit, offset });
    
    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 🔗 Load relations
  async loadRelations(records: T[], relations: string[]): Promise<T[]> {
    for (const relation of relations) {
      const relationDef = this.definition.relations?.[relation];
      
      if (!relationDef) {
        this.logger.warn(`Relation not found: ${relation}`);
        continue;
      }
      
      await this.loadRelation(records, relation, relationDef);
    }
    
    return records;
  }

  // 🔗 Load single relation
  private async loadRelation(records: T[], relationName: string, relation: RelationDefinition): Promise<void> {
    // Implementation depends on relation type (hasOne, hasMany, belongsTo, belongsToMany)
    switch (relation.type) {
      case 'hasOne':
      case 'hasMany':
        await this.loadHasRelation(records, relationName, relation);
        break;
      case 'belongsTo':
        await this.loadBelongsToRelation(records, relationName, relation);
        break;
      case 'belongsToMany':
        await this.loadBelongsToManyRelation(records, relationName, relation);
        break;
    }
  }

  // 🔗 Load has relation (hasOne, hasMany)
  private async loadHasRelation(records: T[], relationName: string, relation: RelationDefinition): Promise<void> {
    const foreignKey = relation.foreignKey || `${this.name.toLowerCase()}_id`;
    const localKey = relation.localKey || 'id';
    
    const localValues = records.map(record => (record as any)[localKey]);
    const relatedModel = new KilatModel(relation.model, { fields: {} }, this.adapter, this.logger);
    
    const relatedRecords = await relatedModel.find({
      where: { [foreignKey]: { $in: localValues } }
    });
    
    // Group related records by foreign key
    const groupedRecords = new Map();
    relatedRecords.forEach(record => {
      const key = (record as any)[foreignKey];
      if (!groupedRecords.has(key)) {
        groupedRecords.set(key, []);
      }
      groupedRecords.get(key).push(record);
    });
    
    // Attach to parent records
    records.forEach(record => {
      const key = (record as any)[localKey];
      const related = groupedRecords.get(key) || [];
      
      (record as any)[relationName] = relation.type === 'hasOne' ? related[0] : related;
    });
  }

  // 🔗 Load belongs to relation
  private async loadBelongsToRelation(records: T[], relationName: string, relation: RelationDefinition): Promise<void> {
    const foreignKey = relation.foreignKey || `${relation.model.toLowerCase()}_id`;
    const localKey = relation.localKey || 'id';
    
    const foreignValues = records.map(record => (record as any)[foreignKey]).filter(Boolean);
    const relatedModel = new KilatModel(relation.model, { fields: {} }, this.adapter, this.logger);
    
    const relatedRecords = await relatedModel.find({
      where: { [localKey]: { $in: foreignValues } }
    });
    
    // Create lookup map
    const lookupMap = new Map();
    relatedRecords.forEach(record => {
      lookupMap.set((record as any)[localKey], record);
    });
    
    // Attach to parent records
    records.forEach(record => {
      const foreignValue = (record as any)[foreignKey];
      (record as any)[relationName] = lookupMap.get(foreignValue) || null;
    });
  }

  // 🔗 Load belongs to many relation
  private async loadBelongsToManyRelation(records: T[], relationName: string, relation: RelationDefinition): Promise<void> {
    // Implementation for many-to-many relationships through pivot table
    // This would require more complex logic involving the pivot table
    this.logger.warn(`belongsToMany relation loading not yet implemented: ${relationName}`);
  }

  // 🔧 Build query
  private buildQuery(type: 'SELECT' | 'COUNT', options: QueryOptions): { sql: string; params: any[] } {
    const tableName = this.definition.tableName || this.name.toLowerCase();
    let sql = '';
    const params: any[] = [];
    
    if (type === 'SELECT') {
      sql = `SELECT * FROM ${tableName}`;
    } else {
      sql = `SELECT COUNT(*) as count FROM ${tableName}`;
    }
    
    // Add WHERE clause
    if (options.where) {
      const whereClause = this.buildWhereClause(options.where, params);
      sql += ` WHERE ${whereClause}`;
    }
    
    // Add ORDER BY
    if (options.orderBy && type === 'SELECT') {
      sql += ` ORDER BY ${options.orderBy}`;
    }
    
    // Add LIMIT and OFFSET
    if (options.limit && type === 'SELECT') {
      sql += ` LIMIT ${options.limit}`;
      
      if (options.offset) {
        sql += ` OFFSET ${options.offset}`;
      }
    }
    
    return { sql, params };
  }

  // 🔧 Build WHERE clause
  private buildWhereClause(where: any, params: any[]): string {
    const conditions: string[] = [];
    
    for (const [key, value] of Object.entries(where)) {
      if (typeof value === 'object' && value !== null) {
        // Handle operators like $in, $gt, $lt, etc.
        for (const [operator, operatorValue] of Object.entries(value)) {
          switch (operator) {
            case '$in':
              conditions.push(`${key} IN (${(operatorValue as any[]).map(() => '?').join(', ')})`);
              params.push(...(operatorValue as any[]));
              break;
            case '$gt':
              conditions.push(`${key} > ?`);
              params.push(operatorValue);
              break;
            case '$lt':
              conditions.push(`${key} < ?`);
              params.push(operatorValue);
              break;
            case '$gte':
              conditions.push(`${key} >= ?`);
              params.push(operatorValue);
              break;
            case '$lte':
              conditions.push(`${key} <= ?`);
              params.push(operatorValue);
              break;
            case '$ne':
              conditions.push(`${key} != ?`);
              params.push(operatorValue);
              break;
            case '$like':
              conditions.push(`${key} LIKE ?`);
              params.push(operatorValue);
              break;
          }
        }
      } else {
        conditions.push(`${key} = ?`);
        params.push(value);
      }
    }
    
    return conditions.join(' AND ');
  }

  // 🔧 Build INSERT query
  private buildInsertQuery(data: Partial<T>): { sql: string; params: any[] } {
    const tableName = this.definition.tableName || this.name.toLowerCase();
    const keys = Object.keys(data);
    const values = Object.values(data);
    
    const sql = `INSERT INTO ${tableName} (${keys.join(', ')}) VALUES (${keys.map(() => '?').join(', ')})`;
    
    return { sql, params: values };
  }

  // 🔧 Build UPDATE query
  private buildUpdateQuery(where: any, data: Partial<T>): { sql: string; params: any[] } {
    const tableName = this.definition.tableName || this.name.toLowerCase();
    const keys = Object.keys(data);
    const values = Object.values(data);
    const params: any[] = [...values];
    
    const setClause = keys.map(key => `${key} = ?`).join(', ');
    let sql = `UPDATE ${tableName} SET ${setClause}`;
    
    if (where) {
      const whereClause = this.buildWhereClause(where, params);
      sql += ` WHERE ${whereClause}`;
    }
    
    return { sql, params };
  }

  // 🔧 Build DELETE query
  private buildDeleteQuery(where: any): { sql: string; params: any[] } {
    const tableName = this.definition.tableName || this.name.toLowerCase();
    const params: any[] = [];
    
    let sql = `DELETE FROM ${tableName}`;
    
    if (where) {
      const whereClause = this.buildWhereClause(where, params);
      sql += ` WHERE ${whereClause}`;
    }
    
    return { sql, params };
  }
}

// Database adapters would be implemented separately
class PostgreSQLAdapter implements DatabaseAdapter {
  constructor(private config: DatabaseConfig) {}
  
  async connect(): Promise<void> {
    // PostgreSQL connection implementation
  }
  
  async disconnect(): Promise<void> {
    // PostgreSQL disconnection implementation
  }
  
  async query<T>(sql: string, params?: any[]): Promise<T[]> {
    // PostgreSQL query implementation
    return [];
  }
  
  async beginTransaction(): Promise<Transaction> {
    // PostgreSQL transaction implementation
    return {} as Transaction;
  }
  
  async healthCheck(): Promise<any> {
    return {};
  }
  
  async getStats(): Promise<any> {
    return {};
  }
}

class MySQLAdapter implements DatabaseAdapter {
  constructor(private config: DatabaseConfig) {}
  
  async connect(): Promise<void> {}
  async disconnect(): Promise<void> {}
  async query<T>(sql: string, params?: any[]): Promise<T[]> { return []; }
  async beginTransaction(): Promise<Transaction> { return {} as Transaction; }
  async healthCheck(): Promise<any> { return {}; }
  async getStats(): Promise<any> { return {}; }
}

class SQLiteAdapter implements DatabaseAdapter {
  constructor(private config: DatabaseConfig) {}
  
  async connect(): Promise<void> {}
  async disconnect(): Promise<void> {}
  async query<T>(sql: string, params?: any[]): Promise<T[]> { return []; }
  async beginTransaction(): Promise<Transaction> { return {} as Transaction; }
  async healthCheck(): Promise<any> { return {}; }
  async getStats(): Promise<any> { return {}; }
}

class MongoDBAdapter implements DatabaseAdapter {
  constructor(private config: DatabaseConfig) {}
  
  async connect(): Promise<void> {}
  async disconnect(): Promise<void> {}
  async query<T>(sql: string, params?: any[]): Promise<T[]> { return []; }
  async beginTransaction(): Promise<Transaction> { return {} as Transaction; }
  async healthCheck(): Promise<any> { return {}; }
  async getStats(): Promise<any> { return {}; }
}

class RedisAdapter implements DatabaseAdapter {
  constructor(private config: DatabaseConfig) {}
  
  async connect(): Promise<void> {}
  async disconnect(): Promise<void> {}
  async query<T>(sql: string, params?: any[]): Promise<T[]> { return []; }
  async beginTransaction(): Promise<Transaction> { return {} as Transaction; }
  async healthCheck(): Promise<any> { return {}; }
  async getStats(): Promise<any> { return {}; }
}

class KilatQueryBuilder implements QueryBuilder {
  constructor(private adapter: DatabaseAdapter) {}
  
  select(fields: string[]): QueryBuilder { return this; }
  from(table: string): QueryBuilder { return this; }
  where(condition: any): QueryBuilder { return this; }
  join(table: string, condition: string): QueryBuilder { return this; }
  orderBy(field: string, direction?: 'ASC' | 'DESC'): QueryBuilder { return this; }
  limit(count: number): QueryBuilder { return this; }
  offset(count: number): QueryBuilder { return this; }
  
  async execute<T>(): Promise<T[]> {
    return [];
  }
  
  toSQL(): { sql: string; params: any[] } {
    return { sql: '', params: [] };
  }
}

class KilatMigrator {
  constructor(private adapter: DatabaseAdapter, private config: any) {}
  
  async run(): Promise<void> {
    // Migration implementation
  }
}
