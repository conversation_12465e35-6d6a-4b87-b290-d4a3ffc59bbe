import { useState, useEffect, useCallback, useRef } from 'react';
import { createLogger } from 'kilat-utils';
import type { 
  DatabaseConfig, 
  Model, 
  QueryOptions, 
  PaginationResult,
  DatabaseHookOptions,
  DatabaseState,
  CacheOptions
} from '../types';
import { KilatORM } from '../core/KilatORM';

/**
 * 🗃️ useDatabase Hook - React integration for KilatORM
 * Provides reactive database operations with caching and optimistic updates
 */
export function useDatabase<T = any>(
  modelName: string, 
  options: DatabaseHookOptions = {}
): DatabaseState<T> {
  const {
    autoFetch = true,
    fetchOptions = {},
    cache = { enabled: true, ttl: 300000 },
    optimisticUpdates = true,
    errorRetry = { enabled: true, attempts: 3, delay: 1000 }
  } = options;

  // State management
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Refs for stable references
  const ormRef = useRef<KilatORM | null>(null);
  const modelRef = useRef<Model<T> | null>(null);
  const cacheRef = useRef(new Map<string, { data: any; timestamp: number }>());
  const abortControllerRef = useRef<AbortController | null>(null);
  const logger = createLogger({ prefix: 'useDatabase' });

  // Initialize ORM and model
  useEffect(() => {
    if (!ormRef.current) {
      // Get ORM instance from context or create new one
      ormRef.current = getORMInstance();
      modelRef.current = ormRef.current.model<T>(modelName);
    }
  }, [modelName]);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetch && modelRef.current) {
      fetchData(fetchOptions);
    }

    return () => {
      // Cleanup: abort ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [autoFetch, modelName]);

  // 🔍 Fetch data with caching
  const fetchData = useCallback(async (queryOptions: QueryOptions = {}) => {
    if (!modelRef.current) {
      setError(new Error('Model not initialized'));
      return;
    }

    // Check cache first
    const cacheKey = generateCacheKey(modelName, queryOptions);
    const cachedData = getCachedData(cacheKey, cache);
    
    if (cachedData) {
      setData(cachedData);
      return cachedData;
    }

    // Abort previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    setLoading(true);
    setError(null);

    try {
      const result = await modelRef.current.find(queryOptions);
      
      // Cache the result
      setCachedData(cacheKey, result, cache);
      
      setData(result);
      setLoading(false);
      
      return result;
    } catch (err) {
      if (err.name !== 'AbortError') {
        const error = err as Error;
        setError(error);
        logger.error(`Failed to fetch ${modelName}`, error);
        
        // Retry logic
        if (errorRetry.enabled) {
          await retryOperation(() => fetchData(queryOptions), errorRetry);
        }
      }
      setLoading(false);
      throw err;
    }
  }, [modelName, cache, errorRetry, logger]);

  // 📄 Paginated fetch
  const fetchPaginated = useCallback(async (
    page: number = 1, 
    limit: number = 10, 
    queryOptions: QueryOptions = {}
  ) => {
    if (!modelRef.current) {
      setError(new Error('Model not initialized'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await modelRef.current.paginate(page, limit, queryOptions);
      
      setData(result.data);
      setPagination({
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages
      });
      
      setLoading(false);
      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      setLoading(false);
      throw error;
    }
  }, []);

  // ➕ Create record with optimistic updates
  const create = useCallback(async (recordData: Partial<T>) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    // Optimistic update
    if (optimisticUpdates) {
      const optimisticRecord = { 
        ...recordData, 
        id: `temp_${Date.now()}`,
        _isOptimistic: true 
      } as T;
      
      setData(prev => [optimisticRecord, ...prev]);
    }

    try {
      const created = await modelRef.current.create(recordData);
      
      // Replace optimistic record with real one
      if (optimisticUpdates) {
        setData(prev => prev.map(item => 
          (item as any)._isOptimistic ? created : item
        ));
      } else {
        setData(prev => [created, ...prev]);
      }
      
      // Invalidate cache
      invalidateCache(modelName);
      
      return created;
    } catch (error) {
      // Revert optimistic update
      if (optimisticUpdates) {
        setData(prev => prev.filter(item => !(item as any)._isOptimistic));
      }
      throw error;
    }
  }, [modelName, optimisticUpdates]);

  // 🔄 Update record with optimistic updates
  const update = useCallback(async (id: any, updateData: Partial<T>) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    // Store original data for rollback
    const originalData = [...data];
    
    // Optimistic update
    if (optimisticUpdates) {
      setData(prev => prev.map(item => 
        (item as any).id === id ? { ...item, ...updateData } : item
      ));
    }

    try {
      const updated = await modelRef.current.updateById(id, updateData);
      
      if (updated) {
        setData(prev => prev.map(item => 
          (item as any).id === id ? updated : item
        ));
      }
      
      // Invalidate cache
      invalidateCache(modelName);
      
      return updated;
    } catch (error) {
      // Revert optimistic update
      if (optimisticUpdates) {
        setData(originalData);
      }
      throw error;
    }
  }, [data, modelName, optimisticUpdates]);

  // 🗑️ Delete record with optimistic updates
  const remove = useCallback(async (id: any) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    // Store original data for rollback
    const originalData = [...data];
    
    // Optimistic update
    if (optimisticUpdates) {
      setData(prev => prev.filter(item => (item as any).id !== id));
    }

    try {
      const deleted = await modelRef.current.deleteById(id);
      
      if (!optimisticUpdates && deleted) {
        setData(prev => prev.filter(item => (item as any).id !== id));
      }
      
      // Invalidate cache
      invalidateCache(modelName);
      
      return deleted;
    } catch (error) {
      // Revert optimistic update
      if (optimisticUpdates) {
        setData(originalData);
      }
      throw error;
    }
  }, [data, modelName, optimisticUpdates]);

  // 🔍 Find one record
  const findOne = useCallback(async (queryOptions: QueryOptions) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    try {
      return await modelRef.current.findOne(queryOptions);
    } catch (error) {
      logger.error(`Failed to find one ${modelName}`, error);
      throw error;
    }
  }, [modelName, logger]);

  // 🔍 Find by ID
  const findById = useCallback(async (id: any) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    try {
      return await modelRef.current.findById(id);
    } catch (error) {
      logger.error(`Failed to find ${modelName} by ID`, error);
      throw error;
    }
  }, [modelName, logger]);

  // 📊 Count records
  const count = useCallback(async (queryOptions: QueryOptions = {}) => {
    if (!modelRef.current) {
      throw new Error('Model not initialized');
    }

    try {
      return await modelRef.current.count(queryOptions);
    } catch (error) {
      logger.error(`Failed to count ${modelName}`, error);
      throw error;
    }
  }, [modelName, logger]);

  // 🔄 Refresh data
  const refresh = useCallback(() => {
    invalidateCache(modelName);
    return fetchData(fetchOptions);
  }, [modelName, fetchData, fetchOptions]);

  // 🧹 Clear data
  const clear = useCallback(() => {
    setData([]);
    setError(null);
    setPagination({ page: 1, limit: 10, total: 0, totalPages: 0 });
  }, []);

  // 🔧 Utility functions
  const getCachedData = (key: string, cacheOptions: CacheOptions) => {
    if (!cacheOptions.enabled) return null;
    
    const cached = cacheRef.current.get(key);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > cacheOptions.ttl;
    if (isExpired) {
      cacheRef.current.delete(key);
      return null;
    }
    
    return cached.data;
  };

  const setCachedData = (key: string, data: any, cacheOptions: CacheOptions) => {
    if (!cacheOptions.enabled) return;
    
    cacheRef.current.set(key, {
      data,
      timestamp: Date.now()
    });
  };

  const invalidateCache = (pattern: string) => {
    const keys = Array.from(cacheRef.current.keys());
    keys.forEach(key => {
      if (key.includes(pattern)) {
        cacheRef.current.delete(key);
      }
    });
  };

  const generateCacheKey = (model: string, options: QueryOptions) => {
    return `${model}:${JSON.stringify(options)}`;
  };

  const retryOperation = async (operation: () => Promise<any>, retryOptions: any) => {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= retryOptions.attempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retryOptions.attempts) {
          await new Promise(resolve => 
            setTimeout(resolve, retryOptions.delay * attempt)
          );
        }
      }
    }
    
    throw lastError!;
  };

  return {
    // Data state
    data,
    loading,
    error,
    pagination,
    
    // Operations
    fetch: fetchData,
    fetchPaginated,
    create,
    update,
    remove,
    findOne,
    findById,
    count,
    refresh,
    clear,
    
    // Utilities
    model: modelRef.current,
    isConnected: ormRef.current?.isConnected || false
  };
}

/**
 * 🔗 useRelation Hook - For loading related data
 */
export function useRelation<T = any, R = any>(
  parentData: T[],
  relationName: string,
  options: {
    autoLoad?: boolean;
    cache?: CacheOptions;
  } = {}
) {
  const [relationData, setRelationData] = useState<Record<string, R | R[]>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadRelations = useCallback(async () => {
    if (!parentData.length) return;

    setLoading(true);
    setError(null);

    try {
      // Implementation would depend on the specific ORM setup
      // This is a simplified version
      const relations: Record<string, R | R[]> = {};
      
      // Load relations for each parent record
      for (const parent of parentData) {
        const parentId = (parent as any).id;
        // Load relation data based on relationName and parent ID
        // relations[parentId] = await loadRelationData(parent, relationName);
      }
      
      setRelationData(relations);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [parentData, relationName]);

  useEffect(() => {
    if (options.autoLoad && parentData.length > 0) {
      loadRelations();
    }
  }, [options.autoLoad, parentData, loadRelations]);

  return {
    relationData,
    loading,
    error,
    loadRelations
  };
}

/**
 * 🔄 useTransaction Hook - For database transactions
 */
export function useTransaction() {
  const [isInTransaction, setIsInTransaction] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const ormRef = useRef<KilatORM | null>(null);

  useEffect(() => {
    if (!ormRef.current) {
      ormRef.current = getORMInstance();
    }
  }, []);

  const runTransaction = useCallback(async <T>(
    callback: (trx: any) => Promise<T>
  ): Promise<T> => {
    if (!ormRef.current) {
      throw new Error('ORM not initialized');
    }

    setIsInTransaction(true);
    setError(null);

    try {
      const result = await ormRef.current.transaction(callback);
      setIsInTransaction(false);
      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      setIsInTransaction(false);
      throw error;
    }
  }, []);

  return {
    runTransaction,
    isInTransaction,
    error
  };
}

// 🏭 Helper function to get ORM instance
function getORMInstance(): KilatORM {
  // This would typically come from a context or global state
  // For now, return a mock instance
  const config = {
    type: 'sqlite' as const,
    database: ':memory:',
    synchronize: true,
    logging: false
  };
  
  return new KilatORM(config);
}
