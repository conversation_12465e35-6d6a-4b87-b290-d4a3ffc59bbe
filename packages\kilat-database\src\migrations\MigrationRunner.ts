import { join, basename } from 'path';
import { existsSync, readdirSync, readFileSync } from 'fs';
import { createLogger } from 'kilat-utils';
import type { 
  Migration, 
  MigrationConfig, 
  MigrationStatus, 
  DatabaseAdapter,
  MigrationRecord 
} from '../types';

/**
 * 🏃‍♂️ MigrationRunner - Database migration management
 * Handles schema changes, data migrations, and rollbacks
 */
export class MigrationRunner {
  private config: MigrationConfig;
  private adapter: DatabaseAdapter;
  private logger = createLogger({ prefix: 'MigrationRunner' });
  private migrationsTable = 'kilat_migrations';

  constructor(adapter: DatabaseAdapter, config: MigrationConfig) {
    this.adapter = adapter;
    this.config = {
      directory: 'migrations',
      tableName: 'kilat_migrations',
      lockTimeout: 300000, // 5 minutes
      ...config
    };
  }

  // 🚀 Run pending migrations
  async run(): Promise<MigrationStatus[]> {
    this.logger.info('Starting migration run...');

    try {
      // Ensure migrations table exists
      await this.ensureMigrationsTable();

      // Get all migration files
      const migrationFiles = await this.getMigrationFiles();
      
      // Get executed migrations
      const executedMigrations = await this.getExecutedMigrations();
      
      // Find pending migrations
      const pendingMigrations = migrationFiles.filter(file => 
        !executedMigrations.some(executed => executed.name === file.name)
      );

      if (pendingMigrations.length === 0) {
        this.logger.info('No pending migrations found');
        return [];
      }

      this.logger.info(`Found ${pendingMigrations.length} pending migrations`);

      // Acquire migration lock
      await this.acquireLock();

      const results: MigrationStatus[] = [];

      try {
        // Run each pending migration
        for (const migration of pendingMigrations) {
          const result = await this.runMigration(migration);
          results.push(result);
        }
      } finally {
        // Release migration lock
        await this.releaseLock();
      }

      this.logger.success(`Completed ${results.length} migrations`);
      return results;

    } catch (error) {
      this.logger.error('Migration run failed', error);
      throw error;
    }
  }

  // ⏪ Rollback migrations
  async rollback(steps: number = 1): Promise<MigrationStatus[]> {
    this.logger.info(`Rolling back ${steps} migration(s)...`);

    try {
      // Get executed migrations in reverse order
      const executedMigrations = await this.getExecutedMigrations();
      const migrationsToRollback = executedMigrations
        .sort((a, b) => new Date(b.executed_at).getTime() - new Date(a.executed_at).getTime())
        .slice(0, steps);

      if (migrationsToRollback.length === 0) {
        this.logger.info('No migrations to rollback');
        return [];
      }

      // Acquire migration lock
      await this.acquireLock();

      const results: MigrationStatus[] = [];

      try {
        // Rollback each migration
        for (const migrationRecord of migrationsToRollback) {
          const migrationFile = await this.getMigrationFile(migrationRecord.name);
          if (migrationFile) {
            const result = await this.rollbackMigration(migrationFile, migrationRecord);
            results.push(result);
          }
        }
      } finally {
        // Release migration lock
        await this.releaseLock();
      }

      this.logger.success(`Rolled back ${results.length} migrations`);
      return results;

    } catch (error) {
      this.logger.error('Migration rollback failed', error);
      throw error;
    }
  }

  // 📊 Get migration status
  async status(): Promise<{
    executed: MigrationRecord[];
    pending: Migration[];
    total: number;
  }> {
    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    
    const pendingMigrations = migrationFiles.filter(file => 
      !executedMigrations.some(executed => executed.name === file.name)
    );

    return {
      executed: executedMigrations,
      pending: pendingMigrations,
      total: migrationFiles.length
    };
  }

  // 🔄 Reset all migrations
  async reset(): Promise<void> {
    this.logger.warn('Resetting all migrations...');

    try {
      // Get all executed migrations
      const executedMigrations = await this.getExecutedMigrations();
      
      // Rollback all migrations
      if (executedMigrations.length > 0) {
        await this.rollback(executedMigrations.length);
      }

      this.logger.success('All migrations reset');
    } catch (error) {
      this.logger.error('Migration reset failed', error);
      throw error;
    }
  }

  // 🏃‍♂️ Run single migration
  private async runMigration(migration: Migration): Promise<MigrationStatus> {
    const startTime = Date.now();
    
    this.logger.info(`Running migration: ${migration.name}`);

    try {
      // Start transaction
      const transaction = await this.adapter.beginTransaction();

      try {
        // Execute migration up function
        await migration.up(this.adapter, transaction);

        // Record migration execution
        await this.recordMigration(migration, transaction);

        // Commit transaction
        await transaction.commit();

        const duration = Date.now() - startTime;
        this.logger.success(`Migration completed: ${migration.name} (${duration}ms)`);

        return {
          name: migration.name,
          status: 'success',
          duration,
          executedAt: new Date()
        };

      } catch (error) {
        // Rollback transaction
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Migration failed: ${migration.name}`, error);

      return {
        name: migration.name,
        status: 'failed',
        duration,
        error: error.message,
        executedAt: new Date()
      };
    }
  }

  // ⏪ Rollback single migration
  private async rollbackMigration(
    migration: Migration, 
    record: MigrationRecord
  ): Promise<MigrationStatus> {
    const startTime = Date.now();
    
    this.logger.info(`Rolling back migration: ${migration.name}`);

    try {
      // Start transaction
      const transaction = await this.adapter.beginTransaction();

      try {
        // Execute migration down function
        if (migration.down) {
          await migration.down(this.adapter, transaction);
        } else {
          throw new Error(`Migration ${migration.name} does not have a down function`);
        }

        // Remove migration record
        await this.removeMigrationRecord(migration.name, transaction);

        // Commit transaction
        await transaction.commit();

        const duration = Date.now() - startTime;
        this.logger.success(`Migration rolled back: ${migration.name} (${duration}ms)`);

        return {
          name: migration.name,
          status: 'rolled_back',
          duration,
          executedAt: new Date()
        };

      } catch (error) {
        // Rollback transaction
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Migration rollback failed: ${migration.name}`, error);

      return {
        name: migration.name,
        status: 'rollback_failed',
        duration,
        error: error.message,
        executedAt: new Date()
      };
    }
  }

  // 📁 Get migration files
  private async getMigrationFiles(): Promise<Migration[]> {
    const migrationsDir = this.config.directory;
    
    if (!existsSync(migrationsDir)) {
      this.logger.warn(`Migrations directory not found: ${migrationsDir}`);
      return [];
    }

    const files = readdirSync(migrationsDir)
      .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
      .sort(); // Sort by filename (timestamp)

    const migrations: Migration[] = [];

    for (const file of files) {
      try {
        const filePath = join(migrationsDir, file);
        const migration = await this.loadMigrationFile(filePath);
        migrations.push(migration);
      } catch (error) {
        this.logger.error(`Failed to load migration file: ${file}`, error);
      }
    }

    return migrations;
  }

  // 📄 Load migration file
  private async loadMigrationFile(filePath: string): Promise<Migration> {
    const fileName = basename(filePath, '.ts').replace('.js', '');
    
    try {
      // Dynamic import for the migration
      const migrationModule = await import(filePath);
      const migration = migrationModule.default || migrationModule;

      if (!migration.up) {
        throw new Error(`Migration ${fileName} must export an 'up' function`);
      }

      return {
        name: fileName,
        up: migration.up,
        down: migration.down,
        description: migration.description || '',
        timestamp: this.extractTimestamp(fileName)
      };

    } catch (error) {
      throw new Error(`Failed to load migration ${fileName}: ${error.message}`);
    }
  }

  // 🔍 Get single migration file
  private async getMigrationFile(name: string): Promise<Migration | null> {
    const migrations = await this.getMigrationFiles();
    return migrations.find(m => m.name === name) || null;
  }

  // 📊 Get executed migrations
  private async getExecutedMigrations(): Promise<MigrationRecord[]> {
    try {
      const query = `SELECT * FROM ${this.migrationsTable} ORDER BY executed_at ASC`;
      return await this.adapter.query(query);
    } catch (error) {
      // Table might not exist yet
      return [];
    }
  }

  // 📝 Record migration execution
  private async recordMigration(migration: Migration, transaction?: any): Promise<void> {
    const query = `
      INSERT INTO ${this.migrationsTable} (name, executed_at, checksum)
      VALUES (?, ?, ?)
    `;
    
    const checksum = this.calculateChecksum(migration);
    const params = [migration.name, new Date().toISOString(), checksum];

    if (transaction) {
      await transaction.query(query, params);
    } else {
      await this.adapter.query(query, params);
    }
  }

  // 🗑️ Remove migration record
  private async removeMigrationRecord(name: string, transaction?: any): Promise<void> {
    const query = `DELETE FROM ${this.migrationsTable} WHERE name = ?`;
    const params = [name];

    if (transaction) {
      await transaction.query(query, params);
    } else {
      await this.adapter.query(query, params);
    }
  }

  // 🏗️ Ensure migrations table exists
  private async ensureMigrationsTable(): Promise<void> {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS ${this.migrationsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at DATETIME NOT NULL,
        checksum VARCHAR(64),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await this.adapter.query(createTableQuery);
  }

  // 🔒 Acquire migration lock
  private async acquireLock(): Promise<void> {
    const lockQuery = `
      INSERT INTO ${this.migrationsTable}_lock (locked_at, locked_by)
      VALUES (?, ?)
    `;

    try {
      await this.adapter.query(lockQuery, [
        new Date().toISOString(),
        process.pid.toString()
      ]);
    } catch (error) {
      // Lock table might not exist, create it
      await this.createLockTable();
      await this.adapter.query(lockQuery, [
        new Date().toISOString(),
        process.pid.toString()
      ]);
    }
  }

  // 🔓 Release migration lock
  private async releaseLock(): Promise<void> {
    const unlockQuery = `DELETE FROM ${this.migrationsTable}_lock WHERE locked_by = ?`;
    await this.adapter.query(unlockQuery, [process.pid.toString()]);
  }

  // 🏗️ Create lock table
  private async createLockTable(): Promise<void> {
    const createLockTableQuery = `
      CREATE TABLE IF NOT EXISTS ${this.migrationsTable}_lock (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        locked_at DATETIME NOT NULL,
        locked_by VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await this.adapter.query(createLockTableQuery);
  }

  // 🔢 Extract timestamp from migration name
  private extractTimestamp(fileName: string): number {
    const match = fileName.match(/^(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }

  // 🔐 Calculate migration checksum
  private calculateChecksum(migration: Migration): string {
    const crypto = require('crypto');
    const content = migration.up.toString() + (migration.down?.toString() || '');
    return crypto.createHash('md5').update(content).digest('hex');
  }
}

/**
 * 🏭 Migration Generator - Creates new migration files
 */
export class MigrationGenerator {
  private config: MigrationConfig;
  private logger = createLogger({ prefix: 'MigrationGenerator' });

  constructor(config: MigrationConfig) {
    this.config = config;
  }

  // 📝 Generate new migration file
  async generate(name: string, type: 'table' | 'column' | 'data' | 'custom' = 'custom'): Promise<string> {
    const timestamp = Date.now();
    const fileName = `${timestamp}_${name.toLowerCase().replace(/\s+/g, '_')}.ts`;
    const filePath = join(this.config.directory, fileName);

    const template = this.getTemplate(type, name);

    try {
      const fs = await import('fs/promises');
      await fs.mkdir(this.config.directory, { recursive: true });
      await fs.writeFile(filePath, template);

      this.logger.success(`Migration created: ${fileName}`);
      return filePath;
    } catch (error) {
      this.logger.error('Failed to create migration file', error);
      throw error;
    }
  }

  // 📋 Get migration template
  private getTemplate(type: string, name: string): string {
    const className = name.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');

    switch (type) {
      case 'table':
        return this.getTableTemplate(className, name);
      case 'column':
        return this.getColumnTemplate(className, name);
      case 'data':
        return this.getDataTemplate(className, name);
      default:
        return this.getCustomTemplate(className, name);
    }
  }

  // 📋 Table migration template
  private getTableTemplate(className: string, name: string): string {
    return `import type { DatabaseAdapter, Transaction } from 'kilat-database';

/**
 * ${className} Migration
 * ${new Date().toISOString()}
 */
export default {
  async up(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    const query = \`
      CREATE TABLE ${name} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    \`;
    
    await db.query(query);
  },

  async down(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    await db.query(\`DROP TABLE IF EXISTS ${name}\`);
  },

  description: 'Create ${name} table'
};
`;
  }

  // 📋 Column migration template
  private getColumnTemplate(className: string, name: string): string {
    return `import type { DatabaseAdapter, Transaction } from 'kilat-database';

/**
 * ${className} Migration
 * ${new Date().toISOString()}
 */
export default {
  async up(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Add your column modifications here
    const query = \`
      ALTER TABLE table_name 
      ADD COLUMN column_name VARCHAR(255)
    \`;
    
    await db.query(query);
  },

  async down(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Reverse the column modifications
    const query = \`
      ALTER TABLE table_name 
      DROP COLUMN column_name
    \`;
    
    await db.query(query);
  },

  description: '${name} - Add/modify columns'
};
`;
  }

  // 📋 Data migration template
  private getDataTemplate(className: string, name: string): string {
    return `import type { DatabaseAdapter, Transaction } from 'kilat-database';

/**
 * ${className} Migration
 * ${new Date().toISOString()}
 */
export default {
  async up(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Add your data migration logic here
    const query = \`
      INSERT INTO table_name (column1, column2)
      VALUES (?, ?)
    \`;
    
    await db.query(query, ['value1', 'value2']);
  },

  async down(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Reverse the data changes
    const query = \`
      DELETE FROM table_name 
      WHERE condition = ?
    \`;
    
    await db.query(query, ['condition_value']);
  },

  description: '${name} - Data migration'
};
`;
  }

  // 📋 Custom migration template
  private getCustomTemplate(className: string, name: string): string {
    return `import type { DatabaseAdapter, Transaction } from 'kilat-database';

/**
 * ${className} Migration
 * ${new Date().toISOString()}
 */
export default {
  async up(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Add your migration logic here
    
  },

  async down(db: DatabaseAdapter, trx?: Transaction): Promise<void> {
    // Add rollback logic here
    
  },

  description: '${name}'
};
`;
  }
}
