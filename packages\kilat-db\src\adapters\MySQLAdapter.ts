import mysql from 'mysql2/promise';
import { createLogger } from 'kilat-utils';
import type { 
  DatabaseConnection, 
  MySQLConfig, 
  QueryResult, 
  Transaction,
  DatabaseStats 
} from '../types';

/**
 * 🐬 MySQL Database Adapter for KilatDB
 * High-performance MySQL connection with connection pooling
 */
export class MyS<PERSON>Adapter implements DatabaseConnection {
  public isConnected = false;
  
  private config: MySQLConfig;
  private pool: mysql.Pool | null = null;
  private logger = createLogger({ prefix: 'MySQLAdapter' });
  private stats = {
    queries: { total: 0, successful: 0, failed: 0, totalDuration: 0 },
    connections: { active: 0, total: 0 }
  };

  constructor(config: MySQLConfig) {
    this.config = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'kilat',
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true,
      charset: 'utf8mb4',
      timezone: '+00:00',
      ssl: false,
      ...config
    };
  }

  // 🔌 Connect to MySQL
  async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        return;
      }

      // Create connection pool
      this.pool = mysql.createPool({
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        connectionLimit: this.config.connectionLimit,
        acquireTimeout: this.config.acquireTimeout,
        timeout: this.config.timeout,
        reconnect: this.config.reconnect,
        charset: this.config.charset,
        timezone: this.config.timezone,
        ssl: this.config.ssl,
        multipleStatements: true,
        namedPlaceholders: true
      });

      // Test connection
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      this.isConnected = true;
      this.logger.info('MySQL connection established', {
        host: this.config.host,
        database: this.config.database
      });

    } catch (error) {
      this.logger.error('Failed to connect to MySQL:', error);
      throw error;
    }
  }

  // 🔌 Disconnect from MySQL
  async close(): Promise<void> {
    try {
      if (this.pool) {
        await this.pool.end();
        this.pool = null;
      }
      this.isConnected = false;
      this.logger.info('MySQL connection closed');
    } catch (error) {
      this.logger.error('Error closing MySQL connection:', error);
      throw error;
    }
  }

  // 🏓 Ping database
  async ping(): Promise<boolean> {
    try {
      if (!this.pool) {
        return false;
      }

      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      this.logger.warn('MySQL ping failed:', error);
      return false;
    }
  }

  // 🔍 Execute query
  async query(sql: string, params: any[] = []): Promise<QueryResult> {
    const startTime = Date.now();
    this.stats.queries.total++;

    try {
      if (!this.pool) {
        throw new Error('MySQL connection not established');
      }

      // Execute query
      const [rows, fields] = await this.pool.execute(sql, params);
      
      const duration = Date.now() - startTime;
      this.stats.queries.successful++;
      this.stats.queries.totalDuration += duration;

      this.logger.debug('Query executed', { sql, params, duration });

      // Handle different result types
      if (Array.isArray(rows)) {
        return {
          rows: rows as any[],
          rowCount: rows.length,
          fields: fields as any[],
          insertId: (rows as any).insertId,
          affectedRows: (rows as any).affectedRows,
          changedRows: (rows as any).changedRows
        };
      } else {
        // For INSERT, UPDATE, DELETE operations
        const result = rows as any;
        return {
          rows: [],
          rowCount: result.affectedRows || 0,
          fields: [],
          insertId: result.insertId,
          affectedRows: result.affectedRows,
          changedRows: result.changedRows
        };
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.stats.queries.failed++;
      this.stats.queries.totalDuration += duration;

      this.logger.error('Query failed', { sql, params, error, duration });
      throw error;
    }
  }

  // 🔄 Begin transaction
  async beginTransaction(): Promise<Transaction> {
    if (!this.pool) {
      throw new Error('MySQL connection not established');
    }

    const connection = await this.pool.getConnection();
    await connection.beginTransaction();

    return {
      query: async (sql: string, params: any[] = []) => {
        const startTime = Date.now();
        try {
          const [rows, fields] = await connection.execute(sql, params);
          const duration = Date.now() - startTime;
          
          this.logger.debug('Transaction query executed', { sql, params, duration });

          if (Array.isArray(rows)) {
            return {
              rows: rows as any[],
              rowCount: rows.length,
              fields: fields as any[],
              insertId: (rows as any).insertId,
              affectedRows: (rows as any).affectedRows,
              changedRows: (rows as any).changedRows
            };
          } else {
            const result = rows as any;
            return {
              rows: [],
              rowCount: result.affectedRows || 0,
              fields: [],
              insertId: result.insertId,
              affectedRows: result.affectedRows,
              changedRows: result.changedRows
            };
          }
        } catch (error) {
          this.logger.error('Transaction query failed', { sql, params, error });
          throw error;
        }
      },

      commit: async () => {
        try {
          await connection.commit();
          connection.release();
          this.logger.debug('Transaction committed');
        } catch (error) {
          this.logger.error('Transaction commit failed:', error);
          throw error;
        }
      },

      rollback: async () => {
        try {
          await connection.rollback();
          connection.release();
          this.logger.debug('Transaction rolled back');
        } catch (error) {
          this.logger.error('Transaction rollback failed:', error);
          throw error;
        }
      }
    };
  }

  // 📊 Get database statistics
  async getStats(): Promise<DatabaseStats> {
    try {
      const [statusRows] = await this.pool!.execute('SHOW STATUS');
      const [variableRows] = await this.pool!.execute('SHOW VARIABLES');
      
      const status = Object.fromEntries(
        (statusRows as any[]).map(row => [row.Variable_name, row.Value])
      );
      
      const variables = Object.fromEntries(
        (variableRows as any[]).map(row => [row.Variable_name, row.Value])
      );

      return {
        driver: 'mysql',
        version: variables.version,
        uptime: parseInt(status.Uptime) * 1000, // Convert to milliseconds
        connections: {
          current: parseInt(status.Threads_connected),
          total: parseInt(status.Connections),
          max: parseInt(variables.max_connections)
        },
        queries: {
          total: this.stats.queries.total,
          successful: this.stats.queries.successful,
          failed: this.stats.queries.failed,
          averageDuration: this.stats.queries.total > 0 
            ? this.stats.queries.totalDuration / this.stats.queries.total 
            : 0
        },
        memory: {
          used: parseInt(status.Innodb_buffer_pool_bytes_data),
          total: parseInt(variables.innodb_buffer_pool_size)
        },
        storage: {
          dataSize: parseInt(status.Innodb_data_written),
          indexSize: parseInt(status.Innodb_data_read)
        }
      };
    } catch (error) {
      this.logger.error('Failed to get MySQL stats:', error);
      throw error;
    }
  }

  // 🗃️ Get table information
  async getTableInfo(tableName: string): Promise<any> {
    try {
      const [columns] = await this.pool!.execute(
        'SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?',
        [this.config.database, tableName]
      );

      const [indexes] = await this.pool!.execute(
        'SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?',
        [this.config.database, tableName]
      );

      return {
        columns: columns as any[],
        indexes: indexes as any[]
      };
    } catch (error) {
      this.logger.error(`Failed to get table info for ${tableName}:`, error);
      throw error;
    }
  }

  // 📋 List all tables
  async listTables(): Promise<string[]> {
    try {
      const [rows] = await this.pool!.execute(
        'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?',
        [this.config.database]
      );

      return (rows as any[]).map(row => row.TABLE_NAME);
    } catch (error) {
      this.logger.error('Failed to list tables:', error);
      throw error;
    }
  }

  // 🔧 Execute raw SQL
  async raw(sql: string): Promise<any> {
    try {
      if (!this.pool) {
        throw new Error('MySQL connection not established');
      }

      const [rows] = await this.pool.execute(sql);
      return rows;
    } catch (error) {
      this.logger.error('Raw SQL execution failed:', error);
      throw error;
    }
  }

  // 🧹 Clean up resources
  async cleanup(): Promise<void> {
    try {
      if (this.pool) {
        // Close idle connections
        await this.pool.execute('KILL CONNECTION_ID()');
      }
    } catch (error) {
      this.logger.warn('Cleanup warning:', error);
    }
  }

  // 🔄 Reconnect to database
  async reconnect(): Promise<void> {
    this.logger.info('Reconnecting to MySQL...');
    await this.close();
    await this.connect();
  }

  // 🏥 Health check
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const isConnected = await this.ping();
      if (!isConnected) {
        return {
          status: 'unhealthy',
          details: { error: 'Connection failed' }
        };
      }

      const stats = await this.getStats();
      return {
        status: 'healthy',
        details: {
          driver: 'mysql',
          version: stats.version,
          uptime: stats.uptime,
          connections: stats.connections,
          queries: stats.queries
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: (error as Error).message }
      };
    }
  }
}
