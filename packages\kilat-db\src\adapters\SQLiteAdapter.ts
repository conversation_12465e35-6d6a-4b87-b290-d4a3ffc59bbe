import Database from 'better-sqlite3';
import type { DatabaseConnection, Transaction } from '../types';

/**
 * 🗃️ SQLite Database Adapter
 * Offline-first database adapter using better-sqlite3
 */
export class SQLiteAdapter implements DatabaseConnection {
  public driver = 'sqlite' as const;
  public isConnected = false;
  
  private db: Database.Database | null = null;
  private config: {
    file: string;
    enableWAL?: boolean;
    timeout?: number;
    memory?: boolean;
    readonly?: boolean;
  };

  constructor(config: {
    file: string;
    enableWAL?: boolean;
    timeout?: number;
    memory?: boolean;
    readonly?: boolean;
  }) {
    this.config = config;
    this.connect();
  }

  private connect(): void {
    try {
      const options: Database.Options = {
        timeout: this.config.timeout || 5000,
        readonly: this.config.readonly || false,
        fileMustExist: false
      };

      // Handle in-memory database
      const dbPath = this.config.memory ? ':memory:' : this.config.file;
      
      this.db = new Database(dbPath, options);
      
      // Enable WAL mode for better concurrency
      if (this.config.enableWAL && !this.config.memory) {
        this.db.pragma('journal_mode = WAL');
      }
      
      // Set other pragmas for performance
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000');
      this.db.pragma('temp_store = memory');
      
      this.isConnected = true;
    } catch (error) {
      throw new Error(`Failed to connect to SQLite database: ${error}`);
    }
  }

  async query(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    try {
      // Determine if this is a SELECT query
      const isSelect = sql.trim().toLowerCase().startsWith('select');
      
      if (isSelect) {
        const stmt = this.db.prepare(sql);
        const result = stmt.all(params);
        return Array.isArray(result) ? result : [result];
      } else {
        // For non-SELECT queries, use execute method
        const result = await this.execute(sql, params);
        return [{ affectedRows: result.affectedRows, insertId: result.insertId }];
      }
    } catch (error) {
      throw new Error(`SQLite query failed: ${error}`);
    }
  }

  async execute(sql: string, params: any[] = []): Promise<{ affectedRows: number; insertId?: number }> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(params);
      
      return {
        affectedRows: result.changes,
        insertId: result.lastInsertRowid as number
      };
    } catch (error) {
      throw new Error(`SQLite execute failed: ${error}`);
    }
  }

  async transaction<T>(callback: (trx: Transaction) => Promise<T>): Promise<T> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const transaction = this.db.transaction((callback: any) => {
      return callback();
    });

    const trx: Transaction = {
      query: async (sql: string, params: any[] = []) => {
        return this.query(sql, params);
      },
      
      execute: async (sql: string, params: any[] = []) => {
        return this.execute(sql, params);
      },
      
      commit: async () => {
        // SQLite transactions auto-commit when function completes successfully
      },
      
      rollback: async () => {
        throw new Error('Transaction rollback');
      }
    };

    try {
      return transaction(() => callback(trx));
    } catch (error) {
      throw new Error(`Transaction failed: ${error}`);
    }
  }

  async ping(): Promise<boolean> {
    if (!this.db) return false;
    
    try {
      this.db.prepare('SELECT 1').get();
      return true;
    } catch {
      return false;
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isConnected = false;
    }
  }

  // 🔧 SQLite-specific methods
  
  /**
   * Get database file size in bytes
   */
  getFileSize(): number {
    if (!this.db || this.config.memory) return 0;
    
    try {
      const result = this.db.prepare('PRAGMA page_count').get() as { page_count: number };
      const pageSize = this.db.prepare('PRAGMA page_size').get() as { page_size: number };
      return result.page_count * pageSize.page_size;
    } catch {
      return 0;
    }
  }

  /**
   * Vacuum database to reclaim space
   */
  vacuum(): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    this.db.exec('VACUUM');
  }

  /**
   * Analyze database for query optimization
   */
  analyze(): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    this.db.exec('ANALYZE');
  }

  /**
   * Get database integrity check
   */
  integrityCheck(): any[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const stmt = this.db.prepare('PRAGMA integrity_check');
    return stmt.all();
  }

  /**
   * Backup database to another file
   */
  backup(destinationPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      try {
        const backup = this.db.backup(destinationPath);
        
        const step = () => {
          try {
            const result = backup.step(100); // Copy 100 pages at a time
            if (result.remainingPages === 0) {
              backup.close();
              resolve();
            } else {
              // Continue backup asynchronously
              setImmediate(step);
            }
          } catch (error) {
            backup.close();
            reject(error);
          }
        };
        
        step();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get table information
   */
  getTableInfo(tableName: string): any[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const stmt = this.db.prepare(`PRAGMA table_info(${tableName})`);
    return stmt.all();
  }

  /**
   * Get foreign key information
   */
  getForeignKeys(tableName: string): any[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const stmt = this.db.prepare(`PRAGMA foreign_key_list(${tableName})`);
    return stmt.all();
  }

  /**
   * Get index information
   */
  getIndexes(tableName: string): any[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const stmt = this.db.prepare(`PRAGMA index_list(${tableName})`);
    return stmt.all();
  }

  /**
   * Enable/disable foreign key constraints
   */
  setForeignKeysEnabled(enabled: boolean): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    this.db.pragma(`foreign_keys = ${enabled ? 'ON' : 'OFF'}`);
  }

  /**
   * Get database statistics
   */
  getStats(): any {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const pageCount = this.db.prepare('PRAGMA page_count').get();
    const pageSize = this.db.prepare('PRAGMA page_size').get();
    const freelistCount = this.db.prepare('PRAGMA freelist_count').get();
    
    return {
      pageCount,
      pageSize,
      freelistCount,
      fileSize: this.getFileSize(),
      walMode: this.db.prepare('PRAGMA journal_mode').get()
    };
  }
}
