import { createLogger } from 'kilat-utils';
import type { 
  DatabaseConfig, 
  DatabaseConnection, 
  DatabaseManager, 
  ModelClass, 
  ModelDefinition, 
  ModelOptions,
  MigrationRecord,
  Transaction,
  DatabaseStats
} from '../types';
import { SQLiteAdapter } from '../adapters/SQLiteAdapter';
import { MySQLAdapter } from '../adapters/MySQLAdapter';
import { Model } from './Model';
import { MigrationManager } from '../migrations/MigrationManager';

/**
 * 🗃️ KilatDB - Main Database Manager
 * Universal ORM for SQLite and MySQL with offline capabilities
 */
export class KilatDB implements DatabaseManager {
  public config: DatabaseConfig;
  public connection: DatabaseConnection | null = null;
  public models: Map<string, ModelClass> = new Map();
  
  private logger = createLogger({ level: 'info' });
  private migrationManager: MigrationManager;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private stats = {
    queries: { total: 0, successful: 0, failed: 0, totalDuration: 0 },
    cache: { hits: 0, misses: 0 }
  };

  constructor(config: DatabaseConfig) {
    this.config = {
      logging: { enabled: false, level: 'info', queries: false },
      cache: { enabled: true, ttl: 300000, maxSize: 100 },
      migrations: { directory: './migrations', autoRun: false, tableName: 'migrations' },
      ...config
    };
    
    this.migrationManager = new MigrationManager(this);
    
    if (this.config.logging?.enabled) {
      this.logger.info('KilatDB initialized', { driver: this.config.driver });
    }
  }

  // 🔌 Connection Management
  async connect(): Promise<void> {
    try {
      if (this.connection?.isConnected) {
        this.logger.warn('Database already connected');
        return;
      }

      // Create appropriate adapter
      switch (this.config.driver) {
        case 'sqlite':
          this.connection = new SQLiteAdapter(this.config.connection.sqlite!);
          break;
        case 'mysql':
          this.connection = new MySQLAdapter(this.config.connection.mysql!);
          break;
        default:
          throw new Error(`Unsupported database driver: ${this.config.driver}`);
      }

      // Test connection
      const isConnected = await this.connection.ping();
      if (!isConnected) {
        throw new Error('Failed to establish database connection');
      }

      this.logger.info('Database connected successfully', { 
        driver: this.config.driver 
      });

      // Run migrations if auto-run is enabled
      if (this.config.migrations?.autoRun) {
        await this.migrate();
      }

    } catch (error) {
      this.logger.error('Failed to connect to database', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
        this.logger.info('Database disconnected');
      }
    } catch (error) {
      this.logger.error('Error disconnecting from database', error as Error);
      throw error;
    }
  }

  isConnected(): boolean {
    return this.connection?.isConnected || false;
  }

  // 📊 Model Management
  defineModel(name: string, definition: ModelDefinition, options: ModelOptions = {}): ModelClass {
    if (this.models.has(name)) {
      throw new Error(`Model '${name}' is already defined`);
    }

    const model = new Model(name, definition, options, this);
    this.models.set(name, model as any);
    
    this.logger.debug('Model defined', { name, fields: Object.keys(definition) });
    
    return model as any;
  }

  getModel(name: string): ModelClass {
    const model = this.models.get(name);
    if (!model) {
      throw new Error(`Model '${name}' is not defined`);
    }
    return model;
  }

  // 🔄 Migration Management
  async migrate(): Promise<void> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }
    
    await this.migrationManager.runPendingMigrations();
  }

  async rollback(steps = 1): Promise<void> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }
    
    await this.migrationManager.rollback(steps);
  }

  async getMigrationStatus(): Promise<MigrationRecord[]> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }
    
    return this.migrationManager.getExecutedMigrations();
  }

  // 🔍 Raw Query Methods
  async raw(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }

    const startTime = Date.now();
    
    try {
      this.stats.queries.total++;
      
      if (this.config.logging?.queries) {
        this.logger.debug('Executing query', { sql, params });
      }

      const result = await this.connection.query(sql, params);
      
      const duration = Date.now() - startTime;
      this.stats.queries.successful++;
      this.stats.queries.totalDuration += duration;
      
      if (this.config.logging?.queries) {
        this.logger.debug('Query completed', { duration, rows: result.length });
      }
      
      return result;
    } catch (error) {
      this.stats.queries.failed++;
      this.logger.error('Query failed', error as Error, { sql, params });
      throw error;
    }
  }

  async transaction<T>(callback: (trx: Transaction) => Promise<T>): Promise<T> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }

    return this.connection.transaction(callback);
  }

  // 💾 Cache Management
  clearCache(): void {
    this.cache.clear();
    this.stats.cache.hits = 0;
    this.stats.cache.misses = 0;
    this.logger.debug('Cache cleared');
  }

  getCacheStats(): { hits: number; misses: number; size: number } {
    return {
      hits: this.stats.cache.hits,
      misses: this.stats.cache.misses,
      size: this.cache.size
    };
  }

  private getCacheKey(sql: string, params: any[]): string {
    return `${sql}:${JSON.stringify(params)}`;
  }

  private getFromCache(key: string): any | null {
    if (!this.config.cache?.enabled) return null;
    
    const cached = this.cache.get(key);
    if (!cached) {
      this.stats.cache.misses++;
      return null;
    }
    
    const isExpired = Date.now() - cached.timestamp > this.config.cache.ttl;
    if (isExpired) {
      this.cache.delete(key);
      this.stats.cache.misses++;
      return null;
    }
    
    this.stats.cache.hits++;
    return cached.data;
  }

  private setCache(key: string, data: any): void {
    if (!this.config.cache?.enabled) return;
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.cache.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // 📊 Statistics
  getStats(): DatabaseStats {
    const avgDuration = this.stats.queries.total > 0 
      ? this.stats.queries.totalDuration / this.stats.queries.total 
      : 0;
    
    const cacheHitRate = (this.stats.cache.hits + this.stats.cache.misses) > 0
      ? this.stats.cache.hits / (this.stats.cache.hits + this.stats.cache.misses)
      : 0;

    return {
      connections: {
        active: this.isConnected() ? 1 : 0,
        idle: 0,
        total: 1
      },
      queries: {
        total: this.stats.queries.total,
        successful: this.stats.queries.successful,
        failed: this.stats.queries.failed,
        averageDuration: avgDuration
      },
      cache: {
        hits: this.stats.cache.hits,
        misses: this.stats.cache.misses,
        hitRate: cacheHitRate,
        size: this.cache.size
      },
      tables: {} // Would be populated by scanning database
    };
  }

  // 🔧 Utility Methods
  async ping(): Promise<boolean> {
    if (!this.connection) return false;
    return this.connection.ping();
  }

  async close(): Promise<void> {
    await this.disconnect();
  }

  // 🎯 Model Factory Methods
  createModel<T = any>(name: string, definition: ModelDefinition, options?: ModelOptions): ModelClass {
    return this.defineModel(name, definition, options);
  }

  // 🔍 Schema Inspection
  async getTableNames(): Promise<string[]> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }

    const sql = this.config.driver === 'sqlite' 
      ? "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      : "SHOW TABLES";
    
    const result = await this.raw(sql);
    
    if (this.config.driver === 'sqlite') {
      return result.map(row => row.name);
    } else {
      const tableKey = Object.keys(result[0] || {})[0];
      return result.map(row => row[tableKey]);
    }
  }

  async getTableSchema(tableName: string): Promise<any[]> {
    if (!this.connection) {
      throw new Error('Database not connected');
    }

    const sql = this.config.driver === 'sqlite'
      ? `PRAGMA table_info(${tableName})`
      : `DESCRIBE ${tableName}`;
    
    return this.raw(sql);
  }
}
