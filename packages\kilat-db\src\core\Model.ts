import { createLogger } from 'kilat-utils';
import type { 
  ModelDefinition, 
  ModelOptions, 
  QueryBuilder, 
  ModelInstance,
  ValidationRule,
  RelationDefinition,
  DatabaseManager
} from '../types';

/**
 * 🏗️ Base Model Class for KilatDB
 * Active Record pattern with validation and relationships
 */
export class Model {
  public static tableName: string;
  public static definition: ModelDefinition;
  public static options: ModelOptions;
  public static db: DatabaseManager;
  
  private static logger = createLogger({ prefix: 'Model' });
  private attributes: Record<string, any> = {};
  private originalAttributes: Record<string, any> = {};
  private isNewRecord = true;
  private isDirty = false;

  constructor(attributes: Record<string, any> = {}) {
    this.attributes = { ...attributes };
    this.originalAttributes = { ...attributes };
    this.isNewRecord = !attributes[this.getPrimaryKey()];
  }

  // 🔑 Get primary key field name
  getPrimaryKey(): string {
    return this.constructor.options?.primaryKey || 'id';
  }

  // 📝 Get attribute value
  get(key: string): any {
    return this.attributes[key];
  }

  // 📝 Set attribute value
  set(key: string, value: any): this {
    if (this.attributes[key] !== value) {
      this.attributes[key] = value;
      this.isDirty = true;
    }
    return this;
  }

  // 📦 Set multiple attributes
  setAttributes(attributes: Record<string, any>): this {
    for (const [key, value] of Object.entries(attributes)) {
      this.set(key, value);
    }
    return this;
  }

  // 📋 Get all attributes
  getAttributes(): Record<string, any> {
    return { ...this.attributes };
  }

  // 🔍 Check if attribute has changed
  hasChanged(key?: string): boolean {
    if (key) {
      return this.attributes[key] !== this.originalAttributes[key];
    }
    return this.isDirty;
  }

  // 🔄 Get changed attributes
  getChanges(): Record<string, { from: any; to: any }> {
    const changes: Record<string, { from: any; to: any }> = {};
    
    for (const key in this.attributes) {
      if (this.hasChanged(key)) {
        changes[key] = {
          from: this.originalAttributes[key],
          to: this.attributes[key]
        };
      }
    }
    
    return changes;
  }

  // ✅ Validate model
  async validate(): Promise<{ isValid: boolean; errors: Record<string, string[]> }> {
    const errors: Record<string, string[]> = {};
    const definition = (this.constructor as any).definition;

    for (const [field, fieldDef] of Object.entries(definition.fields)) {
      const value = this.get(field);
      const fieldErrors: string[] = [];

      // Required validation
      if (fieldDef.required && (value === null || value === undefined || value === '')) {
        fieldErrors.push(`${field} is required`);
      }

      // Type validation
      if (value !== null && value !== undefined) {
        if (!this.validateType(value, fieldDef.type)) {
          fieldErrors.push(`${field} must be of type ${fieldDef.type}`);
        }
      }

      // Custom validation rules
      if (fieldDef.validation) {
        for (const rule of fieldDef.validation) {
          const result = await this.validateRule(value, rule);
          if (!result.isValid) {
            fieldErrors.push(result.message);
          }
        }
      }

      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // 🔍 Validate field type
  private validateType(value: any, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'json':
        return typeof value === 'object';
      default:
        return true;
    }
  }

  // 🔍 Validate single rule
  private async validateRule(value: any, rule: ValidationRule): Promise<{ isValid: boolean; message: string }> {
    switch (rule.type) {
      case 'min':
        if (typeof value === 'string' || Array.isArray(value)) {
          return {
            isValid: value.length >= rule.value,
            message: rule.message || `Must be at least ${rule.value} characters`
          };
        }
        if (typeof value === 'number') {
          return {
            isValid: value >= rule.value,
            message: rule.message || `Must be at least ${rule.value}`
          };
        }
        break;

      case 'max':
        if (typeof value === 'string' || Array.isArray(value)) {
          return {
            isValid: value.length <= rule.value,
            message: rule.message || `Must be at most ${rule.value} characters`
          };
        }
        if (typeof value === 'number') {
          return {
            isValid: value <= rule.value,
            message: rule.message || `Must be at most ${rule.value}`
          };
        }
        break;

      case 'pattern':
        return {
          isValid: new RegExp(rule.value).test(value),
          message: rule.message || 'Invalid format'
        };

      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return {
          isValid: emailPattern.test(value),
          message: rule.message || 'Invalid email format'
        };

      case 'unique':
        const exists = await this.checkUnique(rule.field || 'id', value);
        return {
          isValid: !exists,
          message: rule.message || `${rule.field} must be unique`
        };

      case 'custom':
        if (rule.validator) {
          const result = await rule.validator(value, this);
          return {
            isValid: result,
            message: rule.message || 'Validation failed'
          };
        }
        break;
    }

    return { isValid: true, message: '' };
  }

  // 🔍 Check if value is unique
  private async checkUnique(field: string, value: any): Promise<boolean> {
    const ModelClass = this.constructor as any;
    const query = ModelClass.where(field, value);
    
    // Exclude current record if updating
    if (!this.isNewRecord) {
      const primaryKey = this.getPrimaryKey();
      query.where(primaryKey, '!=', this.get(primaryKey));
    }
    
    const existing = await query.first();
    return !!existing;
  }

  // 💾 Save model
  async save(): Promise<boolean> {
    try {
      // Validate before saving
      const validation = await this.validate();
      if (!validation.isValid) {
        Model.logger.error('Validation failed:', validation.errors);
        return false;
      }

      // Run before save hooks
      await this.beforeSave();

      const ModelClass = this.constructor as any;
      const db = ModelClass.db;

      if (this.isNewRecord) {
        // Insert new record
        await this.beforeCreate();
        
        const result = await db.query(
          this.buildInsertQuery(),
          this.getInsertValues()
        );

        if (result.insertId) {
          this.set(this.getPrimaryKey(), result.insertId);
        }

        this.isNewRecord = false;
        await this.afterCreate();
      } else {
        // Update existing record
        await this.beforeUpdate();
        
        await db.query(
          this.buildUpdateQuery(),
          this.getUpdateValues()
        );

        await this.afterUpdate();
      }

      // Update original attributes
      this.originalAttributes = { ...this.attributes };
      this.isDirty = false;

      await this.afterSave();
      return true;

    } catch (error) {
      Model.logger.error('Failed to save model:', error);
      return false;
    }
  }

  // 🗑️ Delete model
  async delete(): Promise<boolean> {
    try {
      if (this.isNewRecord) {
        return false;
      }

      await this.beforeDelete();

      const ModelClass = this.constructor as any;
      const db = ModelClass.db;
      const primaryKey = this.getPrimaryKey();

      await db.query(
        `DELETE FROM ${ModelClass.tableName} WHERE ${primaryKey} = ?`,
        [this.get(primaryKey)]
      );

      await this.afterDelete();
      return true;

    } catch (error) {
      Model.logger.error('Failed to delete model:', error);
      return false;
    }
  }

  // 🔄 Reload model from database
  async reload(): Promise<this> {
    if (this.isNewRecord) {
      return this;
    }

    const ModelClass = this.constructor as any;
    const primaryKey = this.getPrimaryKey();
    const fresh = await ModelClass.find(this.get(primaryKey));

    if (fresh) {
      this.attributes = fresh.getAttributes();
      this.originalAttributes = { ...this.attributes };
      this.isDirty = false;
    }

    return this;
  }

  // 🏗️ Build SQL queries
  private buildInsertQuery(): string {
    const ModelClass = this.constructor as any;
    const fields = Object.keys(this.attributes).filter(key => key !== this.getPrimaryKey());
    const placeholders = fields.map(() => '?').join(', ');
    
    return `INSERT INTO ${ModelClass.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
  }

  private buildUpdateQuery(): string {
    const ModelClass = this.constructor as any;
    const fields = Object.keys(this.attributes).filter(key => 
      key !== this.getPrimaryKey() && this.hasChanged(key)
    );
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const primaryKey = this.getPrimaryKey();
    
    return `UPDATE ${ModelClass.tableName} SET ${setClause} WHERE ${primaryKey} = ?`;
  }

  private getInsertValues(): any[] {
    return Object.keys(this.attributes)
      .filter(key => key !== this.getPrimaryKey())
      .map(key => this.attributes[key]);
  }

  private getUpdateValues(): any[] {
    const values = Object.keys(this.attributes)
      .filter(key => key !== this.getPrimaryKey() && this.hasChanged(key))
      .map(key => this.attributes[key]);
    
    values.push(this.get(this.getPrimaryKey()));
    return values;
  }

  // 🪝 Lifecycle hooks (override in subclasses)
  async beforeSave(): Promise<void> {}
  async afterSave(): Promise<void> {}
  async beforeCreate(): Promise<void> {}
  async afterCreate(): Promise<void> {}
  async beforeUpdate(): Promise<void> {}
  async afterUpdate(): Promise<void> {}
  async beforeDelete(): Promise<void> {}
  async afterDelete(): Promise<void> {}

  // 🔍 Static query methods
  static where(field: string, operator: any, value?: any): QueryBuilder {
    // Implementation would return a QueryBuilder instance
    throw new Error('QueryBuilder not implemented');
  }

  static async find(id: any): Promise<Model | null> {
    // Implementation would find by primary key
    throw new Error('find method not implemented');
  }

  static async findBy(field: string, value: any): Promise<Model | null> {
    // Implementation would find by field
    throw new Error('findBy method not implemented');
  }

  static async all(): Promise<Model[]> {
    // Implementation would return all records
    throw new Error('all method not implemented');
  }

  static async create(attributes: Record<string, any>): Promise<Model> {
    const instance = new this(attributes);
    await instance.save();
    return instance;
  }

  // 📄 Convert to JSON
  toJSON(): Record<string, any> {
    return this.getAttributes();
  }

  // 📄 Convert to string
  toString(): string {
    return JSON.stringify(this.toJSON(), null, 2);
  }
}
