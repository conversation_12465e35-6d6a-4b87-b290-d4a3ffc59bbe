// 🗃️ Kilat.js Database - Production Ready Universal ORM
// Complete database solution with migrations, relationships, and validation

// Export all types
export * from './types';

// Core ORM
export { KilatDB } from './core/KilatDB';
export { Model } from './core/Model';
export { QueryBuilder } from './core/QueryBuilder';
export { Schema } from './core/Schema';
export { Connection } from './core/Connection';
export { Transaction } from './core/Transaction';

// Database Adapters
export { SQLiteAdapter } from './adapters/SQLiteAdapter';
export { MySQLAdapter } from './adapters/MySQLAdapter';
export { PostgreSQLAdapter } from './adapters/PostgreSQLAdapter';
export { MongoDBAdapter } from './adapters/MongoDBAdapter';

// Migration System
export { MigrationManager } from './migrations/MigrationManager';
export { Migration } from './migrations/Migration';
export { MigrationRunner } from './migrations/MigrationRunner';
export { MigrationGenerator } from './migrations/MigrationGenerator';
export { Seeder } from './migrations/Seeder';

// Relationships
export { HasOne } from './relationships/HasOne';
export { HasMany } from './relationships/HasMany';
export { BelongsTo } from './relationships/BelongsTo';
export { BelongsToMany } from './relationships/BelongsToMany';
export { MorphTo } from './relationships/MorphTo';
export { MorphMany } from './relationships/MorphMany';

// Validation System
export { Validator } from './validation/Validator';
export { ValidationRule } from './validation/ValidationRule';
export { ValidationError } from './validation/ValidationError';
export { createValidationRules } from './validation/createValidationRules';

// Query Features
export { Paginator } from './query/Paginator';
export { Aggregator } from './query/Aggregator';
export { JoinBuilder } from './query/JoinBuilder';
export { SubQueryBuilder } from './query/SubQueryBuilder';
export { RawQuery } from './query/RawQuery';

// Database Utilities
export { DatabaseManager } from './utils/DatabaseManager';
export { ConnectionPool } from './utils/ConnectionPool';
export { QueryLogger } from './utils/QueryLogger';
export { CacheManager } from './utils/CacheManager';
export { IndexManager } from './utils/IndexManager';
export { BackupManager } from './utils/BackupManager';

// CLI Integration
export { createMigration } from './cli/createMigration';
export { runMigrations } from './cli/runMigrations';
export { rollbackMigrations } from './cli/rollbackMigrations';
export { seedDatabase } from './cli/seedDatabase';
export { generateModel } from './cli/generateModel';
export { databaseStatus } from './cli/databaseStatus';

// Export utilities
export { defineModel } from './utils/defineModel';
export { createConnection } from './utils/connection';
export { validateSchema } from './utils/validation';
export { generateMigration } from './utils/migration';

// Export hooks
export { useKilatDB } from './hooks/useKilatDB';
export { useModel } from './hooks/useModel';
export { useQuery } from './hooks/useQuery';

// Version info
export const KILAT_DB_VERSION = '1.0.0';

// 🏭 Main factory function
import { KilatDB } from './core/KilatDB';
import type { DatabaseConfig } from './types';

export function createKilatDB(config: DatabaseConfig): KilatDB {
  return new KilatDB(config);
}

// 🎯 Quick setup functions
export function createSQLiteDB(file: string, options?: Partial<DatabaseConfig>): KilatDB {
  return createKilatDB({
    driver: 'sqlite',
    connection: {
      sqlite: { file, enableWAL: true, timeout: 5000 }
    },
    migrations: {
      directory: './migrations',
      autoRun: true
    },
    logging: {
      enabled: process.env.NODE_ENV === 'development',
      level: 'info',
      queries: false
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100
    },
    ...options
  });
}

export function createMySQLDB(connection: {
  host: string;
  user: string;
  password: string;
  database: string;
  port?: number;
}, options?: Partial<DatabaseConfig>): KilatDB {
  return createKilatDB({
    driver: 'mysql',
    connection: {
      mysql: {
        host: connection.host,
        port: connection.port || 3306,
        user: connection.user,
        password: connection.password,
        database: connection.database,
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000
      }
    },
    migrations: {
      directory: './migrations',
      autoRun: true
    },
    logging: {
      enabled: process.env.NODE_ENV === 'development',
      level: 'info',
      queries: false
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100
    },
    ...options
  });
}

// 🎨 Model definition helpers
export function defineSchema<T extends Record<string, any>>(definition: T): T {
  return definition;
}

// 📊 Common field types
export const Fields = {
  id: () => ({ type: 'uuid' as const, required: true, unique: true }),
  string: (length = 255) => ({ type: 'string' as const, length }),
  text: () => ({ type: 'text' as const }),
  number: () => ({ type: 'number' as const }),
  boolean: () => ({ type: 'boolean' as const }),
  date: () => ({ type: 'date' as const }),
  json: () => ({ type: 'json' as const }),
  email: () => ({ type: 'email' as const }),
  url: () => ({ type: 'url' as const }),
  enum: (values: string[]) => ({ type: 'enum' as const, enum: values }),
  
  // Common patterns
  timestamps: () => ({
    createdAt: { type: 'date' as const, default: () => new Date() },
    updatedAt: { type: 'date' as const, default: () => new Date() }
  }),
  
  softDelete: () => ({
    deletedAt: { type: 'date' as const, default: null }
  }),
  
  // Foreign key helper
  foreignKey: (model: string, field = 'id') => ({
    type: 'uuid' as const,
    references: { model, field }
  })
};

// 🔍 Query helpers
export const Query = {
  eq: (value: any) => ({ $eq: value }),
  ne: (value: any) => ({ $ne: value }),
  gt: (value: any) => ({ $gt: value }),
  gte: (value: any) => ({ $gte: value }),
  lt: (value: any) => ({ $lt: value }),
  lte: (value: any) => ({ $lte: value }),
  in: (values: any[]) => ({ $in: values }),
  nin: (values: any[]) => ({ $nin: values }),
  like: (pattern: string) => ({ $like: pattern }),
  ilike: (pattern: string) => ({ $ilike: pattern }),
  between: (min: any, max: any) => ({ $between: [min, max] }),
  isNull: () => ({ $null: true }),
  isNotNull: () => ({ $null: false }),
  and: (...conditions: any[]) => ({ $and: conditions }),
  or: (...conditions: any[]) => ({ $or: conditions })
};

// 🎯 Migration helpers
export const Schema = {
  createTable: (name: string, callback: (table: any) => void) => {
    // Implementation would be in migration system
    return { type: 'createTable', name, callback };
  },
  
  dropTable: (name: string) => {
    return { type: 'dropTable', name };
  },
  
  addColumn: (table: string, name: string, definition: any) => {
    return { type: 'addColumn', table, name, definition };
  },
  
  dropColumn: (table: string, name: string) => {
    return { type: 'dropColumn', table, name };
  },
  
  addIndex: (table: string, fields: string[], options?: any) => {
    return { type: 'addIndex', table, fields, options };
  },
  
  dropIndex: (table: string, name: string) => {
    return { type: 'dropIndex', table, name };
  }
};

// Default export
export default {
  version: KILAT_DB_VERSION,
  createKilatDB,
  createSQLiteDB,
  createMySQLDB,
  defineSchema,
  Fields,
  Query,
  Schema
};
