// 🎯 Database Types

// 🔧 Database Configuration
export type DatabaseDriver = 'sqlite' | 'mysql';

export interface DatabaseConfig {
  driver: DatabaseDriver;
  connection: {
    sqlite?: {
      file: string;
      enableWAL?: boolean;
      timeout?: number;
      memory?: boolean;
      readonly?: boolean;
    };
    mysql?: {
      host: string;
      port?: number;
      user: string;
      password: string;
      database: string;
      ssl?: boolean;
      connectionLimit?: number;
      acquireTimeout?: number;
      timeout?: number;
    };
  };
  migrations?: {
    directory: string;
    autoRun: boolean;
    tableName?: string;
  };
  logging?: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    queries: boolean;
  };
  cache?: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

// 📊 Model Definition Types
export type FieldType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'date' 
  | 'json' 
  | 'text' 
  | 'uuid'
  | 'email'
  | 'url'
  | 'enum';

export interface FieldDefinition {
  type: FieldType;
  required?: boolean;
  unique?: boolean;
  default?: any;
  length?: number;
  precision?: number;
  scale?: number;
  enum?: string[];
  validate?: (value: any) => boolean | string;
  transform?: {
    input?: (value: any) => any;
    output?: (value: any) => any;
  };
  index?: boolean;
  comment?: string;
}

export interface ModelDefinition {
  [fieldName: string]: FieldType | FieldDefinition;
}

export interface ModelOptions {
  tableName?: string;
  timestamps?: boolean;
  softDelete?: boolean;
  indexes?: IndexDefinition[];
  hooks?: ModelHooks;
  validation?: ValidationRules;
}

export interface IndexDefinition {
  name?: string;
  fields: string[];
  unique?: boolean;
  type?: 'btree' | 'hash' | 'fulltext';
}

// 🔗 Relationship Types
export type RelationType = 'hasOne' | 'hasMany' | 'belongsTo' | 'belongsToMany';

export interface RelationDefinition {
  type: RelationType;
  model: string;
  foreignKey?: string;
  localKey?: string;
  through?: string;
  as?: string;
  onDelete?: 'cascade' | 'set null' | 'restrict';
  onUpdate?: 'cascade' | 'set null' | 'restrict';
}

// 🎣 Model Hooks
export interface ModelHooks {
  beforeCreate?: (data: any) => void | Promise<void>;
  afterCreate?: (data: any) => void | Promise<void>;
  beforeUpdate?: (data: any, where: any) => void | Promise<void>;
  afterUpdate?: (data: any, where: any) => void | Promise<void>;
  beforeDelete?: (where: any) => void | Promise<void>;
  afterDelete?: (where: any) => void | Promise<void>;
  beforeFind?: (options: QueryOptions) => void | Promise<void>;
  afterFind?: (results: any[], options: QueryOptions) => void | Promise<void>;
}

// ✅ Validation Types
export interface ValidationRules {
  [fieldName: string]: ValidationRule[];
}

export interface ValidationRule {
  rule: string;
  message?: string;
  params?: any[];
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
}

// 🔍 Query Types
export interface QueryOptions {
  select?: string[];
  where?: WhereClause;
  orderBy?: OrderByClause[];
  groupBy?: string[];
  having?: WhereClause;
  limit?: number;
  offset?: number;
  include?: IncludeClause[];
  distinct?: boolean;
  raw?: boolean;
}

export interface WhereClause {
  [field: string]: any | {
    $eq?: any;
    $ne?: any;
    $gt?: any;
    $gte?: any;
    $lt?: any;
    $lte?: any;
    $in?: any[];
    $nin?: any[];
    $like?: string;
    $ilike?: string;
    $between?: [any, any];
    $null?: boolean;
    $and?: WhereClause[];
    $or?: WhereClause[];
  };
}

export interface OrderByClause {
  field: string;
  direction: 'ASC' | 'DESC';
}

export interface IncludeClause {
  model: string;
  as?: string;
  where?: WhereClause;
  select?: string[];
  include?: IncludeClause[];
}

// 📝 Migration Types
export interface Migration {
  id: string;
  name: string;
  up: (db: DatabaseConnection) => Promise<void>;
  down: (db: DatabaseConnection) => Promise<void>;
  timestamp: Date;
}

export interface MigrationRecord {
  id: string;
  name: string;
  executed_at: Date;
  batch: number;
}

// 🔌 Connection Types
export interface DatabaseConnection {
  driver: DatabaseDriver;
  isConnected: boolean;
  query: (sql: string, params?: any[]) => Promise<any[]>;
  execute: (sql: string, params?: any[]) => Promise<{ affectedRows: number; insertId?: number }>;
  transaction: <T>(callback: (trx: Transaction) => Promise<T>) => Promise<T>;
  close: () => Promise<void>;
  ping: () => Promise<boolean>;
}

export interface Transaction {
  query: (sql: string, params?: any[]) => Promise<any[]>;
  execute: (sql: string, params?: any[]) => Promise<{ affectedRows: number; insertId?: number }>;
  commit: () => Promise<void>;
  rollback: () => Promise<void>;
}

// 📊 Model Instance Types
export interface ModelInstance {
  [key: string]: any;
  save: () => Promise<ModelInstance>;
  delete: () => Promise<boolean>;
  reload: () => Promise<ModelInstance>;
  toJSON: () => Record<string, any>;
  isDirty: () => boolean;
  getChanges: () => Record<string, any>;
}

// 🏭 Model Class Types
export interface ModelClass {
  tableName: string;
  definition: ModelDefinition;
  options: ModelOptions;
  
  // Query methods
  find: (options?: QueryOptions) => Promise<ModelInstance[]>;
  findOne: (options?: QueryOptions) => Promise<ModelInstance | null>;
  findById: (id: any) => Promise<ModelInstance | null>;
  create: (data: Record<string, any>) => Promise<ModelInstance>;
  update: (data: Record<string, any>, where: WhereClause) => Promise<number>;
  delete: (where: WhereClause) => Promise<number>;
  count: (where?: WhereClause) => Promise<number>;
  exists: (where: WhereClause) => Promise<boolean>;
  
  // Relationship methods
  hasOne: (model: string, options?: Partial<RelationDefinition>) => void;
  hasMany: (model: string, options?: Partial<RelationDefinition>) => void;
  belongsTo: (model: string, options?: Partial<RelationDefinition>) => void;
  belongsToMany: (model: string, options?: Partial<RelationDefinition>) => void;
  
  // Schema methods
  createTable: () => Promise<void>;
  dropTable: () => Promise<void>;
  addColumn: (name: string, definition: FieldDefinition) => Promise<void>;
  dropColumn: (name: string) => Promise<void>;
  addIndex: (definition: IndexDefinition) => Promise<void>;
  dropIndex: (name: string) => Promise<void>;
}

// 🎯 Database Manager Types
export interface DatabaseManager {
  config: DatabaseConfig;
  connection: DatabaseConnection | null;
  models: Map<string, ModelClass>;
  
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  isConnected: () => boolean;
  
  // Model registration
  defineModel: (name: string, definition: ModelDefinition, options?: ModelOptions) => ModelClass;
  getModel: (name: string) => ModelClass;
  
  // Migration methods
  migrate: () => Promise<void>;
  rollback: (steps?: number) => Promise<void>;
  getMigrationStatus: () => Promise<MigrationRecord[]>;
  
  // Utility methods
  raw: (sql: string, params?: any[]) => Promise<any[]>;
  transaction: <T>(callback: (trx: Transaction) => Promise<T>) => Promise<T>;
  
  // Cache methods
  clearCache: () => void;
  getCacheStats: () => { hits: number; misses: number; size: number };
}

// 📈 Performance Types
export interface QueryPerformance {
  sql: string;
  params: any[];
  duration: number;
  rows: number;
  cached: boolean;
  timestamp: Date;
}

export interface DatabaseStats {
  connections: {
    active: number;
    idle: number;
    total: number;
  };
  queries: {
    total: number;
    successful: number;
    failed: number;
    averageDuration: number;
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
  };
  tables: {
    [tableName: string]: {
      rows: number;
      size: number;
      indexes: number;
    };
  };
}

// 🔄 Sync Types (for offline capabilities)
export interface SyncConfig {
  enabled: boolean;
  strategy: 'push' | 'pull' | 'bidirectional';
  conflictResolution: 'client' | 'server' | 'manual';
  batchSize: number;
  retryAttempts: number;
  syncInterval: number;
}

export interface SyncRecord {
  id: string;
  table: string;
  operation: 'create' | 'update' | 'delete';
  data: any;
  timestamp: Date;
  synced: boolean;
  attempts: number;
}

export interface ConflictRecord {
  id: string;
  table: string;
  localData: any;
  remoteData: any;
  timestamp: Date;
  resolved: boolean;
}
