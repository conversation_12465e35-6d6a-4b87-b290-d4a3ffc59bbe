{"name": "kilat-plugins", "version": "1.0.0", "description": "🔌 Official and community plugins for Kilat.js framework", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["kilat", "kilatjs", "plugins", "framework", "fullstack", "typescript", "react", "bun"], "author": "KangPCode", "license": "MIT", "dependencies": {"kilat-core": "workspace:*", "kilat-utils": "workspace:*", "kilat-backend": "workspace:*", "kilat-db": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsup": "^7.0.0", "vitest": "^0.34.0", "eslint": "^8.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}}