import { createLogger } from 'kilat-utils';
import type { 
  KilatPlugin, 
  PluginManagerConfig, 
  PluginRegistryEntry, 
  PluginStatus,
  PluginEvent,
  HealthStatus
} from '../types';

/**
 * 🔌 Plugin Manager for Kilat.js
 * Manages plugin lifecycle, dependencies, and health monitoring
 */
export class PluginManager {
  private plugins = new Map<string, PluginRegistryEntry>();
  private config: PluginManagerConfig;
  private logger = createLogger({ prefix: 'PluginManager' });
  private eventListeners = new Map<string, Function[]>();
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(config: PluginManagerConfig = {}) {
    this.config = {
      autoLoad: true,
      errorHandling: 'lenient',
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 30000
      },
      healthCheck: {
        enabled: true,
        interval: 60000, // 1 minute
        timeout: 5000,
        failureThreshold: 3
      },
      ...config
    };

    if (this.config.healthCheck?.enabled) {
      this.startHealthCheck();
    }
  }

  // 📝 Register a plugin
  async register(plugin: KilatPlugin): Promise<boolean> {
    try {
      // Validate plugin
      this.validatePlugin(plugin);

      // Check if already registered
      if (this.plugins.has(plugin.name)) {
        this.logger.warn(`Plugin ${plugin.name} is already registered`);
        return false;
      }

      // Create registry entry
      const entry: PluginRegistryEntry = {
        plugin,
        status: 'registered',
        retryCount: 0
      };

      this.plugins.set(plugin.name, entry);
      this.logger.info(`Plugin ${plugin.name} registered`);

      // Emit event
      this.emitEvent({
        type: 'registered',
        plugin: plugin.name,
        timestamp: new Date()
      });

      // Auto-load if enabled
      if (this.config.autoLoad) {
        await this.load(plugin.name);
      }

      return true;

    } catch (error) {
      this.logger.error(`Failed to register plugin ${plugin.name}:`, error);
      return false;
    }
  }

  // 🚀 Load a plugin
  async load(pluginName: string): Promise<boolean> {
    const entry = this.plugins.get(pluginName);
    if (!entry) {
      this.logger.error(`Plugin ${pluginName} not found`);
      return false;
    }

    if (entry.status === 'loaded' || entry.status === 'active') {
      this.logger.warn(`Plugin ${pluginName} is already loaded`);
      return true;
    }

    try {
      entry.status = 'loading';
      this.logger.info(`Loading plugin ${pluginName}...`);

      // Check dependencies
      await this.loadDependencies(entry.plugin);

      // Initialize plugin
      if (entry.plugin.onInit) {
        await entry.plugin.onInit({} as any); // Context would be provided
      }

      entry.status = 'loaded';
      entry.loadedAt = new Date();
      entry.lastError = undefined;
      entry.retryCount = 0;

      this.logger.success(`Plugin ${pluginName} loaded successfully`);

      // Emit event
      this.emitEvent({
        type: 'loaded',
        plugin: pluginName,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      entry.status = 'error';
      entry.lastError = error as Error;
      entry.retryCount = (entry.retryCount || 0) + 1;

      this.logger.error(`Failed to load plugin ${pluginName}:`, error);

      // Emit error event
      this.emitEvent({
        type: 'error',
        plugin: pluginName,
        timestamp: new Date(),
        error: error as Error
      });

      // Retry if configured
      if (this.shouldRetry(entry)) {
        await this.scheduleRetry(pluginName);
      }

      return false;
    }
  }

  // ⚡ Activate a plugin
  async activate(pluginName: string): Promise<boolean> {
    const entry = this.plugins.get(pluginName);
    if (!entry) {
      this.logger.error(`Plugin ${pluginName} not found`);
      return false;
    }

    if (entry.status !== 'loaded') {
      this.logger.error(`Plugin ${pluginName} must be loaded before activation`);
      return false;
    }

    try {
      entry.status = 'active';
      this.logger.info(`Plugin ${pluginName} activated`);

      // Emit event
      this.emitEvent({
        type: 'activated',
        plugin: pluginName,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      this.logger.error(`Failed to activate plugin ${pluginName}:`, error);
      return false;
    }
  }

  // 🛑 Deactivate a plugin
  async deactivate(pluginName: string): Promise<boolean> {
    const entry = this.plugins.get(pluginName);
    if (!entry) {
      this.logger.error(`Plugin ${pluginName} not found`);
      return false;
    }

    if (entry.status !== 'active') {
      this.logger.warn(`Plugin ${pluginName} is not active`);
      return true;
    }

    try {
      entry.status = 'loaded';
      this.logger.info(`Plugin ${pluginName} deactivated`);

      // Emit event
      this.emitEvent({
        type: 'deactivated',
        plugin: pluginName,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      this.logger.error(`Failed to deactivate plugin ${pluginName}:`, error);
      return false;
    }
  }

  // 🗑️ Unload a plugin
  async unload(pluginName: string): Promise<boolean> {
    const entry = this.plugins.get(pluginName);
    if (!entry) {
      this.logger.error(`Plugin ${pluginName} not found`);
      return false;
    }

    try {
      // Deactivate first if active
      if (entry.status === 'active') {
        await this.deactivate(pluginName);
      }

      // Call destroy hook
      if (entry.plugin.onDestroy) {
        await entry.plugin.onDestroy();
      }

      entry.status = 'unloaded';
      this.logger.info(`Plugin ${pluginName} unloaded`);

      // Emit event
      this.emitEvent({
        type: 'unloaded',
        plugin: pluginName,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      this.logger.error(`Failed to unload plugin ${pluginName}:`, error);
      return false;
    }
  }

  // 🗑️ Unregister a plugin
  async unregister(pluginName: string): Promise<boolean> {
    const entry = this.plugins.get(pluginName);
    if (!entry) {
      this.logger.error(`Plugin ${pluginName} not found`);
      return false;
    }

    try {
      // Unload first if loaded
      if (entry.status !== 'registered') {
        await this.unload(pluginName);
      }

      this.plugins.delete(pluginName);
      this.logger.info(`Plugin ${pluginName} unregistered`);

      return true;

    } catch (error) {
      this.logger.error(`Failed to unregister plugin ${pluginName}:`, error);
      return false;
    }
  }

  // 🔍 Get plugin status
  getStatus(pluginName: string): PluginStatus | null {
    const entry = this.plugins.get(pluginName);
    return entry ? entry.status : null;
  }

  // 📋 List all plugins
  listPlugins(): Array<{ name: string; status: PluginStatus; version: string }> {
    return Array.from(this.plugins.entries()).map(([name, entry]) => ({
      name,
      status: entry.status,
      version: entry.plugin.version
    }));
  }

  // 🔍 Get plugin details
  getPlugin(pluginName: string): PluginRegistryEntry | null {
    return this.plugins.get(pluginName) || null;
  }

  // 🏥 Health check
  async healthCheck(pluginName?: string): Promise<HealthStatus | Map<string, HealthStatus>> {
    if (pluginName) {
      return this.checkPluginHealth(pluginName);
    }

    const healthStatuses = new Map<string, HealthStatus>();
    
    for (const [name] of this.plugins) {
      healthStatuses.set(name, await this.checkPluginHealth(name));
    }

    return healthStatuses;
  }

  // 🔧 Private methods
  private validatePlugin(plugin: KilatPlugin): void {
    if (!plugin.name) {
      throw new Error('Plugin name is required');
    }

    if (!plugin.version) {
      throw new Error('Plugin version is required');
    }

    // Validate version format
    if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      throw new Error('Plugin version must follow semantic versioning');
    }
  }

  private async loadDependencies(plugin: KilatPlugin): Promise<void> {
    if (!plugin.dependencies) return;

    for (const dep of plugin.dependencies) {
      const depEntry = this.plugins.get(dep);
      
      if (!depEntry) {
        throw new Error(`Dependency ${dep} not found`);
      }

      if (depEntry.status !== 'loaded' && depEntry.status !== 'active') {
        await this.load(dep);
      }
    }
  }

  private shouldRetry(entry: PluginRegistryEntry): boolean {
    const retryPolicy = this.config.retryPolicy!;
    return entry.retryCount! < retryPolicy.maxRetries;
  }

  private async scheduleRetry(pluginName: string): Promise<void> {
    const entry = this.plugins.get(pluginName)!;
    const retryPolicy = this.config.retryPolicy!;
    
    let delay = retryPolicy.baseDelay;
    
    if (retryPolicy.backoffStrategy === 'exponential') {
      delay = Math.min(
        retryPolicy.baseDelay * Math.pow(2, entry.retryCount! - 1),
        retryPolicy.maxDelay
      );
    } else if (retryPolicy.backoffStrategy === 'linear') {
      delay = Math.min(
        retryPolicy.baseDelay * entry.retryCount!,
        retryPolicy.maxDelay
      );
    }

    this.logger.info(`Scheduling retry for plugin ${pluginName} in ${delay}ms`);

    setTimeout(() => {
      this.load(pluginName);
    }, delay);
  }

  private async checkPluginHealth(pluginName: string): Promise<HealthStatus> {
    const entry = this.plugins.get(pluginName);
    
    if (!entry) {
      return {
        status: 'unknown',
        lastCheck: new Date(),
        details: { error: 'Plugin not found' }
      };
    }

    try {
      // Basic health check - plugin is loaded and no recent errors
      const isHealthy = entry.status === 'active' || entry.status === 'loaded';
      const hasRecentErrors = entry.lastError && 
        (Date.now() - (entry.loadedAt?.getTime() || 0)) < 300000; // 5 minutes

      const status: HealthStatus = {
        status: isHealthy && !hasRecentErrors ? 'healthy' : 'unhealthy',
        lastCheck: new Date(),
        details: {
          status: entry.status,
          loadedAt: entry.loadedAt,
          retryCount: entry.retryCount,
          lastError: entry.lastError?.message
        }
      };

      entry.healthStatus = status;
      return status;

    } catch (error) {
      return {
        status: 'unhealthy',
        lastCheck: new Date(),
        details: { error: (error as Error).message }
      };
    }
  }

  private startHealthCheck(): void {
    const interval = this.config.healthCheck!.interval;
    
    this.healthCheckInterval = setInterval(async () => {
      for (const [name] of this.plugins) {
        await this.checkPluginHealth(name);
      }
    }, interval);
  }

  private emitEvent(event: PluginEvent): void {
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        this.logger.error('Event listener error:', error);
      }
    });
  }

  // 📡 Event handling
  on(eventType: string, listener: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  off(eventType: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 🧹 Cleanup
  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Unload all plugins
    for (const [name] of this.plugins) {
      this.unload(name);
    }

    this.plugins.clear();
    this.eventListeners.clear();
  }
}
