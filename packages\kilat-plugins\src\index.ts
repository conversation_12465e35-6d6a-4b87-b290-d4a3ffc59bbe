// 🔌 Kilat.js Plugins - Official and Community Plugins
// Export all official plugins

// 🔐 Authentication Plugins
export * from './auth/AuthPlugin';
export * from './auth/JWTPlugin';
export * from './auth/OAuthPlugin';
export * from './auth/SessionPlugin';

// 📝 CMS Plugins
export * from './cms/CMSPlugin';
export * from './cms/BlogPlugin';
export * from './cms/MediaPlugin';

// 💳 Payment Plugins
export * from './payments/PaymentPlugin';
export * from './payments/StripePlugin';
export * from './payments/PayPalPlugin';

// 🤖 AI Assistant Plugins
export * from './ai/AIPlugin';
export * from './ai/ChatbotPlugin';
export * from './ai/TranslationPlugin';

// 📊 Monitoring Plugins
export * from './monitoring/MonitoringPlugin';
export * from './monitoring/AnalyticsPlugin';
export * from './monitoring/LoggingPlugin';

// 🔧 Utility Plugins
export * from './utils/CachePlugin';
export * from './utils/CompressionPlugin';
export * from './utils/SecurityPlugin';

// 🎨 UI Plugins
export * from './ui/ThemePlugin';
export * from './ui/ComponentLibraryPlugin';
export * from './ui/IconPlugin';

// 📱 Platform Plugins
export * from './platform/PWAPlugin';
export * from './platform/ElectronPlugin';
export * from './platform/MobilePlugin';

// 🌐 Integration Plugins
export * from './integrations/DatabasePlugin';
export * from './integrations/APIPlugin';
export * from './integrations/WebhookPlugin';

// 🔌 Plugin System Core
export * from './core/PluginManager';
export * from './core/PluginRegistry';
export * from './core/PluginLoader';

// 📋 Types
export * from './types';

// 🏭 Plugin Factory
export { createPlugin } from './core/PluginFactory';

// 📦 Plugin Collections
export { officialPlugins } from './collections/official';
export { communityPlugins } from './collections/community';
export { experimentalPlugins } from './collections/experimental';

// 🎯 Default export
export default {
  version: '1.0.0',
  name: 'kilat-plugins'
};
