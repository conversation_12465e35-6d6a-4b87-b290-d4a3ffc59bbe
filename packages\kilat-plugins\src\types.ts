import type { KilatContextValue } from 'kilat-core';

// 🔌 Base Plugin Interface
export interface KilatPlugin {
  name: string;
  version: string;
  description?: string;
  author?: string;
  homepage?: string;
  keywords?: string[];
  
  // Plugin lifecycle
  onInit?: (context: KilatContextValue) => Promise<void> | void;
  onDestroy?: () => Promise<void> | void;
  onRequest?: (context: any) => Promise<void> | void;
  onResponse?: (context: any) => Promise<void> | void;
  onError?: (error: Error, context?: any) => Promise<void> | void;
  
  // Plugin configuration
  config?: PluginConfig;
  dependencies?: string[];
  peerDependencies?: string[];
  
  // Plugin metadata
  meta?: PluginMeta;
}

// 🔧 Plugin Configuration
export interface PluginConfig {
  enabled?: boolean;
  priority?: number;
  retryCount?: number;
  maxRetries?: number;
  timeout?: number;
  fallback?: boolean;
  settings?: Record<string, any>;
}

// 📋 Plugin Metadata
export interface PluginMeta {
  category: PluginCategory;
  platform?: ('web' | 'desktop' | 'mobile')[];
  environment?: ('development' | 'production' | 'test')[];
  experimental?: boolean;
  deprecated?: boolean;
  license?: string;
  repository?: string;
  bugs?: string;
  changelog?: string;
}

// 🏷️ Plugin Categories
export type PluginCategory = 
  | 'auth'
  | 'cms'
  | 'payments'
  | 'ai'
  | 'monitoring'
  | 'utils'
  | 'ui'
  | 'platform'
  | 'integrations'
  | 'database'
  | 'security'
  | 'performance'
  | 'development'
  | 'testing'
  | 'deployment';

// 🔐 Authentication Plugin Types
export interface AuthPluginConfig extends PluginConfig {
  providers?: string[];
  sessionTimeout?: number;
  tokenExpiry?: number;
  refreshTokens?: boolean;
  multiFactorAuth?: boolean;
  passwordPolicy?: PasswordPolicy;
}

export interface PasswordPolicy {
  minLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSymbols?: boolean;
  preventReuse?: number;
}

// 📝 CMS Plugin Types
export interface CMSPluginConfig extends PluginConfig {
  contentTypes?: ContentType[];
  mediaStorage?: MediaStorageConfig;
  workflow?: WorkflowConfig;
  permissions?: PermissionConfig;
}

export interface ContentType {
  name: string;
  fields: ContentField[];
  slug?: string;
  template?: string;
  permissions?: string[];
}

export interface ContentField {
  name: string;
  type: 'text' | 'textarea' | 'rich-text' | 'number' | 'date' | 'boolean' | 'select' | 'file' | 'image';
  required?: boolean;
  validation?: ValidationRule[];
  options?: string[];
}

export interface ValidationRule {
  type: string;
  value?: any;
  message?: string;
}

export interface MediaStorageConfig {
  provider: 'local' | 'aws-s3' | 'cloudinary' | 'azure';
  settings: Record<string, any>;
  maxFileSize?: number;
  allowedTypes?: string[];
}

export interface WorkflowConfig {
  enabled: boolean;
  states: WorkflowState[];
  transitions: WorkflowTransition[];
}

export interface WorkflowState {
  name: string;
  label: string;
  color?: string;
  permissions?: string[];
}

export interface WorkflowTransition {
  from: string;
  to: string;
  label: string;
  permissions?: string[];
}

export interface PermissionConfig {
  roles: Role[];
  permissions: Permission[];
}

export interface Role {
  name: string;
  label: string;
  permissions: string[];
  inherits?: string[];
}

export interface Permission {
  name: string;
  label: string;
  resource?: string;
  action?: string;
}

// 💳 Payment Plugin Types
export interface PaymentPluginConfig extends PluginConfig {
  providers?: PaymentProvider[];
  currency?: string;
  webhooks?: WebhookConfig;
  security?: PaymentSecurityConfig;
}

export interface PaymentProvider {
  name: string;
  enabled: boolean;
  credentials: Record<string, string>;
  settings: Record<string, any>;
}

export interface WebhookConfig {
  enabled: boolean;
  endpoints: WebhookEndpoint[];
  security: WebhookSecurity;
}

export interface WebhookEndpoint {
  event: string;
  url: string;
  method?: string;
  headers?: Record<string, string>;
}

export interface WebhookSecurity {
  signature?: boolean;
  secretKey?: string;
  algorithm?: string;
}

export interface PaymentSecurityConfig {
  encryption: boolean;
  tokenization: boolean;
  pciCompliance: boolean;
  fraudDetection: boolean;
}

// 🤖 AI Plugin Types
export interface AIPluginConfig extends PluginConfig {
  providers?: AIProvider[];
  models?: AIModel[];
  features?: AIFeature[];
  limits?: AILimits;
}

export interface AIProvider {
  name: string;
  apiKey: string;
  endpoint?: string;
  models: string[];
}

export interface AIModel {
  name: string;
  provider: string;
  type: 'text' | 'image' | 'audio' | 'video' | 'multimodal';
  capabilities: string[];
}

export interface AIFeature {
  name: string;
  enabled: boolean;
  model: string;
  settings: Record<string, any>;
}

export interface AILimits {
  requestsPerMinute?: number;
  requestsPerDay?: number;
  tokensPerRequest?: number;
  maxFileSize?: number;
}

// 📊 Monitoring Plugin Types
export interface MonitoringPluginConfig extends PluginConfig {
  metrics?: MetricConfig[];
  alerts?: AlertConfig[];
  dashboards?: DashboardConfig[];
  retention?: RetentionConfig;
}

export interface MetricConfig {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  labels?: string[];
  description?: string;
}

export interface AlertConfig {
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  channels: string[];
}

export interface DashboardConfig {
  name: string;
  widgets: WidgetConfig[];
  layout: LayoutConfig;
}

export interface WidgetConfig {
  type: 'chart' | 'table' | 'metric' | 'log';
  title: string;
  query: string;
  settings: Record<string, any>;
}

export interface LayoutConfig {
  columns: number;
  rows: number;
  positions: WidgetPosition[];
}

export interface WidgetPosition {
  widget: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface RetentionConfig {
  metrics: string;
  logs: string;
  traces: string;
}

// 🔧 Plugin Manager Types
export interface PluginManagerConfig {
  autoLoad?: boolean;
  loadOrder?: string[];
  errorHandling?: 'strict' | 'lenient' | 'ignore';
  retryPolicy?: RetryPolicy;
  healthCheck?: HealthCheckConfig;
}

export interface RetryPolicy {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  baseDelay: number;
  maxDelay: number;
}

export interface HealthCheckConfig {
  enabled: boolean;
  interval: number;
  timeout: number;
  failureThreshold: number;
}

// 📦 Plugin Registry Types
export interface PluginRegistryEntry {
  plugin: KilatPlugin;
  status: PluginStatus;
  loadedAt?: Date;
  lastError?: Error;
  retryCount?: number;
  healthStatus?: HealthStatus;
}

export type PluginStatus = 
  | 'registered'
  | 'loading'
  | 'loaded'
  | 'active'
  | 'error'
  | 'disabled'
  | 'unloaded';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  details?: Record<string, any>;
}

// 🎯 Plugin Events
export interface PluginEvent {
  type: PluginEventType;
  plugin: string;
  timestamp: Date;
  data?: any;
  error?: Error;
}

export type PluginEventType =
  | 'registered'
  | 'loaded'
  | 'activated'
  | 'deactivated'
  | 'unloaded'
  | 'error'
  | 'health-check';

// 🏭 Plugin Factory Types
export interface PluginFactoryOptions {
  name: string;
  version: string;
  category: PluginCategory;
  config?: PluginConfig;
  meta?: Partial<PluginMeta>;
}

export interface PluginBuilder {
  onInit(handler: (context: KilatContextValue) => Promise<void> | void): PluginBuilder;
  onDestroy(handler: () => Promise<void> | void): PluginBuilder;
  onRequest(handler: (context: any) => Promise<void> | void): PluginBuilder;
  onResponse(handler: (context: any) => Promise<void> | void): PluginBuilder;
  onError(handler: (error: Error, context?: any) => Promise<void> | void): PluginBuilder;
  withConfig(config: PluginConfig): PluginBuilder;
  withMeta(meta: Partial<PluginMeta>): PluginBuilder;
  withDependencies(dependencies: string[]): PluginBuilder;
  build(): KilatPlugin;
}
