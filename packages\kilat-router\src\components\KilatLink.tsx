import React from 'react';
import { Link } from 'react-router-dom';

/**
 * 🔗 KilatLink Component
 * Enhanced Link component with Kilat.js features
 */
export interface KilatLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  prefetch?: boolean;
  external?: boolean;
  variant?: 'default' | 'button' | 'nav';
}

export function KilatLink({
  to,
  children,
  prefetch = false,
  external = false,
  variant = 'default',
  className = '',
  ...props
}: KilatLinkProps) {
  const baseClasses = {
    default: 'k-link',
    button: 'k-btn k-btn-primary',
    nav: 'k-nav-link'
  };

  const classes = `${baseClasses[variant]} ${className}`.trim();

  if (external) {
    return (
      <a
        href={to}
        className={classes}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    );
  }

  return (
    <Link to={to} className={classes} {...props}>
      {children}
    </Link>
  );
}

export default KilatLink;
