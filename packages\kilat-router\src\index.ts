/**
 * 🧭 Kilat.js Router - Simple and powerful routing
 */

// Re-export React Router components for convenience
export {
  BrowserRouter,
  Routes,
  Route,
  Link,
  Navigate,
  Outlet,
  useNavigate,
  useLocation,
  useParams,
  useSearchParams
} from 'react-router-dom';

// Export Kilat-specific components
export { <PERSON>latRoute } from './components/KilatRoute';
export { KilatLink } from './components/KilatLink';
export { KilatOutlet } from './components/KilatOutlet';
export { NestedLayout } from './components/NestedLayout';

// Export types
export type {
  KilatRouteProps,
  KilatLinkProps,
  KilatOutletProps,
  NestedLayoutProps
} from './components/types';

// Version info
export const KILAT_ROUTER_VERSION = '1.0.0';
