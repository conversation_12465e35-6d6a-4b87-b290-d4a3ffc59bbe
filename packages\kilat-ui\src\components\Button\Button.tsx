import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';
import { useTheme } from 'kilat-utils';

// 🎨 Button variants using CVA
const buttonVariants = cva(
  // Base styles
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        // Theme-specific variants
        cyberpunk: 'relative overflow-hidden border border-cyan-500 bg-transparent text-cyan-400 hover:text-black hover:bg-cyan-400 shadow-glow hover:shadow-glow-lg before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-cyan-400 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700',
        nusantara: 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border border-amber-600 hover:from-amber-600 hover:to-orange-600 shadow-warm',
        retro: 'bg-gradient-to-r from-pink-500 to-purple-500 text-white border-2 border-pink-400 hover:from-pink-600 hover:to-purple-600 shadow-neon',
        material: 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:scale-105',
        neumorphism: 'bg-gray-200 text-gray-800 shadow-neumorphism hover:shadow-neumorphism-inset border-none',
        aurora: 'bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 text-white hover:from-green-500 hover:via-blue-600 hover:to-purple-700 shadow-aurora',
        carbon: 'bg-gray-900 text-white border border-gray-700 hover:bg-gray-800 shadow-carbon',
        unix: 'bg-black text-green-400 border border-green-400 hover:bg-green-400 hover:text-black font-mono shadow-terminal',
        dana: 'bg-blue-600 text-white hover:bg-blue-700 rounded-lg shadow-dana',
        glassmorphism: 'bg-white/20 backdrop-blur-md border border-white/30 text-white hover:bg-white/30 shadow-glass',
        asymmetric: 'bg-red-500 text-white hover:bg-red-600 transform hover:skew-x-2 shadow-asymmetric'
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        xl: 'h-12 rounded-lg px-10 text-base',
        icon: 'h-10 w-10'
      },
      animation: {
        none: '',
        pulse: 'animate-pulse',
        bounce: 'animate-bounce',
        spin: 'animate-spin',
        ping: 'animate-ping',
        // Theme-specific animations
        glow: 'animate-pulse-glow',
        flicker: 'animate-flicker',
        neon: 'animate-neon-flicker',
        warm: 'animate-warm-pulse',
        shimmer: 'animate-golden-shimmer',
        glitch: 'animate-glitch'
      },
      loading: {
        true: 'cursor-not-allowed',
        false: ''
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      animation: 'none',
      loading: false
    }
  }
);

// 🎯 Button props interface
export interface ButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  gradient?: boolean;
  glow?: boolean;
  theme?: string;
}

// 🎯 Button component
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      animation,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      gradient = false,
      glow = false,
      theme,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const { config: themeConfig } = useTheme();
    
    // Auto-detect theme variant if theme prop is provided
    const effectiveVariant = theme && !variant ? theme as any : variant;
    
    // Auto-detect animation based on theme
    const effectiveAnimation = animation || getThemeAnimation(effectiveVariant);

    return (
      <button
        className={cn(
          buttonVariants({ 
            variant: effectiveVariant, 
            size, 
            animation: effectiveAnimation,
            loading 
          }),
          fullWidth && 'w-full',
          gradient && 'bg-gradient-to-r',
          glow && getGlowClass(effectiveVariant),
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {/* Loading spinner */}
        {loading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}

        {/* Left icon */}
        {leftIcon && !loading && (
          <span className="mr-2 flex items-center">{leftIcon}</span>
        )}

        {/* Button content */}
        <span className="flex items-center">
          {loading && loadingText ? loadingText : children}
        </span>

        {/* Right icon */}
        {rightIcon && !loading && (
          <span className="ml-2 flex items-center">{rightIcon}</span>
        )}

        {/* Theme-specific effects */}
        {effectiveVariant === 'cyberpunk' && (
          <>
            {/* Scan line effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
            {/* Corner accents */}
            <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-cyan-400" />
            <div className="absolute top-0 right-0 w-2 h-2 border-t-2 border-r-2 border-cyan-400" />
            <div className="absolute bottom-0 left-0 w-2 h-2 border-b-2 border-l-2 border-cyan-400" />
            <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-cyan-400" />
          </>
        )}

        {effectiveVariant === 'nusantara' && (
          <>
            {/* Traditional pattern overlay */}
            <div className="absolute inset-0 bg-[image:var(--k-nusantara-batik-pattern)] opacity-10" />
            {/* Golden shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-400/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1500" />
          </>
        )}

        {effectiveVariant === 'glassmorphism' && (
          <>
            {/* Glass reflection */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-md" />
            {/* Frosted effect */}
            <div className="absolute inset-0 backdrop-blur-sm rounded-md" />
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// 🎨 Helper functions
function getThemeAnimation(variant?: string): 'none' | 'glow' | 'flicker' | 'neon' | 'warm' | 'shimmer' | 'glitch' {
  switch (variant) {
    case 'cyberpunk':
      return 'glow';
    case 'nusantara':
      return 'warm';
    case 'retro':
      return 'neon';
    case 'unix':
      return 'flicker';
    case 'asymmetric':
      return 'glitch';
    default:
      return 'none';
  }
}

function getGlowClass(variant?: string): string {
  switch (variant) {
    case 'cyberpunk':
      return 'shadow-glow hover:shadow-glow-lg';
    case 'nusantara':
      return 'shadow-warm hover:shadow-warm-lg';
    case 'retro':
      return 'shadow-neon hover:shadow-neon-lg';
    case 'material':
      return 'shadow-md hover:shadow-lg';
    case 'aurora':
      return 'shadow-aurora hover:shadow-aurora-lg';
    case 'carbon':
      return 'shadow-carbon hover:shadow-carbon-lg';
    case 'unix':
      return 'shadow-terminal hover:shadow-terminal-lg';
    case 'dana':
      return 'shadow-dana hover:shadow-dana-lg';
    case 'glassmorphism':
      return 'shadow-glass hover:shadow-glass-lg';
    case 'asymmetric':
      return 'shadow-asymmetric hover:shadow-asymmetric-lg';
    default:
      return '';
  }
}

// 🎯 Button group component
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'default' | 'lg';
  variant?: string;
  attached?: boolean;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size = 'default',
  variant,
  attached = false
}) => {
  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        attached && orientation === 'horizontal' && '[&>button]:rounded-none [&>button:first-child]:rounded-l-md [&>button:last-child]:rounded-r-md [&>button:not(:first-child)]:border-l-0',
        attached && orientation === 'vertical' && '[&>button]:rounded-none [&>button:first-child]:rounded-t-md [&>button:last-child]:rounded-b-md [&>button:not(:first-child)]:border-t-0',
        !attached && 'gap-2',
        className
      )}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Button) {
          return React.cloneElement(child, {
            size: child.props.size || size,
            variant: child.props.variant || variant,
            ...child.props
          });
        }
        return child;
      })}
    </div>
  );
};

// 🎯 Icon button component
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'icon', ...props }, ref) => {
    return (
      <Button
        ref={ref}
        size={size}
        className={cn('p-0', className)}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// 🎯 Floating action button
export interface FABProps extends ButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  extended?: boolean;
}

export const FAB = forwardRef<HTMLButtonElement, FABProps>(
  ({ 
    position = 'bottom-right', 
    extended = false, 
    className, 
    size = extended ? 'default' : 'icon',
    ...props 
  }, ref) => {
    const positionClasses = {
      'bottom-right': 'fixed bottom-4 right-4',
      'bottom-left': 'fixed bottom-4 left-4',
      'top-right': 'fixed top-4 right-4',
      'top-left': 'fixed top-4 left-4'
    };

    return (
      <Button
        ref={ref}
        size={size}
        className={cn(
          'rounded-full shadow-lg hover:shadow-xl z-50',
          extended ? 'px-6' : 'w-14 h-14',
          positionClasses[position],
          className
        )}
        {...props}
      />
    );
  }
);

FAB.displayName = 'FAB';

export { Button, buttonVariants };
export type { ButtonProps };
