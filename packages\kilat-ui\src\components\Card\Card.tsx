import React, { forwardRef, HTMLAttributes } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';
import { useTheme } from 'kilat-utils';

// 🎨 Card variants using CVA
const cardVariants = cva(
  // Base styles
  'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-border bg-background',
        elevated: 'shadow-md hover:shadow-lg',
        outlined: 'border-2 border-border bg-transparent',
        filled: 'bg-muted border-transparent',
        // Theme-specific variants
        cyberpunk: 'bg-black/80 border border-cyan-500/30 backdrop-blur-sm shadow-glow-sm hover:shadow-glow bg-[image:var(--k-cyberpunk-grid)] bg-[size:20px_20px]',
        nusantara: 'bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-300 shadow-warm bg-[image:var(--k-nusantara-batik-pattern)]',
        retro: 'bg-gradient-to-br from-pink-900/20 to-purple-900/20 border-2 border-pink-500 shadow-neon backdrop-blur-sm',
        material: 'bg-white shadow-md hover:shadow-lg transform hover:scale-[1.02] border-none',
        neumorphism: 'bg-gray-200 shadow-neumorphism border-none',
        aurora: 'bg-gradient-to-br from-green-50 to-purple-50 border border-blue-200 shadow-aurora',
        carbon: 'bg-gray-900 border border-gray-700 shadow-carbon text-white',
        unix: 'bg-black border border-green-400 shadow-terminal text-green-400 font-mono',
        dana: 'bg-white border border-blue-200 shadow-dana rounded-xl',
        glassmorphism: 'bg-white/20 backdrop-blur-md border border-white/30 shadow-glass',
        asymmetric: 'bg-white border-l-4 border-l-red-500 shadow-asymmetric transform hover:skew-y-1'
      },
      size: {
        sm: 'p-4',
        default: 'p-6',
        lg: 'p-8',
        xl: 'p-10'
      },
      interactive: {
        true: 'cursor-pointer hover:shadow-md transition-shadow',
        false: ''
      },
      loading: {
        true: 'animate-pulse',
        false: ''
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      interactive: false,
      loading: false
    }
  }
);

// 🎯 Card props interface
export interface CardProps
  extends HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
  loading?: boolean;
  theme?: string;
  glow?: boolean;
  pattern?: boolean;
}

// 🎯 Card component
const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant,
      size,
      interactive,
      loading = false,
      theme,
      glow = false,
      pattern = false,
      children,
      ...props
    },
    ref
  ) => {
    const { config: themeConfig } = useTheme();
    
    // Auto-detect theme variant if theme prop is provided
    const effectiveVariant = theme && !variant ? theme as any : variant;

    return (
      <div
        ref={ref}
        className={cn(
          cardVariants({ 
            variant: effectiveVariant, 
            size, 
            interactive,
            loading 
          }),
          glow && getGlowClass(effectiveVariant),
          pattern && getPatternClass(effectiveVariant),
          className
        )}
        {...props}
      >
        {/* Theme-specific effects */}
        {effectiveVariant === 'cyberpunk' && (
          <>
            {/* Corner accents */}
            <div className="absolute top-2 left-2 w-3 h-3 border-t-2 border-l-2 border-cyan-400 opacity-60" />
            <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-cyan-400 opacity-60" />
            <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-cyan-400 opacity-60" />
            <div className="absolute bottom-2 right-2 w-3 h-3 border-b-2 border-r-2 border-cyan-400 opacity-60" />
            {/* Scan line */}
            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-50" />
          </>
        )}

        {effectiveVariant === 'nusantara' && pattern && (
          <>
            {/* Traditional pattern overlay */}
            <div className="absolute inset-0 bg-[image:var(--k-nusantara-batik-pattern)] opacity-5 rounded-lg" />
            {/* Golden accent border */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 rounded-t-lg" />
          </>
        )}

        {effectiveVariant === 'glassmorphism' && (
          <>
            {/* Glass reflection */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg" />
            {/* Frosted border */}
            <div className="absolute inset-0 rounded-lg border border-white/20" />
          </>
        )}

        {effectiveVariant === 'retro' && (
          <>
            {/* Neon glow border */}
            <div className="absolute inset-0 rounded-lg border-2 border-pink-500 opacity-50 blur-sm" />
            {/* Grid pattern */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(255,0,128,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,0,128,0.1)_1px,transparent_1px)] bg-[size:20px_20px] rounded-lg" />
          </>
        )}

        {/* Loading skeleton */}
        {loading ? (
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded animate-pulse" />
            <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
            <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
          </div>
        ) : (
          children
        )}
      </div>
    );
  }
);

Card.displayName = 'Card';

// 🎯 Card Header component
export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  divider?: boolean;
}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, divider = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'flex flex-col space-y-1.5 p-6',
        divider && 'border-b border-border pb-4',
        className
      )}
      {...props}
    />
  )
);

CardHeader.displayName = 'CardHeader';

// 🎯 Card Title component
const CardTitle = forwardRef<
  HTMLParagraphElement,
  HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      'text-2xl font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
));

CardTitle.displayName = 'CardTitle';

// 🎯 Card Description component
const CardDescription = forwardRef<
  HTMLParagraphElement,
  HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));

CardDescription.displayName = 'CardDescription';

// 🎯 Card Content component
const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
  )
);

CardContent.displayName = 'CardContent';

// 🎯 Card Footer component
export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  divider?: boolean;
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
}

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, divider = false, justify = 'start', ...props }, ref) => {
    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around'
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center p-6 pt-0',
          justifyClasses[justify],
          divider && 'border-t border-border pt-4',
          className
        )}
        {...props}
      />
    );
  }
);

CardFooter.displayName = 'CardFooter';

// 🎯 Card Image component
export interface CardImageProps extends HTMLAttributes<HTMLDivElement> {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
}

const CardImage = forwardRef<HTMLDivElement, CardImageProps>(
  ({ 
    className, 
    src, 
    alt, 
    aspectRatio = 'video',
    objectFit = 'cover',
    ...props 
  }, ref) => {
    const aspectRatioClasses = {
      square: 'aspect-square',
      video: 'aspect-video',
      wide: 'aspect-[21/9]',
      tall: 'aspect-[9/16]'
    };

    const objectFitClasses = {
      cover: 'object-cover',
      contain: 'object-contain',
      fill: 'object-fill',
      none: 'object-none',
      'scale-down': 'object-scale-down'
    };

    return (
      <div
        ref={ref}
        className={cn(
          'relative overflow-hidden rounded-t-lg',
          aspectRatioClasses[aspectRatio],
          className
        )}
        {...props}
      >
        <img
          src={src}
          alt={alt}
          className={cn(
            'h-full w-full transition-transform duration-300 hover:scale-105',
            objectFitClasses[objectFit]
          )}
        />
      </div>
    );
  }
);

CardImage.displayName = 'CardImage';

// 🎨 Helper functions
function getGlowClass(variant?: string): string {
  switch (variant) {
    case 'cyberpunk':
      return 'shadow-glow hover:shadow-glow-md';
    case 'nusantara':
      return 'shadow-warm hover:shadow-warm-md';
    case 'retro':
      return 'shadow-neon hover:shadow-neon-md';
    case 'aurora':
      return 'shadow-aurora hover:shadow-aurora-md';
    case 'carbon':
      return 'shadow-carbon hover:shadow-carbon-md';
    case 'unix':
      return 'shadow-terminal hover:shadow-terminal-md';
    case 'dana':
      return 'shadow-dana hover:shadow-dana-md';
    case 'glassmorphism':
      return 'shadow-glass hover:shadow-glass-md';
    case 'asymmetric':
      return 'shadow-asymmetric hover:shadow-asymmetric-md';
    default:
      return '';
  }
}

function getPatternClass(variant?: string): string {
  switch (variant) {
    case 'cyberpunk':
      return 'bg-[image:var(--k-cyberpunk-grid)] bg-[size:20px_20px]';
    case 'nusantara':
      return 'bg-[image:var(--k-nusantara-batik-pattern)]';
    case 'retro':
      return 'bg-[linear-gradient(rgba(255,0,128,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,0,128,0.1)_1px,transparent_1px)] bg-[size:20px_20px]';
    case 'unix':
      return 'bg-[linear-gradient(rgba(0,255,0,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(0,255,0,0.1)_1px,transparent_1px)] bg-[size:10px_10px]';
    default:
      return '';
  }
}

// 🎯 Compound Card component with all sub-components
const CompoundCard = Object.assign(Card, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter,
  Image: CardImage
});

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardImage,
  CompoundCard,
  cardVariants
};

export type { CardProps };
