import React, { forwardRef, InputHTMLAttributes, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';
import { useTheme } from 'kilat-utils';

// 🎨 Input variants using CVA
const inputVariants = cva(
  // Base styles
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-input focus-visible:ring-ring',
        filled: 'bg-muted border-transparent focus-visible:bg-background',
        outlined: 'border-2 border-input bg-transparent',
        underlined: 'border-0 border-b-2 border-input rounded-none bg-transparent px-0 focus-visible:ring-0 focus-visible:border-ring',
        // Theme-specific variants
        cyberpunk: 'bg-black/50 border border-cyan-500/50 text-cyan-400 placeholder-cyan-600 focus:border-cyan-400 focus:shadow-glow-sm focus:ring-cyan-400',
        nusantara: 'bg-white border border-amber-300 text-amber-900 placeholder-amber-500 focus:border-amber-500 focus:ring-amber-200 focus:shadow-warm-sm',
        retro: 'bg-black border-2 border-pink-500 text-pink-400 placeholder-pink-600 focus:border-pink-400 focus:shadow-neon-sm focus:ring-pink-400',
        material: 'bg-white border-b-2 border-gray-300 rounded-none focus:border-blue-500 focus:ring-0 focus:shadow-none',
        neumorphism: 'bg-gray-200 border-none shadow-neumorphism-inset focus:shadow-neumorphism text-gray-800 placeholder-gray-500',
        aurora: 'bg-white/90 border border-blue-200 focus:border-purple-400 focus:ring-purple-200 backdrop-blur-sm',
        carbon: 'bg-gray-900 border border-gray-700 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20',
        unix: 'bg-black border border-green-400 text-green-400 placeholder-green-600 font-mono focus:border-green-300 focus:shadow-terminal-sm',
        dana: 'bg-white border border-blue-200 rounded-lg focus:border-blue-500 focus:ring-blue-100',
        glassmorphism: 'bg-white/20 backdrop-blur-md border border-white/30 text-white placeholder-white/60 focus:border-white/50 focus:ring-white/20',
        asymmetric: 'bg-white border-l-4 border-l-red-500 border-y border-r border-gray-300 focus:border-l-red-600 transform focus:skew-x-1'
      },
      size: {
        sm: 'h-9 px-2 text-xs',
        default: 'h-10 px-3 text-sm',
        lg: 'h-11 px-4 text-base',
        xl: 'h-12 px-5 text-lg'
      },
      state: {
        default: '',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-green-500 focus-visible:ring-green-500',
        warning: 'border-yellow-500 focus-visible:ring-yellow-500'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      state: 'default'
    }
  }
);

// 🎯 Input props interface
export interface InputProps
  extends InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  helperText?: string;
  errorText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
  loading?: boolean;
  clearable?: boolean;
  theme?: string;
  glow?: boolean;
  onClear?: () => void;
}

// 🎯 Input component
const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = 'text',
      variant,
      size,
      state,
      label,
      helperText,
      errorText,
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,
      loading = false,
      clearable = false,
      theme,
      glow = false,
      onClear,
      value,
      disabled,
      ...props
    },
    ref
  ) => {
    const { config: themeConfig } = useTheme();
    const [showPassword, setShowPassword] = useState(false);
    
    // Auto-detect theme variant if theme prop is provided
    const effectiveVariant = theme && !variant ? theme as any : variant;
    
    // Determine effective state
    const effectiveState = errorText ? 'error' : state;
    
    // Handle password visibility toggle
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };
    
    // Handle clear input
    const handleClear = () => {
      if (onClear) {
        onClear();
      }
    };

    const inputElement = (
      <div className="relative flex items-center">
        {/* Left addon */}
        {leftAddon && (
          <div className={cn(
            'flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground',
            effectiveVariant === 'cyberpunk' && 'bg-black/30 border-cyan-500/50 text-cyan-400',
            effectiveVariant === 'nusantara' && 'bg-amber-50 border-amber-300 text-amber-700',
            effectiveVariant === 'retro' && 'bg-black border-pink-500 text-pink-400',
            effectiveVariant === 'carbon' && 'bg-gray-800 border-gray-700 text-gray-300',
            effectiveVariant === 'unix' && 'bg-black border-green-400 text-green-400 font-mono'
          )}>
            {leftAddon}
          </div>
        )}

        {/* Left icon */}
        {leftIcon && (
          <div className="absolute left-3 flex items-center pointer-events-none z-10">
            {leftIcon}
          </div>
        )}

        {/* Input field */}
        <input
          type={type === 'password' && showPassword ? 'text' : type}
          className={cn(
            inputVariants({ 
              variant: effectiveVariant, 
              size, 
              state: effectiveState 
            }),
            leftIcon && 'pl-10',
            (rightIcon || loading || clearable || type === 'password') && 'pr-10',
            leftAddon && 'rounded-l-none border-l-0',
            rightAddon && 'rounded-r-none border-r-0',
            glow && getGlowClass(effectiveVariant),
            className
          )}
          ref={ref}
          value={value}
          disabled={disabled || loading}
          {...props}
        />

        {/* Right side icons */}
        <div className="absolute right-3 flex items-center space-x-1">
          {/* Loading spinner */}
          {loading && (
            <svg
              className="h-4 w-4 animate-spin text-muted-foreground"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          )}

          {/* Clear button */}
          {clearable && value && !loading && (
            <button
              type="button"
              onClick={handleClear}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}

          {/* Password visibility toggle */}
          {type === 'password' && !loading && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {showPassword ? (
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          )}

          {/* Right icon */}
          {rightIcon && !loading && (
            <div className="flex items-center">
              {rightIcon}
            </div>
          )}
        </div>

        {/* Right addon */}
        {rightAddon && (
          <div className={cn(
            'flex items-center px-3 border border-l-0 rounded-r-md bg-muted text-muted-foreground',
            effectiveVariant === 'cyberpunk' && 'bg-black/30 border-cyan-500/50 text-cyan-400',
            effectiveVariant === 'nusantara' && 'bg-amber-50 border-amber-300 text-amber-700',
            effectiveVariant === 'retro' && 'bg-black border-pink-500 text-pink-400',
            effectiveVariant === 'carbon' && 'bg-gray-800 border-gray-700 text-gray-300',
            effectiveVariant === 'unix' && 'bg-black border-green-400 text-green-400 font-mono'
          )}>
            {rightAddon}
          </div>
        )}

        {/* Theme-specific effects */}
        {effectiveVariant === 'cyberpunk' && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-0 left-0 w-2 h-2 border-t border-l border-cyan-400 opacity-60" />
            <div className="absolute top-0 right-0 w-2 h-2 border-t border-r border-cyan-400 opacity-60" />
            <div className="absolute bottom-0 left-0 w-2 h-2 border-b border-l border-cyan-400 opacity-60" />
            <div className="absolute bottom-0 right-0 w-2 h-2 border-b border-r border-cyan-400 opacity-60" />
          </div>
        )}
      </div>
    );

    // Wrap with label and helper text if provided
    if (label || helperText || errorText) {
      return (
        <div className="space-y-2">
          {label && (
            <label className={cn(
              'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
              effectiveVariant === 'cyberpunk' && 'text-cyan-400',
              effectiveVariant === 'nusantara' && 'text-amber-800',
              effectiveVariant === 'retro' && 'text-pink-400',
              effectiveVariant === 'carbon' && 'text-gray-300',
              effectiveVariant === 'unix' && 'text-green-400 font-mono',
              effectiveVariant === 'glassmorphism' && 'text-white'
            )}>
              {label}
            </label>
          )}
          
          {inputElement}
          
          {(helperText || errorText) && (
            <p className={cn(
              'text-sm',
              errorText ? 'text-destructive' : 'text-muted-foreground',
              effectiveVariant === 'cyberpunk' && !errorText && 'text-cyan-600',
              effectiveVariant === 'nusantara' && !errorText && 'text-amber-600',
              effectiveVariant === 'retro' && !errorText && 'text-pink-600',
              effectiveVariant === 'carbon' && !errorText && 'text-gray-400',
              effectiveVariant === 'unix' && !errorText && 'text-green-600 font-mono',
              effectiveVariant === 'glassmorphism' && !errorText && 'text-white/70'
            )}>
              {errorText || helperText}
            </p>
          )}
        </div>
      );
    }

    return inputElement;
  }
);

Input.displayName = 'Input';

// 🎯 Input Group component
export interface InputGroupProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'default' | 'lg' | 'xl';
  variant?: string;
}

export const InputGroup: React.FC<InputGroupProps> = ({
  children,
  className,
  size = 'default',
  variant
}) => {
  return (
    <div
      className={cn(
        'flex [&>*:not(:first-child)]:rounded-l-none [&>*:not(:last-child)]:rounded-r-none [&>*:not(:first-child)]:border-l-0',
        className
      )}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Input) {
          return React.cloneElement(child, {
            size: child.props.size || size,
            variant: child.props.variant || variant,
            ...child.props
          });
        }
        return child;
      })}
    </div>
  );
};

// 🎨 Helper functions
function getGlowClass(variant?: string): string {
  switch (variant) {
    case 'cyberpunk':
      return 'focus:shadow-glow-sm';
    case 'nusantara':
      return 'focus:shadow-warm-sm';
    case 'retro':
      return 'focus:shadow-neon-sm';
    case 'aurora':
      return 'focus:shadow-aurora-sm';
    case 'carbon':
      return 'focus:shadow-carbon-sm';
    case 'unix':
      return 'focus:shadow-terminal-sm';
    case 'dana':
      return 'focus:shadow-dana-sm';
    case 'glassmorphism':
      return 'focus:shadow-glass-sm';
    case 'asymmetric':
      return 'focus:shadow-asymmetric-sm';
    default:
      return '';
  }
}

export { Input, inputVariants, InputGroup };
export type { InputProps };
