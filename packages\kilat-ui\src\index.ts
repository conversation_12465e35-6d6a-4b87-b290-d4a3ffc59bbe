// 🎯 Kilat UI - Universal Component Library
// Export all components, hooks, and utilities

// 🎨 Core Components
export * from './components/Button/Button';
export * from './components/Card/Card';
export * from './components/Input/Input';

// 🎯 Layout Components
export * from './components/Container/Container';
export * from './components/Grid/Grid';
export * from './components/Stack/Stack';
export * from './components/Flex/Flex';
export * from './components/Box/Box';
export * from './components/Divider/Divider';

// 📝 Form Components
export * from './components/Form/Form';
export * from './components/Textarea/Textarea';
export * from './components/Select/Select';
export * from './components/Checkbox/Checkbox';
export * from './components/Radio/Radio';
export * from './components/Switch/Switch';
export * from './components/Slider/Slider';
export * from './components/FileUpload/FileUpload';

// 🎭 Feedback Components
export * from './components/Alert/Alert';
export * from './components/Toast/Toast';
export * from './components/Modal/Modal';
export * from './components/Dialog/Dialog';
export * from './components/Drawer/Drawer';
export * from './components/Popover/Popover';
export * from './components/Tooltip/Tooltip';
export * from './components/Loading/Loading';
export * from './components/Skeleton/Skeleton';
export * from './components/Progress/Progress';

// 🧭 Navigation Components
export * from './components/Navbar/Navbar';
export * from './components/Sidebar/Sidebar';
export * from './components/Breadcrumb/Breadcrumb';
export * from './components/Pagination/Pagination';
export * from './components/Tabs/Tabs';
export * from './components/Menu/Menu';
export * from './components/Dropdown/Dropdown';

// 📊 Data Display Components
export * from './components/Table/Table';
export * from './components/List/List';
export * from './components/Avatar/Avatar';
export * from './components/Badge/Badge';
export * from './components/Chip/Chip';
export * from './components/Tag/Tag';
export * from './components/Timeline/Timeline';
export * from './components/Accordion/Accordion';
export * from './components/Collapse/Collapse';

// 🎨 Media Components
export * from './components/Image/Image';
export * from './components/Video/Video';
export * from './components/Audio/Audio';
export * from './components/Icon/Icon';
export * from './components/Carousel/Carousel';
export * from './components/Gallery/Gallery';

// 🎯 Specialized Components
export * from './components/Calendar/Calendar';
export * from './components/DatePicker/DatePicker';
export * from './components/TimePicker/TimePicker';
export * from './components/ColorPicker/ColorPicker';
export * from './components/Rating/Rating';
export * from './components/Stepper/Stepper';
export * from './components/Tree/Tree';
export * from './components/Transfer/Transfer';

// 🎨 Theme-Specific Components
export * from './components/Cyberpunk/CyberpunkButton';
export * from './components/Cyberpunk/CyberpunkCard';
export * from './components/Cyberpunk/CyberpunkInput';
export * from './components/Cyberpunk/CyberpunkTerminal';
export * from './components/Cyberpunk/CyberpunkGlitch';
export * from './components/Cyberpunk/CyberpunkHologram';

export * from './components/Nusantara/NusantaraButton';
export * from './components/Nusantara/NusantaraCard';
export * from './components/Nusantara/NusantaraInput';
export * from './components/Nusantara/NusantaraBatik';
export * from './components/Nusantara/NusantaraPattern';

export * from './components/Retro/RetroButton';
export * from './components/Retro/RetroCard';
export * from './components/Retro/RetroInput';
export * from './components/Retro/RetroNeon';
export * from './components/Retro/RetroGrid';

// 🎭 Animation Components
export * from './components/Animation/FadeIn';
export * from './components/Animation/SlideIn';
export * from './components/Animation/ScaleIn';
export * from './components/Animation/RotateIn';
export * from './components/Animation/Parallax';
export * from './components/Animation/Morphing';
export * from './components/Animation/Particle';

// 🎯 Hooks
export * from './hooks/useTheme';
export * from './hooks/useBreakpoint';
export * from './hooks/useLocalStorage';
export * from './hooks/useSessionStorage';
export * from './hooks/useClipboard';
export * from './hooks/useToggle';
export * from './hooks/useCounter';
export * from './hooks/useDebounce';
export * from './hooks/useThrottle';
export * from './hooks/useInterval';
export * from './hooks/useTimeout';
export * from './hooks/useEventListener';
export * from './hooks/useClickOutside';
export * from './hooks/useKeyPress';
export * from './hooks/useMediaQuery';
export * from './hooks/useIntersectionObserver';
export * from './hooks/useMutation';
export * from './hooks/useQuery';
export * from './hooks/useForm';
export * from './hooks/useValidation';
export * from './hooks/useAnimation';
export * from './hooks/useGesture';
export * from './hooks/useDragDrop';

// 🎨 Providers
export * from './providers/ThemeProvider';
export * from './providers/ToastProvider';
export * from './providers/ModalProvider';
export * from './providers/AnimationProvider';
export * from './providers/KilatUIProvider';

// 🔧 Utilities
export * from './utils/cn';
export * from './utils/colors';
export * from './utils/animations';
export * from './utils/responsive';
export * from './utils/accessibility';
export * from './utils/validation';
export * from './utils/formatting';
export * from './utils/dom';
export * from './utils/performance';

// 🎨 Theme System
export * from './themes';
export * from './themes/cyberpunk';
export * from './themes/nusantara';
export * from './themes/retro';
export * from './themes/material';
export * from './themes/neumorphism';
export * from './themes/aurora';
export * from './themes/carbon';
export * from './themes/unix';
export * from './themes/dana';
export * from './themes/glassmorphism';
export * from './themes/asymmetric';

// 🎯 Types
export * from './types';
export * from './types/components';
export * from './types/themes';
export * from './types/animations';
export * from './types/responsive';

// 🎨 Constants
export * from './constants/breakpoints';
export * from './constants/colors';
export * from './constants/animations';
export * from './constants/spacing';
export * from './constants/typography';

// 🎯 Default Theme Configuration
export const defaultKilatUIConfig = {
  theme: 'cyberpunk',
  mode: 'dark',
  animations: true,
  reducedMotion: false,
  highContrast: false,
  customProperties: true,
  prefix: 'k-',
  components: {
    button: {
      defaultVariant: 'default',
      defaultSize: 'default'
    },
    card: {
      defaultVariant: 'default',
      defaultSize: 'default'
    },
    input: {
      defaultVariant: 'default',
      defaultSize: 'default'
    }
  },
  breakpoints: {
    xs: '0px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    '2xl': '1400px'
  },
  colors: {
    primary: '#00ffff',
    secondary: '#ff0080',
    accent: '#00ff41',
    background: '#0a0a0a',
    foreground: '#ffffff'
  },
  animations: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms'
    },
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
};

// 🎯 Version
export const KILAT_UI_VERSION = '1.0.0';

// 🎯 Main export
export default {
  version: KILAT_UI_VERSION,
  config: defaultKilatUIConfig
};
