{"name": "kilat-utils", "version": "1.0.0", "description": "⚡ Kilat.js Utilities - Hooks, helpers, and platform detection", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./hooks": {"import": "./dist/hooks.esm.js", "require": "./dist/hooks.js", "types": "./dist/hooks.d.ts"}, "./platform": {"import": "./dist/platform.esm.js", "require": "./dist/platform.js", "types": "./dist/platform.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "bun run build:esm && bun run build:cjs && bun run build:types", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.esm.js --external:react --external:react-dom", "build:cjs": "esbuild src/index.ts --bundle --format=cjs --outfile=dist/index.js --external:react --external:react-dom", "build:types": "tsc --emitDeclarationOnly --outDir dist", "dev": "bun run build --watch", "test": "bun test", "clean": "rm -rf dist"}, "keywords": ["kilat", "utils", "hooks", "platform", "helpers", "react"], "author": "KangPCode", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"ua-parser-js": "^1.0.37"}, "devDependencies": {"esbuild": "^0.19.8", "typescript": "^5.3.0", "@types/react": "^18.2.0", "@types/ua-parser-js": "^0.7.39"}}