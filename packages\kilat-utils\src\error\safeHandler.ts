import { createLogger } from '../logger';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface SafeHandlerConfig {
  enableLogging?: boolean;
  enableReporting?: boolean;
  reportingEndpoint?: string;
  fallbackValue?: any;
  retryAttempts?: number;
  retryDelay?: number;
  onError?: (error: Error, context?: ErrorContext) => void;
  onRetry?: (attempt: number, error: Error) => void;
  onFallback?: (error: Error, fallbackValue: any) => void;
}

export interface ErrorReport {
  id: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: ErrorContext;
  severity: ErrorSeverity;
  timestamp: Date;
  userAgent?: string;
  url?: string;
  resolved: boolean;
}

/**
 * 🛡️ SafeHandler - Comprehensive error handling utility
 * Provides safe execution, retry logic, and error reporting
 */
export class SafeHandler {
  private config: SafeHandlerConfig;
  private logger = createLogger({ prefix: 'SafeHandler' });
  private errorReports: ErrorReport[] = [];
  private maxReports = 1000;

  constructor(config: SafeHandlerConfig = {}) {
    this.config = {
      enableLogging: true,
      enableReporting: false,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }

  // 🔒 Safe execution wrapper
  async safe<T>(
    fn: () => Promise<T> | T,
    fallbackValue?: T,
    context?: ErrorContext
  ): Promise<T | undefined> {
    try {
      const result = await fn();
      return result;
    } catch (error) {
      return this.handleError(error as Error, fallbackValue, context);
    }
  }

  // 🔄 Safe execution with retry
  async safeWithRetry<T>(
    fn: () => Promise<T> | T,
    options: {
      attempts?: number;
      delay?: number;
      fallbackValue?: T;
      context?: ErrorContext;
      shouldRetry?: (error: Error, attempt: number) => boolean;
    } = {}
  ): Promise<T | undefined> {
    const {
      attempts = this.config.retryAttempts || 3,
      delay = this.config.retryDelay || 1000,
      fallbackValue,
      context,
      shouldRetry = () => true
    } = options;

    let lastError: Error;

    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        const result = await fn();
        return result;
      } catch (error) {
        lastError = error as Error;

        if (attempt === attempts || !shouldRetry(lastError, attempt)) {
          break;
        }

        if (this.config.onRetry) {
          this.config.onRetry(attempt, lastError);
        }

        if (this.config.enableLogging) {
          this.logger.warn(`Retry attempt ${attempt}/${attempts}`, {
            error: lastError.message,
            context
          });
        }

        // Wait before retry
        await this.delay(delay * attempt); // Exponential backoff
      }
    }

    return this.handleError(lastError!, fallbackValue, context);
  }

  // 🎯 Async safe wrapper
  safeAsync<T>(
    fn: () => Promise<T>,
    fallbackValue?: T,
    context?: ErrorContext
  ): Promise<T | undefined> {
    return this.safe(fn, fallbackValue, context);
  }

  // 🔄 Sync safe wrapper
  safeSync<T>(
    fn: () => T,
    fallbackValue?: T,
    context?: ErrorContext
  ): T | undefined {
    try {
      return fn();
    } catch (error) {
      return this.handleError(error as Error, fallbackValue, context);
    }
  }

  // 🎭 Promise wrapper
  safePromise<T>(
    promise: Promise<T>,
    fallbackValue?: T,
    context?: ErrorContext
  ): Promise<T | undefined> {
    return promise.catch(error => 
      this.handleError(error as Error, fallbackValue, context)
    );
  }

  // 🔥 Event listener wrapper
  safeEventListener<T extends Event>(
    element: EventTarget,
    event: string,
    handler: (event: T) => void,
    options?: AddEventListenerOptions
  ): () => void {
    const safeHandler = (event: T) => {
      this.safeSync(
        () => handler(event),
        undefined,
        {
          component: 'EventListener',
          action: event.type,
          metadata: { eventType: event.type }
        }
      );
    };

    element.addEventListener(event, safeHandler as EventListener, options);

    // Return cleanup function
    return () => {
      element.removeEventListener(event, safeHandler as EventListener, options);
    };
  }

  // 🎯 React component wrapper
  safeComponent<P extends object>(
    Component: React.ComponentType<P>,
    fallbackComponent?: React.ComponentType<{ error: Error }> | React.ReactElement
  ): React.ComponentType<P> {
    return (props: P) => {
      try {
        return React.createElement(Component, props);
      } catch (error) {
        this.handleError(error as Error, undefined, {
          component: Component.name || 'UnknownComponent',
          action: 'render'
        });

        if (fallbackComponent) {
          if (React.isValidElement(fallbackComponent)) {
            return fallbackComponent;
          } else {
            return React.createElement(fallbackComponent as React.ComponentType<any>, { error });
          }
        }

        return React.createElement('div', {
          style: { color: 'red', padding: '1rem', border: '1px solid red' }
        }, 'Component Error: ' + (error as Error).message);
      }
    };
  }

  // 🔧 API call wrapper
  async safeApiCall<T>(
    apiCall: () => Promise<T>,
    options: {
      retries?: number;
      timeout?: number;
      fallbackValue?: T;
      context?: ErrorContext;
    } = {}
  ): Promise<T | undefined> {
    const { retries = 3, timeout = 10000, fallbackValue, context } = options;

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('API call timeout')), timeout);
    });

    const apiCallWithTimeout = () => Promise.race([apiCall(), timeoutPromise]);

    return this.safeWithRetry(
      apiCallWithTimeout,
      {
        attempts: retries,
        fallbackValue,
        context: {
          ...context,
          action: 'api-call'
        },
        shouldRetry: (error, attempt) => {
          // Don't retry on client errors (4xx)
          if (error.message.includes('4')) {
            return false;
          }
          return attempt < retries;
        }
      }
    );
  }

  // 🎯 Local storage wrapper
  safeLocalStorage = {
    getItem: (key: string, fallback?: string): string | undefined => {
      return this.safeSync(
        () => {
          if (typeof localStorage === 'undefined') {
            throw new Error('localStorage not available');
          }
          return localStorage.getItem(key) || fallback;
        },
        fallback,
        { action: 'localStorage.getItem', metadata: { key } }
      );
    },

    setItem: (key: string, value: string): boolean => {
      return this.safeSync(
        () => {
          if (typeof localStorage === 'undefined') {
            throw new Error('localStorage not available');
          }
          localStorage.setItem(key, value);
          return true;
        },
        false,
        { action: 'localStorage.setItem', metadata: { key } }
      ) || false;
    },

    removeItem: (key: string): boolean => {
      return this.safeSync(
        () => {
          if (typeof localStorage === 'undefined') {
            throw new Error('localStorage not available');
          }
          localStorage.removeItem(key);
          return true;
        },
        false,
        { action: 'localStorage.removeItem', metadata: { key } }
      ) || false;
    },

    getJSON: <T>(key: string, fallback?: T): T | undefined => {
      return this.safeSync(
        () => {
          const item = this.safeLocalStorage.getItem(key);
          return item ? JSON.parse(item) : fallback;
        },
        fallback,
        { action: 'localStorage.getJSON', metadata: { key } }
      );
    },

    setJSON: <T>(key: string, value: T): boolean => {
      return this.safeSync(
        () => {
          const serialized = JSON.stringify(value);
          return this.safeLocalStorage.setItem(key, serialized);
        },
        false,
        { action: 'localStorage.setJSON', metadata: { key } }
      ) || false;
    }
  };

  // 🔧 Private methods
  private async handleError<T>(
    error: Error,
    fallbackValue?: T,
    context?: ErrorContext
  ): Promise<T | undefined> {
    const errorReport = this.createErrorReport(error, context);

    // Log error
    if (this.config.enableLogging) {
      this.logger.error('Safe handler caught error', error, {
        context,
        errorId: errorReport.id
      });
    }

    // Call custom error handler
    if (this.config.onError) {
      this.config.onError(error, context);
    }

    // Report error
    if (this.config.enableReporting) {
      await this.reportError(errorReport);
    }

    // Call fallback handler
    if (fallbackValue !== undefined && this.config.onFallback) {
      this.config.onFallback(error, fallbackValue);
    }

    return fallbackValue;
  }

  private createErrorReport(error: Error, context?: ErrorContext): ErrorReport {
    const report: ErrorReport = {
      id: this.generateId(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context: {
        timestamp: new Date(),
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        ...context
      },
      severity: this.determineSeverity(error),
      timestamp: new Date(),
      resolved: false
    };

    // Add to reports
    this.errorReports.push(report);

    // Keep reports manageable
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(-this.maxReports);
    }

    return report;
  }

  private determineSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'medium';
    }
    
    if (message.includes('syntax') || message.includes('reference')) {
      return 'high';
    }
    
    if (message.includes('security') || message.includes('permission')) {
      return 'critical';
    }
    
    return 'low';
  }

  private async reportError(report: ErrorReport): Promise<void> {
    if (!this.config.reportingEndpoint) {
      return;
    }

    try {
      await fetch(this.config.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
    } catch (reportingError) {
      if (this.config.enableLogging) {
        this.logger.warn('Failed to report error', reportingError);
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 📊 Public utility methods
  getErrorReports(): ErrorReport[] {
    return [...this.errorReports];
  }

  clearErrorReports(): void {
    this.errorReports = [];
  }

  getErrorStats(): {
    total: number;
    bySeverity: Record<ErrorSeverity, number>;
    byComponent: Record<string, number>;
    resolved: number;
    unresolved: number;
  } {
    const bySeverity: Record<ErrorSeverity, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    const byComponent: Record<string, number> = {};
    let resolved = 0;

    this.errorReports.forEach(report => {
      bySeverity[report.severity]++;
      
      const component = report.context.component || 'unknown';
      byComponent[component] = (byComponent[component] || 0) + 1;
      
      if (report.resolved) {
        resolved++;
      }
    });

    return {
      total: this.errorReports.length,
      bySeverity,
      byComponent,
      resolved,
      unresolved: this.errorReports.length - resolved
    };
  }

  markErrorResolved(errorId: string): void {
    const report = this.errorReports.find(r => r.id === errorId);
    if (report) {
      report.resolved = true;
    }
  }
}

// 🏭 Factory function
export function createSafeHandler(config: SafeHandlerConfig = {}): SafeHandler {
  return new SafeHandler(config);
}

// 🌍 Global safe handler instance
export const safeHandler = createSafeHandler();

// 🎯 Convenience exports
export const safe = safeHandler.safe.bind(safeHandler);
export const safeAsync = safeHandler.safeAsync.bind(safeHandler);
export const safeSync = safeHandler.safeSync.bind(safeHandler);
export const safePromise = safeHandler.safePromise.bind(safeHandler);
export const safeWithRetry = safeHandler.safeWithRetry.bind(safeHandler);

export default {
  SafeHandler,
  createSafeHandler,
  safeHandler,
  safe,
  safeAsync,
  safeSync,
  safePromise,
  safeWithRetry
};
