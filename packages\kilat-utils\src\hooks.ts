import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import type { 
  UseToggleReturn, 
  UseCounterReturn, 
  UseLocalStorageReturn,
  SafeHandlerOptions,
  MediaQueryOptions,
  KeyboardShortcutOptions,
  KeyboardEventHandler,
  DebounceOptions,
  ThrottleOptions
} from './types';

// 🎨 Theme Hook (re-export from core for convenience)
export { useTheme } from 'kilat-core';

// 🔄 Toggle Hook
export function useToggle(initialValue = false): UseToggleReturn {
  const [value, setValue] = useState(initialValue);
  
  const toggle = useCallback(() => setValue(prev => !prev), []);
  const setTrue = useCallback(() => setValue(true), []);
  const setFalse = useCallback(() => setValue(false), []);
  
  return { value, toggle, setTrue, setFalse };
}

// 🔢 Counter Hook
export function useCounter(initialValue = 0): UseCounterReturn {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(prev => prev + 1), []);
  const decrement = useCallback(() => setCount(prev => prev - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  const set = useCallback((value: number) => setCount(value), []);
  
  return { count, increment, decrement, reset, set };
}

// 💾 Local Storage Hook
export function useLocalStorage<T>(
  key: string, 
  initialValue: T
): UseLocalStorageReturn<T> {
  const [value, setValue] = useState<T>(() => {
    if (typeof window === 'undefined') return initialValue;
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch {
      return initialValue;
    }
  });

  const setStoredValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(value) : value;
      setValue(valueToStore);
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key]);

  const removeValue = useCallback(() => {
    try {
      setValue(initialValue);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return { value, setValue: setStoredValue, removeValue };
}

// 💾 Session Storage Hook
export function useSessionStorage<T>(
  key: string, 
  initialValue: T
): UseLocalStorageReturn<T> {
  const [value, setValue] = useState<T>(() => {
    if (typeof window === 'undefined') return initialValue;
    
    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch {
      return initialValue;
    }
  });

  const setStoredValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(value) : value;
      setValue(valueToStore);
      
      if (typeof window !== 'undefined') {
        window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting sessionStorage key "${key}":`, error);
    }
  }, [key]);

  const removeValue = useCallback(() => {
    try {
      setValue(initialValue);
      if (typeof window !== 'undefined') {
        window.sessionStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return { value, setValue: setStoredValue, removeValue };
}

// 📱 Media Query Hook
export function useMediaQuery(
  query: string, 
  options: MediaQueryOptions = {}
): boolean {
  const { defaultValue = false, initializeWithValue = true } = options;
  
  const [matches, setMatches] = useState(() => {
    if (initializeWithValue && typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return defaultValue;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handleChange = () => setMatches(mediaQuery.matches);
    
    mediaQuery.addEventListener('change', handleChange);
    setMatches(mediaQuery.matches);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches;
}

// ⌨️ Keyboard Shortcut Hook
export function useKeyboardShortcut(
  keys: string | string[],
  callback: KeyboardEventHandler,
  options: KeyboardShortcutOptions = {}
): void {
  const { preventDefault = true, stopPropagation = true, enabled = true } = options;
  const keysArray = Array.isArray(keys) ? keys : [keys];

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const pressedKeys = [];
      
      if (event.ctrlKey) pressedKeys.push('ctrl');
      if (event.metaKey) pressedKeys.push('meta');
      if (event.shiftKey) pressedKeys.push('shift');
      if (event.altKey) pressedKeys.push('alt');
      
      pressedKeys.push(event.key.toLowerCase());
      
      const pressedKeysString = pressedKeys.join('+');
      
      if (keysArray.some(keyCombo => keyCombo.toLowerCase() === pressedKeysString)) {
        if (preventDefault) event.preventDefault();
        if (stopPropagation) event.stopPropagation();
        callback(event);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [keysArray, callback, preventDefault, stopPropagation, enabled]);
}

// ⏱️ Debounce Hook
export function useDebounce<T>(
  value: T, 
  delay: number, 
  options: DebounceOptions = {}
): T {
  const { leading = false, trailing = true } = options;
  const [debouncedValue, setDebouncedValue] = useState(value);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const leadingRef = useRef(true);

  useEffect(() => {
    if (leading && leadingRef.current) {
      setDebouncedValue(value);
      leadingRef.current = false;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (trailing) {
        setDebouncedValue(value);
      }
      leadingRef.current = true;
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, delay, leading, trailing]);

  return debouncedValue;
}

// 🚀 Throttle Hook
export function useThrottle<T>(
  value: T, 
  limit: number, 
  options: ThrottleOptions = {}
): T {
  const { leading = true, trailing = true } = options;
  const [throttledValue, setThrottledValue] = useState(value);
  const lastRan = useRef(Date.now());
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (leading && Date.now() - lastRan.current >= limit) {
      setThrottledValue(value);
      lastRan.current = Date.now();
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        if (trailing && Date.now() - lastRan.current >= limit) {
          setThrottledValue(value);
          lastRan.current = Date.now();
        }
      }, limit - (Date.now() - lastRan.current));
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, limit, leading, trailing]);

  return throttledValue;
}

// 🧠 Memo Compare Hook
export function useMemoCompare<T>(
  next: T,
  compare: (previous: T | undefined, next: T) => boolean
): T {
  const previousRef = useRef<T>();
  const previous = previousRef.current;

  const isEqual = compare(previous, next);

  useEffect(() => {
    if (!isEqual) {
      previousRef.current = next;
    }
  });

  return isEqual ? previous! : next;
}

// 📝 Previous Value Hook
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();

  useEffect(() => {
    ref.current = value;
  });

  return ref.current;
}

// 🚀 Mount Hook
export function useMount(fn: () => void): void {
  useEffect(() => {
    fn();
  }, []);
}

// 🔚 Unmount Hook
export function useUnmount(fn: () => void): void {
  const fnRef = useRef(fn);
  fnRef.current = fn;

  useEffect(() => {
    return () => fnRef.current();
  }, []);
}

// 🔄 Update Effect Hook (skip first render)
export function useUpdateEffect(effect: () => void | (() => void), deps?: React.DependencyList): void {
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    return effect();
  }, deps);
}

// 🛡️ Safe Handler Hook
export function useSafeHandler<T extends (...args: any[]) => any>(
  handler: T,
  options: SafeHandlerOptions = {}
): T {
  const { fallback, onError, silent = false } = options;

  return useCallback((...args: Parameters<T>) => {
    try {
      return handler(...args);
    } catch (error) {
      if (!silent) {
        console.error('Safe handler error:', error);
      }

      if (onError) {
        onError(error as Error);
      }

      return fallback;
    }
  }, [handler, fallback, onError, silent]) as T;
}

// 🚨 Error Boundary Hook
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const captureError = useCallback((error: Error) => {
    setError(error);
  }, []);

  useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
}
