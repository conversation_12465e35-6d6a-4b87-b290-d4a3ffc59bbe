import { useState, useEffect } from 'react';

export type Platform = 'web' | 'mobile' | 'desktop' | 'server';
export type OS = 'windows' | 'macos' | 'linux' | 'ios' | 'android' | 'unknown';
export type Browser = 'chrome' | 'firefox' | 'safari' | 'edge' | 'opera' | 'unknown';

export interface PlatformInfo {
  platform: Platform;
  os: OS;
  browser: Browser;
  isMobile: boolean;
  isDesktop: boolean;
  isWeb: boolean;
  isServer: boolean;
  isTouchDevice: boolean;
  isOnline: boolean;
  userAgent: string;
  screenSize: {
    width: number;
    height: number;
  };
  viewport: {
    width: number;
    height: number;
  };
  devicePixelRatio: number;
  colorScheme: 'light' | 'dark' | 'auto';
  reducedMotion: boolean;
  highContrast: boolean;
}

/**
 * 🎯 usePlatform Hook
 * Detects platform, OS, browser, and device capabilities
 */
export function usePlatform(): PlatformInfo {
  const [platformInfo, setPlatformInfo] = useState<PlatformInfo>(() => 
    detectPlatform()
  );

  useEffect(() => {
    // Update platform info on window resize
    const handleResize = () => {
      setPlatformInfo(prev => ({
        ...prev,
        screenSize: {
          width: window.screen.width,
          height: window.screen.height
        },
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      }));
    };

    // Update online status
    const handleOnline = () => {
      setPlatformInfo(prev => ({ ...prev, isOnline: true }));
    };

    const handleOffline = () => {
      setPlatformInfo(prev => ({ ...prev, isOnline: false }));
    };

    // Update color scheme
    const handleColorSchemeChange = (e: MediaQueryListEvent) => {
      setPlatformInfo(prev => ({
        ...prev,
        colorScheme: e.matches ? 'dark' : 'light'
      }));
    };

    // Update reduced motion preference
    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setPlatformInfo(prev => ({
        ...prev,
        reducedMotion: e.matches
      }));
    };

    // Update high contrast preference
    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setPlatformInfo(prev => ({
        ...prev,
        highContrast: e.matches
      }));
    };

    if (typeof window !== 'undefined') {
      // Add event listeners
      window.addEventListener('resize', handleResize);
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Media query listeners
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

      darkModeQuery.addEventListener('change', handleColorSchemeChange);
      reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
      highContrastQuery.addEventListener('change', handleHighContrastChange);

      // Cleanup
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        
        darkModeQuery.removeEventListener('change', handleColorSchemeChange);
        reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
        highContrastQuery.removeEventListener('change', handleHighContrastChange);
      };
    }
  }, []);

  return platformInfo;
}

// 🔍 Platform Detection Functions
function detectPlatform(): PlatformInfo {
  if (typeof window === 'undefined') {
    return getServerPlatformInfo();
  }

  const userAgent = navigator.userAgent.toLowerCase();
  const platform = detectPlatformType(userAgent);
  const os = detectOS(userAgent);
  const browser = detectBrowser(userAgent);
  
  return {
    platform,
    os,
    browser,
    isMobile: platform === 'mobile',
    isDesktop: platform === 'desktop',
    isWeb: platform === 'web',
    isServer: platform === 'server',
    isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    isOnline: navigator.onLine,
    userAgent: navigator.userAgent,
    screenSize: {
      width: window.screen.width,
      height: window.screen.height
    },
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    devicePixelRatio: window.devicePixelRatio || 1,
    colorScheme: getColorScheme(),
    reducedMotion: getReducedMotion(),
    highContrast: getHighContrast()
  };
}

function detectPlatformType(userAgent: string): Platform {
  // Check for Electron
  if (userAgent.includes('electron')) {
    return 'desktop';
  }

  // Check for mobile devices
  if (/android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
    return 'mobile';
  }

  // Default to web
  return 'web';
}

function detectOS(userAgent: string): OS {
  if (/windows/i.test(userAgent)) return 'windows';
  if (/macintosh|mac os x/i.test(userAgent)) return 'macos';
  if (/linux/i.test(userAgent)) return 'linux';
  if (/iphone|ipad|ipod/i.test(userAgent)) return 'ios';
  if (/android/i.test(userAgent)) return 'android';
  return 'unknown';
}

function detectBrowser(userAgent: string): Browser {
  if (/chrome/i.test(userAgent) && !/edge|edg/i.test(userAgent)) return 'chrome';
  if (/firefox/i.test(userAgent)) return 'firefox';
  if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) return 'safari';
  if (/edge|edg/i.test(userAgent)) return 'edge';
  if (/opera|opr/i.test(userAgent)) return 'opera';
  return 'unknown';
}

function getColorScheme(): 'light' | 'dark' | 'auto' {
  if (typeof window === 'undefined') return 'auto';
  
  if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  } else if (window.matchMedia('(prefers-color-scheme: light)').matches) {
    return 'light';
  }
  
  return 'auto';
}

function getReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

function getHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
}

function getServerPlatformInfo(): PlatformInfo {
  return {
    platform: 'server',
    os: 'unknown',
    browser: 'unknown',
    isMobile: false,
    isDesktop: false,
    isWeb: false,
    isServer: true,
    isTouchDevice: false,
    isOnline: true,
    userAgent: '',
    screenSize: { width: 0, height: 0 },
    viewport: { width: 0, height: 0 },
    devicePixelRatio: 1,
    colorScheme: 'auto',
    reducedMotion: false,
    highContrast: false
  };
}

// 🎯 Platform-specific utilities
export const platformUtils = {
  // Check if running in specific environments
  isElectron: () => {
    return typeof window !== 'undefined' && 
           window.navigator.userAgent.includes('Electron');
  },

  isReactNative: () => {
    return typeof navigator !== 'undefined' && 
           navigator.product === 'ReactNative';
  },

  isWebView: () => {
    if (typeof window === 'undefined') return false;
    
    const userAgent = window.navigator.userAgent;
    return /wv|webview/i.test(userAgent);
  },

  isPWA: () => {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  },

  // Device capabilities
  supportsWebGL: () => {
    if (typeof window === 'undefined') return false;
    
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  },

  supportsWebGL2: () => {
    if (typeof window === 'undefined') return false;
    
    try {
      const canvas = document.createElement('canvas');
      return !!canvas.getContext('webgl2');
    } catch {
      return false;
    }
  },

  supportsWebAssembly: () => {
    return typeof WebAssembly === 'object';
  },

  supportsServiceWorker: () => {
    return typeof window !== 'undefined' && 'serviceWorker' in navigator;
  },

  supportsWebWorker: () => {
    return typeof Worker !== 'undefined';
  },

  supportsLocalStorage: () => {
    try {
      if (typeof window === 'undefined') return false;
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  },

  supportsIndexedDB: () => {
    return typeof window !== 'undefined' && 'indexedDB' in window;
  },

  // Performance utilities
  getMemoryInfo: () => {
    if (typeof window === 'undefined') return null;
    
    const memory = (performance as any).memory;
    if (!memory) return null;
    
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    };
  },

  getConnectionInfo: () => {
    if (typeof window === 'undefined') return null;
    
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;
    
    if (!connection) return null;
    
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }
};

// 🎨 Responsive utilities
export const responsive = {
  // Breakpoints (matching common CSS frameworks)
  breakpoints: {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1400
  },

  // Get current breakpoint
  getCurrentBreakpoint: (width?: number): string => {
    const w = width || (typeof window !== 'undefined' ? window.innerWidth : 0);
    const { breakpoints } = responsive;
    
    if (w >= breakpoints.xxl) return 'xxl';
    if (w >= breakpoints.xl) return 'xl';
    if (w >= breakpoints.lg) return 'lg';
    if (w >= breakpoints.md) return 'md';
    if (w >= breakpoints.sm) return 'sm';
    return 'xs';
  },

  // Check if screen is at least a certain size
  isAtLeast: (breakpoint: string, width?: number): boolean => {
    const w = width || (typeof window !== 'undefined' ? window.innerWidth : 0);
    const minWidth = responsive.breakpoints[breakpoint as keyof typeof responsive.breakpoints];
    return w >= minWidth;
  },

  // Check if screen is at most a certain size
  isAtMost: (breakpoint: string, width?: number): boolean => {
    const w = width || (typeof window !== 'undefined' ? window.innerWidth : 0);
    const maxWidth = responsive.breakpoints[breakpoint as keyof typeof responsive.breakpoints];
    return w <= maxWidth;
  }
};
