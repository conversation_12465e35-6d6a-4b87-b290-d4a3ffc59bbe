import { useState, useEffect, useCallback, useContext, createContext } from 'react';

export type KilatTheme = 
  | 'cyberpunk' 
  | 'nusantara' 
  | 'retro' 
  | 'material' 
  | 'neumorphism' 
  | 'aurora' 
  | 'carbon' 
  | 'unix' 
  | 'dana'
  | 'glassmorphism'
  | 'asymmetric';

export type ColorMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  theme: KilatTheme;
  mode: ColorMode;
  primaryColor?: string;
  accentColor?: string;
  customCSS?: string;
  animations?: boolean;
  reducedMotion?: boolean;
  highContrast?: boolean;
}

export interface ThemeContextValue {
  config: ThemeConfig;
  setTheme: (theme: KilatTheme) => void;
  setMode: (mode: ColorMode) => void;
  setPrimaryColor: (color: string) => void;
  setAccentColor: (color: string) => void;
  toggleMode: () => void;
  resetTheme: () => void;
  applyTheme: (config: Partial<ThemeConfig>) => void;
  isThemeLoaded: boolean;
  availableThemes: KilatTheme[];
}

// 🎨 Theme Context
const ThemeContext = createContext<ThemeContextValue | null>(null);

// 🎯 Default theme configuration
const defaultThemeConfig: ThemeConfig = {
  theme: 'cyberpunk',
  mode: 'auto',
  animations: true,
  reducedMotion: false,
  highContrast: false
};

// 📱 Available themes with metadata
export const themeMetadata: Record<KilatTheme, {
  name: string;
  description: string;
  primaryColor: string;
  accentColor: string;
  category: 'futuristic' | 'cultural' | 'classic' | 'modern';
  supportsDarkMode: boolean;
}> = {
  cyberpunk: {
    name: 'Cyberpunk',
    description: 'Futuristic neon-lit interface with glowing effects',
    primaryColor: '#00ffff',
    accentColor: '#ff0080',
    category: 'futuristic',
    supportsDarkMode: true
  },
  nusantara: {
    name: 'Nusantara',
    description: 'Indonesian cultural theme with batik patterns',
    primaryColor: '#8B4513',
    accentColor: '#DAA520',
    category: 'cultural',
    supportsDarkMode: true
  },
  retro: {
    name: 'Retro',
    description: '80s/90s synthwave and arcade aesthetics',
    primaryColor: '#ff0080',
    accentColor: '#00ffff',
    category: 'classic',
    supportsDarkMode: true
  },
  material: {
    name: 'Material',
    description: 'Google Material Design principles',
    primaryColor: '#1976d2',
    accentColor: '#dc004e',
    category: 'modern',
    supportsDarkMode: true
  },
  neumorphism: {
    name: 'Neumorphism',
    description: 'Soft UI with subtle shadows and depth',
    primaryColor: '#667eea',
    accentColor: '#764ba2',
    category: 'modern',
    supportsDarkMode: true
  },
  aurora: {
    name: 'Aurora',
    description: 'Northern lights inspired flowing gradients',
    primaryColor: '#00d4aa',
    accentColor: '#5b73ff',
    category: 'futuristic',
    supportsDarkMode: true
  },
  carbon: {
    name: 'Carbon',
    description: 'IBM Carbon Design System dark theme',
    primaryColor: '#0f62fe',
    accentColor: '#ee5396',
    category: 'modern',
    supportsDarkMode: true
  },
  unix: {
    name: 'Unix',
    description: 'Terminal and command-line inspired monospace',
    primaryColor: '#00ff00',
    accentColor: '#ffff00',
    category: 'classic',
    supportsDarkMode: true
  },
  dana: {
    name: 'Dana',
    description: 'Indonesian fintech app inspired clean design',
    primaryColor: '#118EEA',
    accentColor: '#FF6B35',
    category: 'modern',
    supportsDarkMode: true
  },
  glassmorphism: {
    name: 'Glassmorphism',
    description: 'Frosted glass effect with transparency',
    primaryColor: '#667eea',
    accentColor: '#764ba2',
    category: 'modern',
    supportsDarkMode: true
  },
  asymmetric: {
    name: 'Asymmetric',
    description: 'Bold asymmetrical layouts and typography',
    primaryColor: '#ff6b6b',
    accentColor: '#4ecdc4',
    category: 'modern',
    supportsDarkMode: true
  }
};

/**
 * 🎨 useTheme Hook
 * Manages theme state and provides theme utilities
 */
export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
}

/**
 * 🎨 ThemeProvider Component
 * Provides theme context to the application
 */
export function ThemeProvider({ 
  children, 
  initialTheme = defaultThemeConfig 
}: { 
  children: React.ReactNode;
  initialTheme?: Partial<ThemeConfig>;
}) {
  const [config, setConfig] = useState<ThemeConfig>(() => {
    // Load theme from localStorage if available
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('kilat-theme');
        if (saved) {
          return { ...defaultThemeConfig, ...JSON.parse(saved), ...initialTheme };
        }
      } catch (error) {
        console.warn('Failed to load theme from localStorage:', error);
      }
    }
    
    return { ...defaultThemeConfig, ...initialTheme };
  });

  const [isThemeLoaded, setIsThemeLoaded] = useState(false);

  // 💾 Save theme to localStorage
  const saveTheme = useCallback((newConfig: ThemeConfig) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('kilat-theme', JSON.stringify(newConfig));
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
  }, []);

  // 🎯 Apply theme to DOM
  const applyThemeToDOM = useCallback((themeConfig: ThemeConfig) => {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    
    // Set theme attribute
    root.setAttribute('data-kilat-theme', themeConfig.theme);
    
    // Set mode attribute
    const effectiveMode = themeConfig.mode === 'auto' 
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : themeConfig.mode;
    
    root.setAttribute('data-kilat-mode', effectiveMode);
    
    // Set custom properties if provided
    if (themeConfig.primaryColor) {
      root.style.setProperty('--k-primary-custom', themeConfig.primaryColor);
    }
    
    if (themeConfig.accentColor) {
      root.style.setProperty('--k-accent-custom', themeConfig.accentColor);
    }
    
    // Handle animations
    if (themeConfig.animations === false || themeConfig.reducedMotion) {
      root.style.setProperty('--k-animation-duration', '0s');
      root.style.setProperty('--k-transition-duration', '0s');
    } else {
      root.style.removeProperty('--k-animation-duration');
      root.style.removeProperty('--k-transition-duration');
    }
    
    // Handle high contrast
    if (themeConfig.highContrast) {
      root.setAttribute('data-kilat-high-contrast', 'true');
    } else {
      root.removeAttribute('data-kilat-high-contrast');
    }
    
    // Inject custom CSS if provided
    if (themeConfig.customCSS) {
      let customStyleElement = document.getElementById('kilat-custom-theme');
      
      if (!customStyleElement) {
        customStyleElement = document.createElement('style');
        customStyleElement.id = 'kilat-custom-theme';
        document.head.appendChild(customStyleElement);
      }
      
      customStyleElement.textContent = themeConfig.customCSS;
    }
  }, []);

  // 🎨 Theme management functions
  const setTheme = useCallback((theme: KilatTheme) => {
    const newConfig = { ...config, theme };
    setConfig(newConfig);
    saveTheme(newConfig);
    applyThemeToDOM(newConfig);
  }, [config, saveTheme, applyThemeToDOM]);

  const setMode = useCallback((mode: ColorMode) => {
    const newConfig = { ...config, mode };
    setConfig(newConfig);
    saveTheme(newConfig);
    applyThemeToDOM(newConfig);
  }, [config, saveTheme, applyThemeToDOM]);

  const setPrimaryColor = useCallback((primaryColor: string) => {
    const newConfig = { ...config, primaryColor };
    setConfig(newConfig);
    saveTheme(newConfig);
    applyThemeToDOM(newConfig);
  }, [config, saveTheme, applyThemeToDOM]);

  const setAccentColor = useCallback((accentColor: string) => {
    const newConfig = { ...config, accentColor };
    setConfig(newConfig);
    saveTheme(newConfig);
    applyThemeToDOM(newConfig);
  }, [config, saveTheme, applyThemeToDOM]);

  const toggleMode = useCallback(() => {
    const newMode: ColorMode = config.mode === 'light' ? 'dark' : 
                              config.mode === 'dark' ? 'auto' : 'light';
    setMode(newMode);
  }, [config.mode, setMode]);

  const resetTheme = useCallback(() => {
    setConfig(defaultThemeConfig);
    saveTheme(defaultThemeConfig);
    applyThemeToDOM(defaultThemeConfig);
  }, [saveTheme, applyThemeToDOM]);

  const applyTheme = useCallback((partialConfig: Partial<ThemeConfig>) => {
    const newConfig = { ...config, ...partialConfig };
    setConfig(newConfig);
    saveTheme(newConfig);
    applyThemeToDOM(newConfig);
  }, [config, saveTheme, applyThemeToDOM]);

  // 🎧 Listen for system color scheme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (config.mode === 'auto') {
        applyThemeToDOM(config);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [config, applyThemeToDOM]);

  // 🎧 Listen for reduced motion preference
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      const newConfig = { ...config, reducedMotion: e.matches };
      setConfig(newConfig);
      applyThemeToDOM(newConfig);
    };

    mediaQuery.addEventListener('change', handleChange);
    
    // Set initial value
    if (mediaQuery.matches && !config.reducedMotion) {
      handleChange({ matches: true } as MediaQueryListEvent);
    }
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [config, applyThemeToDOM]);

  // 🎧 Listen for high contrast preference
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      const newConfig = { ...config, highContrast: e.matches };
      setConfig(newConfig);
      applyThemeToDOM(newConfig);
    };

    mediaQuery.addEventListener('change', handleChange);
    
    // Set initial value
    if (mediaQuery.matches && !config.highContrast) {
      handleChange({ matches: true } as MediaQueryListEvent);
    }
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [config, applyThemeToDOM]);

  // 🚀 Initialize theme on mount
  useEffect(() => {
    applyThemeToDOM(config);
    setIsThemeLoaded(true);
  }, [config, applyThemeToDOM]);

  const contextValue: ThemeContextValue = {
    config,
    setTheme,
    setMode,
    setPrimaryColor,
    setAccentColor,
    toggleMode,
    resetTheme,
    applyTheme,
    isThemeLoaded,
    availableThemes: Object.keys(themeMetadata) as KilatTheme[]
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// 🎨 Theme utilities
export const themeUtils = {
  // Get theme metadata
  getThemeMetadata: (theme: KilatTheme) => themeMetadata[theme],
  
  // Get themes by category
  getThemesByCategory: (category: string) => {
    return Object.entries(themeMetadata)
      .filter(([_, meta]) => meta.category === category)
      .map(([theme]) => theme as KilatTheme);
  },
  
  // Check if theme supports dark mode
  supportsDarkMode: (theme: KilatTheme) => themeMetadata[theme].supportsDarkMode,
  
  // Generate CSS custom properties for a theme
  generateThemeCSS: (theme: KilatTheme, mode: ColorMode = 'auto') => {
    const meta = themeMetadata[theme];
    return `
      :root {
        --k-theme: ${theme};
        --k-mode: ${mode};
        --k-primary: ${meta.primaryColor};
        --k-accent: ${meta.accentColor};
      }
    `;
  },
  
  // Validate theme configuration
  validateThemeConfig: (config: Partial<ThemeConfig>): boolean => {
    if (config.theme && !themeMetadata[config.theme]) {
      return false;
    }
    
    if (config.mode && !['light', 'dark', 'auto'].includes(config.mode)) {
      return false;
    }
    
    return true;
  }
};
