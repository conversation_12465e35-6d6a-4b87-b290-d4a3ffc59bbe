// 🧰 Kilat.js Utils - Hooks, Helpers & Platform Detection
// Export all utilities
export * from './hooks';
export * from './platform';
export * from './logger';
export * from './helpers';
export * from './types';

// 🎯 Main exports for convenience
export {
  // Platform detection
  usePlatform,
  detectPlatform,
  
  // Theme & UI hooks
  useTheme,
  useLocalStorage,
  useSessionStorage,
  useMediaQuery,
  useKeyboardShortcut,
  
  // Performance hooks
  useDebounce,
  useThrottle,
  useMemoCompare,
  
  // Utility hooks
  useToggle,
  useCounter,
  usePrevious,
  useMount,
  useUnmount,
  useUpdateEffect,
  
  // Error handling
  useSafeHandler,
  useErrorBoundary,
  
  // Logger
  createLogger,
  useLogger,
  
  // Helpers
  cn,
  formatBytes,
  formatDuration,
  generateId,
  sleep,
  retry,
  safeJsonParse,
  safeJsonStringify
} from './hooks';

// Version info
export const KILAT_UTILS_VERSION = '1.0.0';

// Default export
export default {
  version: KILAT_UTILS_VERSION
};
