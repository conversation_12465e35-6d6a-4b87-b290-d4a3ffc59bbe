import { useState, useCallback } from 'react';
import type { LogLevel, LogEntry, LoggerConfig } from './types';

// 🎯 Default Logger Configuration
const defaultConfig: LoggerConfig = {
  level: 'info',
  enableConsole: true,
  enableFile: false,
  format: 'text'
};

// 📊 Log Level Priority
const logLevels: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

// 🎨 Console Colors
const colors = {
  debug: '\x1b[36m', // Cyan
  info: '\x1b[32m',  // Green
  warn: '\x1b[33m',  // Yellow
  error: '\x1b[31m', // Red
  reset: '\x1b[0m'
};

// 📝 Logger Class
export class KilatLogger {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  // 🔧 Update Configuration
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // 📊 Check if level should be logged
  private shouldLog(level: LogLevel): boolean {
    return logLevels[level] >= logLevels[this.config.level];
  }

  // 🎨 Format log message
  private formatMessage(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = entry.level.toUpperCase().padEnd(5);
    
    if (this.config.format === 'json') {
      return JSON.stringify({
        timestamp,
        level: entry.level,
        message: entry.message,
        context: entry.context,
        error: entry.error ? {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        } : undefined
      });
    }
    
    // Text format
    let formatted = `[${timestamp}] ${level} ${entry.message}`;
    
    if (entry.context && Object.keys(entry.context).length > 0) {
      formatted += ` | Context: ${JSON.stringify(entry.context)}`;
    }
    
    if (entry.error) {
      formatted += `\nError: ${entry.error.message}`;
      if (entry.error.stack) {
        formatted += `\nStack: ${entry.error.stack}`;
      }
    }
    
    return formatted;
  }

  // 📤 Log to console
  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;
    
    const message = this.formatMessage(entry);
    const color = colors[entry.level];
    const coloredMessage = `${color}${message}${colors.reset}`;
    
    switch (entry.level) {
      case 'debug':
        console.debug(coloredMessage);
        break;
      case 'info':
        console.info(coloredMessage);
        break;
      case 'warn':
        console.warn(coloredMessage);
        break;
      case 'error':
        console.error(coloredMessage);
        break;
    }
  }

  // 📁 Log to file (browser implementation using localStorage)
  private logToFile(entry: LogEntry): void {
    if (!this.config.enableFile || typeof window === 'undefined') return;
    
    try {
      const key = `kilat-logs-${new Date().toISOString().split('T')[0]}`;
      const existingLogs = localStorage.getItem(key) || '[]';
      const logs = JSON.parse(existingLogs);
      
      logs.push({
        timestamp: entry.timestamp.toISOString(),
        level: entry.level,
        message: entry.message,
        context: entry.context,
        error: entry.error ? {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        } : undefined
      });
      
      // Keep only last 1000 logs per day
      if (logs.length > 1000) {
        logs.splice(0, logs.length - 1000);
      }
      
      localStorage.setItem(key, JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to write log to localStorage:', error);
    }
  }

  // 📝 Core log method
  private log(level: LogLevel, message: string, context?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) return;
    
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error
    };
    
    // Store in memory
    this.logs.push(entry);
    
    // Keep only last 100 logs in memory
    if (this.logs.length > 100) {
      this.logs.shift();
    }
    
    // Output to console and file
    this.logToConsole(entry);
    this.logToFile(entry);
  }

  // 🐛 Debug level
  debug(message: string, context?: Record<string, any>): void {
    this.log('debug', message, context);
  }

  // ℹ️ Info level
  info(message: string, context?: Record<string, any>): void {
    this.log('info', message, context);
  }

  // ⚠️ Warning level
  warn(message: string, context?: Record<string, any>): void {
    this.log('warn', message, context);
  }

  // 🚨 Error level
  error(message: string, error?: Error, context?: Record<string, any>): void {
    this.log('error', message, context, error);
  }

  // 📊 Get logs
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  // 🧹 Clear logs
  clearLogs(): void {
    this.logs = [];
  }

  // 📤 Export logs
  exportLogs(): string {
    return this.logs.map(entry => this.formatMessage(entry)).join('\n');
  }
}

// 🏭 Create Logger Instance
export function createLogger(config?: Partial<LoggerConfig>): KilatLogger {
  return new KilatLogger(config);
}

// 🌍 Global Logger Instance
let globalLogger: KilatLogger | null = null;

export function getGlobalLogger(): KilatLogger {
  if (!globalLogger) {
    globalLogger = createLogger({
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      enableConsole: true,
      enableFile: process.env.NODE_ENV === 'production'
    });
  }
  return globalLogger;
}

// 🪝 Logger Hook
export function useLogger(config?: Partial<LoggerConfig>) {
  const [logger] = useState(() => createLogger(config));
  
  const debug = useCallback((message: string, context?: Record<string, any>) => {
    logger.debug(message, context);
  }, [logger]);
  
  const info = useCallback((message: string, context?: Record<string, any>) => {
    logger.info(message, context);
  }, [logger]);
  
  const warn = useCallback((message: string, context?: Record<string, any>) => {
    logger.warn(message, context);
  }, [logger]);
  
  const error = useCallback((message: string, errorObj?: Error, context?: Record<string, any>) => {
    logger.error(message, errorObj, context);
  }, [logger]);
  
  const getLogs = useCallback(() => {
    return logger.getLogs();
  }, [logger]);
  
  const clearLogs = useCallback(() => {
    logger.clearLogs();
  }, [logger]);
  
  const exportLogs = useCallback(() => {
    return logger.exportLogs();
  }, [logger]);
  
  return {
    debug,
    info,
    warn,
    error,
    getLogs,
    clearLogs,
    exportLogs,
    logger
  };
}
