import chalk from 'chalk';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'success';

export interface LoggerConfig {
  level: LogLevel;
  prefix?: string;
  timestamp?: boolean;
  colors?: boolean;
  file?: string;
  maxFileSize?: number;
  maxFiles?: number;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  data?: any;
  prefix?: string;
}

/**
 * 📝 KilatLogger - Universal logging utility
 * Works in browser, Node.js, and React Native
 */
export class KilatLogger {
  private config: LoggerConfig;
  private logHistory: LogEntry[] = [];
  private maxHistorySize = 1000;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      timestamp: true,
      colors: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      ...config
    };
  }

  // 🎯 Main logging methods
  debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  warn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  error(message: string, error?: Error | any, data?: any): void {
    let errorData = data;
    
    if (error instanceof Error) {
      errorData = {
        ...data,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      };
    } else if (error && typeof error === 'object') {
      errorData = { ...data, error };
    }
    
    this.log('error', message, errorData);
  }

  success(message: string, data?: any): void {
    this.log('success', message, data);
  }

  // 📊 Core logging function
  private log(level: LogLevel, message: string, data?: any): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      data,
      prefix: this.config.prefix
    };

    // Add to history
    this.addToHistory(entry);

    // Output to console
    this.outputToConsole(entry);

    // Output to file (Node.js only)
    if (this.config.file && typeof process !== 'undefined') {
      this.outputToFile(entry);
    }
  }

  // 🎨 Console output with colors
  private outputToConsole(entry: LogEntry): void {
    const { level, message, timestamp, data, prefix } = entry;
    
    let output = '';
    
    // Add timestamp
    if (this.config.timestamp) {
      const timeStr = timestamp.toISOString();
      output += this.config.colors ? chalk.gray(`[${timeStr}]`) : `[${timeStr}]`;
      output += ' ';
    }
    
    // Add prefix
    if (prefix) {
      output += this.config.colors ? chalk.cyan(`[${prefix}]`) : `[${prefix}]`;
      output += ' ';
    }
    
    // Add level indicator
    const levelIndicator = this.getLevelIndicator(level);
    output += levelIndicator + ' ';
    
    // Add message
    output += this.formatMessage(level, message);
    
    // Output to appropriate console method
    switch (level) {
      case 'debug':
        console.debug(output, data ? data : '');
        break;
      case 'info':
        console.info(output, data ? data : '');
        break;
      case 'warn':
        console.warn(output, data ? data : '');
        break;
      case 'error':
        console.error(output, data ? data : '');
        break;
      case 'success':
        console.log(output, data ? data : '');
        break;
    }
  }

  // 📁 File output (Node.js only)
  private async outputToFile(entry: LogEntry): Promise<void> {
    if (typeof require === 'undefined') return;
    
    try {
      const fs = require('fs').promises;
      const path = require('path');
      
      const logLine = this.formatLogLine(entry);
      
      // Ensure log directory exists
      const logDir = path.dirname(this.config.file!);
      await fs.mkdir(logDir, { recursive: true });
      
      // Check file size and rotate if needed
      await this.rotateLogFile();
      
      // Append to log file
      await fs.appendFile(this.config.file!, logLine + '\n');
      
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  // 🔄 Log file rotation
  private async rotateLogFile(): Promise<void> {
    if (typeof require === 'undefined') return;
    
    try {
      const fs = require('fs').promises;
      const path = require('path');
      
      const stats = await fs.stat(this.config.file!).catch(() => null);
      
      if (stats && stats.size > this.config.maxFileSize!) {
        const ext = path.extname(this.config.file!);
        const base = path.basename(this.config.file!, ext);
        const dir = path.dirname(this.config.file!);
        
        // Rotate existing files
        for (let i = this.config.maxFiles! - 1; i > 0; i--) {
          const oldFile = path.join(dir, `${base}.${i}${ext}`);
          const newFile = path.join(dir, `${base}.${i + 1}${ext}`);
          
          try {
            await fs.rename(oldFile, newFile);
          } catch {
            // File doesn't exist, continue
          }
        }
        
        // Move current file to .1
        const rotatedFile = path.join(dir, `${base}.1${ext}`);
        await fs.rename(this.config.file!, rotatedFile);
      }
      
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  // 🎯 Utility methods
  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'success'];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  private getLevelIndicator(level: LogLevel): string {
    if (!this.config.colors) {
      return `[${level.toUpperCase()}]`;
    }
    
    switch (level) {
      case 'debug':
        return chalk.gray('🐛 DEBUG');
      case 'info':
        return chalk.blue('ℹ️  INFO');
      case 'warn':
        return chalk.yellow('⚠️  WARN');
      case 'error':
        return chalk.red('❌ ERROR');
      case 'success':
        return chalk.green('✅ SUCCESS');
      default:
        return `[${level.toUpperCase()}]`;
    }
  }

  private formatMessage(level: LogLevel, message: string): string {
    if (!this.config.colors) {
      return message;
    }
    
    switch (level) {
      case 'debug':
        return chalk.gray(message);
      case 'info':
        return chalk.white(message);
      case 'warn':
        return chalk.yellow(message);
      case 'error':
        return chalk.red(message);
      case 'success':
        return chalk.green(message);
      default:
        return message;
    }
  }

  private formatLogLine(entry: LogEntry): string {
    const { level, message, timestamp, data, prefix } = entry;
    
    let line = timestamp.toISOString();
    
    if (prefix) {
      line += ` [${prefix}]`;
    }
    
    line += ` [${level.toUpperCase()}] ${message}`;
    
    if (data) {
      line += ` ${JSON.stringify(data)}`;
    }
    
    return line;
  }

  private addToHistory(entry: LogEntry): void {
    this.logHistory.push(entry);
    
    // Keep history size manageable
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory = this.logHistory.slice(-this.maxHistorySize);
    }
  }

  // 📊 Public utility methods
  getHistory(): LogEntry[] {
    return [...this.logHistory];
  }

  clearHistory(): void {
    this.logHistory = [];
  }

  getConfig(): LoggerConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // ⏱️ Performance timing
  private timers = new Map<string, number>();

  time(label: string): void {
    this.timers.set(label, Date.now());
  }

  timeEnd(label: string): void {
    const startTime = this.timers.get(label);
    
    if (startTime) {
      const duration = Date.now() - startTime;
      this.info(`Timer "${label}": ${duration}ms`);
      this.timers.delete(label);
    } else {
      this.warn(`Timer "${label}" was not started`);
    }
  }

  // 📊 Log statistics
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<LogLevel, number>;
    oldestLog?: Date;
    newestLog?: Date;
  } {
    const logsByLevel: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
      success: 0
    };
    
    this.logHistory.forEach(entry => {
      logsByLevel[entry.level]++;
    });
    
    return {
      totalLogs: this.logHistory.length,
      logsByLevel,
      oldestLog: this.logHistory[0]?.timestamp,
      newestLog: this.logHistory[this.logHistory.length - 1]?.timestamp
    };
  }

  // 🔍 Search logs
  search(query: string, level?: LogLevel): LogEntry[] {
    return this.logHistory.filter(entry => {
      const matchesLevel = !level || entry.level === level;
      const matchesQuery = entry.message.toLowerCase().includes(query.toLowerCase()) ||
                          (entry.data && JSON.stringify(entry.data).toLowerCase().includes(query.toLowerCase()));
      
      return matchesLevel && matchesQuery;
    });
  }

  // 📤 Export logs
  exportLogs(format: 'json' | 'csv' | 'txt' = 'json'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(this.logHistory, null, 2);
      
      case 'csv':
        const headers = 'timestamp,level,prefix,message,data\n';
        const rows = this.logHistory.map(entry => {
          const data = entry.data ? JSON.stringify(entry.data).replace(/"/g, '""') : '';
          return `"${entry.timestamp.toISOString()}","${entry.level}","${entry.prefix || ''}","${entry.message.replace(/"/g, '""')}","${data}"`;
        }).join('\n');
        return headers + rows;
      
      case 'txt':
        return this.logHistory.map(entry => this.formatLogLine(entry)).join('\n');
      
      default:
        return JSON.stringify(this.logHistory, null, 2);
    }
  }
}

// 🏭 Factory function for creating loggers
export function createLogger(config: Partial<LoggerConfig> = {}): KilatLogger {
  return new KilatLogger(config);
}

// 🌍 Global logger instance
export const logger = createLogger();

// 🎯 Convenience exports
export default {
  createLogger,
  logger,
  KilatLogger
};
