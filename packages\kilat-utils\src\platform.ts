import { useState, useEffect } from 'react';
import UAParser from 'ua-parser-js';
import type { KilatPlatformInfo, KilatPlatformType } from './types';

// 🌐 Platform Detection Utility
export function detectPlatform(): KilatPlatformInfo {
  // Server-side rendering fallback
  if (typeof window === 'undefined') {
    return {
      type: 'web',
      isWeb: true,
      isDesktop: false,
      isMobile: false,
      userAgent: '',
      viewport: { width: 1920, height: 1080 },
      device: { type: 'desktop' },
      browser: {},
      os: {},
      engine: {}
    };
  }

  const parser = new UAParser(window.navigator.userAgent);
  const result = parser.getResult();
  
  // Determine platform type
  let type: KilatPlatformType = 'web';
  
  // Check if running in Electron (desktop)
  if (window.navigator.userAgent.includes('Electron')) {
    type = 'desktop';
  }
  // Check if running in mobile app (Expo/Capacitor)
  else if (window.navigator.userAgent.includes('Expo') || 
           window.navigator.userAgent.includes('Capacitor')) {
    type = 'mobile';
  }
  // Check device type for mobile web
  else if (result.device.type === 'mobile' || result.device.type === 'tablet') {
    type = 'mobile';
  }

  return {
    type,
    isWeb: type === 'web',
    isDesktop: type === 'desktop',
    isMobile: type === 'mobile',
    userAgent: window.navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    device: {
      type: result.device.type || 'desktop',
      vendor: result.device.vendor,
      model: result.device.model
    },
    browser: {
      name: result.browser.name,
      version: result.browser.version,
      major: result.browser.major
    },
    os: {
      name: result.os.name,
      version: result.os.version
    },
    engine: {
      name: result.engine.name,
      version: result.engine.version
    }
  };
}

// 🪝 Platform Detection Hook
export function usePlatform(): KilatPlatformInfo {
  const [platform, setPlatform] = useState<KilatPlatformInfo>(() => detectPlatform());

  useEffect(() => {
    // Update platform info on window resize
    const handleResize = () => {
      setPlatform(prev => ({
        ...prev,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return platform;
}

// 🎯 Platform-specific utilities
export const platformUtils = {
  // Check if current platform matches
  is: (type: KilatPlatformType): boolean => {
    return detectPlatform().type === type;
  },
  
  // Get platform-specific class names
  getClassNames: (): string[] => {
    const platform = detectPlatform();
    const classes = [`kilat-platform-${platform.type}`];
    
    if (platform.device.type) {
      classes.push(`kilat-device-${platform.device.type}`);
    }
    
    if (platform.os.name) {
      classes.push(`kilat-os-${platform.os.name.toLowerCase().replace(/\s+/g, '-')}`);
    }
    
    return classes;
  },
  
  // Get platform-specific styles
  getStyles: (): Record<string, any> => {
    const platform = detectPlatform();
    
    return {
      '--kilat-platform': platform.type,
      '--kilat-viewport-width': `${platform.viewport.width}px`,
      '--kilat-viewport-height': `${platform.viewport.height}px`,
      '--kilat-device-type': platform.device.type || 'unknown'
    };
  },
  
  // Platform-specific feature detection
  supports: {
    touch: (): boolean => 'ontouchstart' in window,
    webgl: (): boolean => {
      try {
        const canvas = document.createElement('canvas');
        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
      } catch {
        return false;
      }
    },
    webgl2: (): boolean => {
      try {
        const canvas = document.createElement('canvas');
        return !!canvas.getContext('webgl2');
      } catch {
        return false;
      }
    },
    webrtc: (): boolean => !!(window.RTCPeerConnection || window.webkitRTCPeerConnection),
    serviceWorker: (): boolean => 'serviceWorker' in navigator,
    pushNotifications: (): boolean => 'PushManager' in window,
    geolocation: (): boolean => 'geolocation' in navigator,
    camera: (): boolean => !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    fullscreen: (): boolean => !!(document.fullscreenEnabled || document.webkitFullscreenEnabled),
    clipboard: (): boolean => !!(navigator.clipboard && navigator.clipboard.writeText)
  }
};
