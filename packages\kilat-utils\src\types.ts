// 🎯 Kilat Utils Types

// 🌐 Platform Types
export type KilatPlatformType = 'web' | 'desktop' | 'mobile';

export interface KilatPlatformInfo {
  type: KilatPlatformType;
  isWeb: boolean;
  isDesktop: boolean;
  isMobile: boolean;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
  device: {
    type: 'mobile' | 'tablet' | 'desktop';
    vendor?: string;
    model?: string;
  };
  browser: {
    name?: string;
    version?: string;
    major?: string;
  };
  os: {
    name?: string;
    version?: string;
  };
  engine: {
    name?: string;
    version?: string;
  };
}

// 📊 Logger Types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  error?: Error;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  filePath?: string;
  maxFileSize?: number;
  maxFiles?: number;
  format?: 'json' | 'text';
}

// 🎯 Hook Types
export interface UseToggleReturn {
  value: boolean;
  toggle: () => void;
  setTrue: () => void;
  setFalse: () => void;
}

export interface UseCounterReturn {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  set: (value: number) => void;
}

export interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
}

// 🔧 Utility Types
export interface RetryOptions {
  maxAttempts: number;
  delay: number;
  backoff?: 'linear' | 'exponential';
  onRetry?: (attempt: number, error: Error) => void;
}

export interface DebounceOptions {
  leading?: boolean;
  trailing?: boolean;
}

export interface ThrottleOptions {
  leading?: boolean;
  trailing?: boolean;
}

// 🎨 Theme Types
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textMuted: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface ThemeConfig {
  name: string;
  colors: ThemeColors;
  fonts: {
    sans: string;
    mono: string;
    display: string;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
  animations: Record<string, string>;
}

// 🔐 Error Types
export interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

export interface SafeHandlerOptions {
  fallback?: any;
  onError?: (error: Error) => void;
  silent?: boolean;
}

// 📱 Media Query Types
export interface MediaQueryOptions {
  defaultValue?: boolean;
  initializeWithValue?: boolean;
}

// ⌨️ Keyboard Shortcut Types
export interface KeyboardShortcutOptions {
  preventDefault?: boolean;
  stopPropagation?: boolean;
  enabled?: boolean;
}

export type KeyboardEventHandler = (event: KeyboardEvent) => void;
