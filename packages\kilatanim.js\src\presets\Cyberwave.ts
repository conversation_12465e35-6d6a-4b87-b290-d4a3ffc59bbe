import * as THREE from 'three';
import type { PresetFactory, CyberwaveConfig } from '../types';

/**
 * Cyberwave Preset - Retro synthwave landscape with grid
 * Creates a nostalgic 80s cyberpunk landscape
 */
export const CyberwavePreset: PresetFactory = {
  create: (config: CyberwaveConfig = {}) => {
    const {
      waveHeight = 2,
      waveFrequency = 0.5,
      gridSize = 50,
      wireframe = true,
      glitchEffect = true,
      colors = ['#ff00ff', '#00ffff', '#ffff00']
    } = config;

    const group = new THREE.Group();
    group.name = 'cyberwave';

    // 🌊 Animated wave grid
    const gridGeometry = new THREE.PlaneGeometry(gridSize, gridSize, 64, 64);
    const gridMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        waveHeight: { value: waveHeight },
        waveFrequency: { value: waveFrequency },
        color1: { value: new THREE.Color(colors[0]) },
        color2: { value: new THREE.Color(colors[1]) },
        color3: { value: new THREE.Color(colors[2]) }
      },
      vertexShader: `
        uniform float time;
        uniform float waveHeight;
        uniform float waveFrequency;
        varying vec3 vPosition;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          vUv = uv;
          vPosition = position;
          
          // Create wave pattern
          float wave1 = sin(position.x * waveFrequency + time * 2.0) * waveHeight;
          float wave2 = sin(position.y * waveFrequency * 0.7 + time * 1.5) * waveHeight * 0.5;
          float wave3 = sin((position.x + position.y) * waveFrequency * 0.3 + time) * waveHeight * 0.3;
          
          vElevation = wave1 + wave2 + wave3;
          
          vec3 newPosition = position;
          newPosition.z = vElevation;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color1;
        uniform vec3 color2;
        uniform vec3 color3;
        varying vec3 vPosition;
        varying vec2 vUv;
        varying float vElevation;
        
        void main() {
          // Color based on elevation and position
          float colorMix1 = (vElevation + 2.0) / 4.0;
          float colorMix2 = sin(vPosition.x * 0.1 + time) * 0.5 + 0.5;
          
          vec3 color = mix(color1, color2, colorMix1);
          color = mix(color, color3, colorMix2 * 0.3);
          
          // Grid lines
          vec2 grid = abs(fract(vUv * 32.0) - 0.5) / fwidth(vUv * 32.0);
          float line = min(grid.x, grid.y);
          float gridPattern = 1.0 - min(line, 1.0);
          
          // Glow effect
          float glow = 1.0 - length(vUv - 0.5) * 2.0;
          glow = max(0.0, glow);
          
          float intensity = gridPattern * 0.8 + glow * 0.2;
          gl_FragColor = vec4(color * intensity, intensity);
        }
      `,
      transparent: true,
      wireframe: wireframe,
      blending: THREE.AdditiveBlending
    });

    const grid = new THREE.Mesh(gridGeometry, gridMaterial);
    grid.rotation.x = -Math.PI / 2;
    grid.position.y = -5;
    group.add(grid);

    // 🌅 Horizon glow
    const horizonGeometry = new THREE.PlaneGeometry(gridSize * 2, 10);
    const horizonMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color1: { value: new THREE.Color(colors[0]) },
        color2: { value: new THREE.Color(colors[1]) }
      },
      vertexShader: `
        varying vec2 vUv;
        
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color1;
        uniform vec3 color2;
        varying vec2 vUv;
        
        void main() {
          // Horizontal gradient
          float gradient = 1.0 - vUv.y;
          
          // Pulsing effect
          float pulse = sin(time * 2.0) * 0.3 + 0.7;
          
          vec3 color = mix(color2, color1, gradient);
          float intensity = gradient * pulse * 0.5;
          
          gl_FragColor = vec4(color * intensity, intensity);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.DoubleSide
    });

    const horizon = new THREE.Mesh(horizonGeometry, horizonMaterial);
    horizon.position.set(0, 0, -gridSize / 2);
    group.add(horizon);

    // 🌟 Floating particles
    const particleCount = 200;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleColors = new Float32Array(particleCount * 3);
    const particleSizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      particlePositions[i3] = (Math.random() - 0.5) * gridSize;
      particlePositions[i3 + 1] = Math.random() * 20 - 5;
      particlePositions[i3 + 2] = (Math.random() - 0.5) * gridSize;
      
      const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
      particleColors[i3] = color.r;
      particleColors[i3 + 1] = color.g;
      particleColors[i3 + 2] = color.b;
      
      particleSizes[i] = Math.random() * 0.2 + 0.1;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        pixelRatio: { value: Math.min(window.devicePixelRatio, 2) }
      },
      vertexShader: `
        attribute float size;
        varying vec3 vColor;
        uniform float time;
        uniform float pixelRatio;
        
        void main() {
          vColor = color;
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          
          // Floating animation
          vec3 pos = position;
          pos.y += sin(time + position.x * 0.1) * 0.5;
          pos.x += cos(time * 0.7 + position.z * 0.1) * 0.3;
          
          mvPosition = modelViewMatrix * vec4(pos, 1.0);
          gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        
        void main() {
          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
          float strength = 0.05 / distanceToCenter - 0.1;
          
          gl_FragColor = vec4(vColor * strength, strength);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      vertexColors: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    return group;
  },

  update: (object: THREE.Object3D, deltaTime: number) => {
    const cyberwave = object as THREE.Group;
    const time = performance.now() * 0.001;

    cyberwave.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Points) {
        const material = child.material as THREE.ShaderMaterial;
        if (material.uniforms?.time) {
          material.uniforms.time.value = time;
        }
      }
    });

    // Subtle camera movement
    cyberwave.rotation.y = Math.sin(time * 0.1) * 0.02;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Points) {
        child.geometry?.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material?.dispose();
        }
      }
    });
  }
};
