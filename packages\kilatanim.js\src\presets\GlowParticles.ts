import * as THREE from 'three';
import type { PresetFactory, GlowParticlesConfig } from '../types';

/**
 * Glow Particles Preset - Interactive particle system with connections
 * Creates floating particles that connect when close to each other
 */
export const GlowParticlesPreset: PresetFactory = {
  create: (config: GlowParticlesConfig = {}) => {
    const {
      particleCount = 500,
      particleSize = 0.1,
      floatSpeed = 1,
      glowRadius = 2,
      connectionDistance = 3,
      showConnections = true,
      colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600']
    } = config;

    const group = new THREE.Group();
    group.name = 'glowParticles';

    // ✨ Create particle system
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleColors = new Float32Array(particleCount * 3);
    const particleSizes = new Float32Array(particleCount);
    const particleVelocities = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Random position in sphere
      const radius = Math.random() * 15 + 5;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      
      particlePositions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i3 + 2] = radius * Math.cos(phi);
      
      // Random velocity
      particleVelocities[i3] = (Math.random() - 0.5) * floatSpeed;
      particleVelocities[i3 + 1] = (Math.random() - 0.5) * floatSpeed;
      particleVelocities[i3 + 2] = (Math.random() - 0.5) * floatSpeed;
      
      // Random color
      const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
      particleColors[i3] = color.r;
      particleColors[i3 + 1] = color.g;
      particleColors[i3 + 2] = color.b;
      
      // Random size
      particleSizes[i] = particleSize + Math.random() * particleSize;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        pixelRatio: { value: Math.min(window.devicePixelRatio, 2) },
        glowRadius: { value: glowRadius }
      },
      vertexShader: `
        attribute float size;
        varying vec3 vColor;
        varying float vSize;
        uniform float time;
        uniform float pixelRatio;
        
        void main() {
          vColor = color;
          vSize = size;
          
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          
          // Pulsing effect
          float pulse = sin(time * 3.0 + position.x * 10.0) * 0.3 + 0.7;
          gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z) * pulse;
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        varying float vSize;
        uniform float glowRadius;
        
        void main() {
          vec2 center = gl_PointCoord - vec2(0.5);
          float distanceToCenter = length(center);
          
          // Core particle
          float core = 1.0 - smoothstep(0.0, 0.3, distanceToCenter);
          
          // Glow effect
          float glow = 1.0 / (distanceToCenter * glowRadius + 1.0);
          
          float intensity = core + glow * 0.5;
          gl_FragColor = vec4(vColor * intensity, intensity);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      vertexColors: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    particles.userData = { velocities: particleVelocities };
    group.add(particles);

    // 🔗 Connection lines (if enabled)
    if (showConnections) {
      const maxConnections = particleCount * 2; // Estimate
      const lineGeometry = new THREE.BufferGeometry();
      const linePositions = new Float32Array(maxConnections * 6); // 2 points per line, 3 coords per point
      const lineColors = new Float32Array(maxConnections * 6); // 2 colors per line, 3 components per color
      
      lineGeometry.setAttribute('position', new THREE.BufferAttribute(linePositions, 3));
      lineGeometry.setAttribute('color', new THREE.BufferAttribute(lineColors, 3));
      
      const lineMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          opacity: { value: 0.3 }
        },
        vertexShader: `
          varying vec3 vColor;
          
          void main() {
            vColor = color;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform float time;
          uniform float opacity;
          varying vec3 vColor;
          
          void main() {
            float pulse = sin(time * 2.0) * 0.3 + 0.7;
            gl_FragColor = vec4(vColor, opacity * pulse);
          }
        `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        vertexColors: true
      });

      const lines = new THREE.LineSegments(lineGeometry, lineMaterial);
      lines.userData = { 
        maxConnections,
        connectionDistance: connectionDistance * connectionDistance // Store squared distance for performance
      };
      group.add(lines);
    }

    return group;
  },

  update: (object: THREE.Object3D, deltaTime: number) => {
    const glowParticles = object as THREE.Group;
    const time = performance.now() * 0.001;

    glowParticles.traverse((child) => {
      if (child instanceof THREE.Points) {
        const positions = child.geometry.attributes.position.array as Float32Array;
        const velocities = child.userData.velocities as Float32Array;
        
        // Update particle positions
        for (let i = 0; i < positions.length; i += 3) {
          // Apply velocity
          positions[i] += velocities[i] * deltaTime;
          positions[i + 1] += velocities[i + 1] * deltaTime;
          positions[i + 2] += velocities[i + 2] * deltaTime;
          
          // Boundary check - bounce off invisible sphere
          const distance = Math.sqrt(
            positions[i] * positions[i] + 
            positions[i + 1] * positions[i + 1] + 
            positions[i + 2] * positions[i + 2]
          );
          
          if (distance > 20) {
            // Reverse velocity and add some randomness
            velocities[i] = -velocities[i] * 0.8 + (Math.random() - 0.5) * 0.1;
            velocities[i + 1] = -velocities[i + 1] * 0.8 + (Math.random() - 0.5) * 0.1;
            velocities[i + 2] = -velocities[i + 2] * 0.8 + (Math.random() - 0.5) * 0.1;
          }
          
          // Add slight attraction to center
          const centerForce = 0.1 * deltaTime;
          velocities[i] -= positions[i] * centerForce;
          velocities[i + 1] -= positions[i + 1] * centerForce;
          velocities[i + 2] -= positions[i + 2] * centerForce;
        }
        
        child.geometry.attributes.position.needsUpdate = true;
        
        // Update material
        const material = child.material as THREE.ShaderMaterial;
        if (material.uniforms?.time) {
          material.uniforms.time.value = time;
        }
      }
      
      // Update connection lines
      if (child instanceof THREE.LineSegments && child.userData.maxConnections) {
        const particles = glowParticles.children.find(c => c instanceof THREE.Points) as THREE.Points;
        if (particles) {
          const particlePositions = particles.geometry.attributes.position.array as Float32Array;
          const particleColors = particles.geometry.attributes.color.array as Float32Array;
          const linePositions = child.geometry.attributes.position.array as Float32Array;
          const lineColors = child.geometry.attributes.color.array as Float32Array;
          const connectionDistance = child.userData.connectionDistance;
          
          let lineIndex = 0;
          const particleCount = particlePositions.length / 3;
          
          // Check connections between particles
          for (let i = 0; i < particleCount && lineIndex < child.userData.maxConnections * 6; i++) {
            for (let j = i + 1; j < particleCount && lineIndex < child.userData.maxConnections * 6; j++) {
              const i3 = i * 3;
              const j3 = j * 3;
              
              const dx = particlePositions[i3] - particlePositions[j3];
              const dy = particlePositions[i3 + 1] - particlePositions[j3 + 1];
              const dz = particlePositions[i3 + 2] - particlePositions[j3 + 2];
              const distanceSquared = dx * dx + dy * dy + dz * dz;
              
              if (distanceSquared < connectionDistance) {
                // Add line
                linePositions[lineIndex] = particlePositions[i3];
                linePositions[lineIndex + 1] = particlePositions[i3 + 1];
                linePositions[lineIndex + 2] = particlePositions[i3 + 2];
                
                linePositions[lineIndex + 3] = particlePositions[j3];
                linePositions[lineIndex + 4] = particlePositions[j3 + 1];
                linePositions[lineIndex + 5] = particlePositions[j3 + 2];
                
                // Average colors
                lineColors[lineIndex] = (particleColors[i3] + particleColors[j3]) * 0.5;
                lineColors[lineIndex + 1] = (particleColors[i3 + 1] + particleColors[j3 + 1]) * 0.5;
                lineColors[lineIndex + 2] = (particleColors[i3 + 2] + particleColors[j3 + 2]) * 0.5;
                
                lineColors[lineIndex + 3] = lineColors[lineIndex];
                lineColors[lineIndex + 4] = lineColors[lineIndex + 1];
                lineColors[lineIndex + 5] = lineColors[lineIndex + 2];
                
                lineIndex += 6;
              }
            }
          }
          
          // Clear unused line positions
          for (let i = lineIndex; i < linePositions.length; i++) {
            linePositions[i] = 0;
            lineColors[i] = 0;
          }
          
          child.geometry.attributes.position.needsUpdate = true;
          child.geometry.attributes.color.needsUpdate = true;
          
          // Update line material
          const material = child.material as THREE.ShaderMaterial;
          if (material.uniforms?.time) {
            material.uniforms.time.value = time;
          }
        }
      }
    });

    // Gentle rotation
    glowParticles.rotation.y += deltaTime * 0.05;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Points || child instanceof THREE.LineSegments) {
        child.geometry?.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material?.dispose();
        }
      }
    });
  }
};
