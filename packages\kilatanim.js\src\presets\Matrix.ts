import * as THREE from 'three';
import type { PresetFactory, MatrixConfig } from '../types';

/**
 * Matrix Preset - Digital rain effect like in The Matrix
 * Creates falling green characters in 3D space
 */
export const MatrixPreset: PresetFactory = {
  create: (config: MatrixConfig = {}) => {
    const {
      columns = 50,
      fallSpeed = 2,
      characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      fontSize = 0.5,
      trailLength = 20,
      colors = ['#00ff41', '#008f11', '#004400'],
      intensity = 1
    } = config;

    const group = new THREE.Group();
    group.name = 'matrix';

    // 📝 Create character texture
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    canvas.width = 64;
    canvas.height = 64;
    
    context.fillStyle = '#00ff41';
    context.font = '48px monospace';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText('M', 32, 32);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 🌧️ Create matrix rain columns
    for (let col = 0; col < columns; col++) {
      const columnGroup = new THREE.Group();
      
      // Position columns in a grid
      const x = (col - columns / 2) * 2;
      const z = Math.random() * 20 - 10;
      columnGroup.position.set(x, 0, z);

      // 💧 Create drops for this column
      const dropCount = trailLength;
      const dropGeometry = new THREE.PlaneGeometry(fontSize, fontSize);
      
      for (let drop = 0; drop < dropCount; drop++) {
        // Random character
        const char = characters[Math.floor(Math.random() * characters.length)];
        
        // Create character texture
        const charCanvas = document.createElement('canvas');
        const charContext = charCanvas.getContext('2d')!;
        charCanvas.width = 64;
        charCanvas.height = 64;
        
        // Brightness based on position in trail (head is brightest)
        const brightness = (dropCount - drop) / dropCount;
        const alpha = brightness * intensity;
        
        charContext.fillStyle = `rgba(0, 255, 65, ${alpha})`;
        charContext.font = '48px monospace';
        charContext.textAlign = 'center';
        charContext.textBaseline = 'middle';
        charContext.fillText(char, 32, 32);
        
        const charTexture = new THREE.CanvasTexture(charCanvas);
        charTexture.needsUpdate = true;

        const dropMaterial = new THREE.MeshBasicMaterial({
          map: charTexture,
          transparent: true,
          opacity: alpha,
          blending: THREE.AdditiveBlending
        });

        const dropMesh = new THREE.Mesh(dropGeometry, dropMaterial);
        
        // Position drops vertically
        dropMesh.position.y = Math.random() * 20 + drop * fontSize;
        dropMesh.userData = {
          originalY: dropMesh.position.y,
          fallSpeed: fallSpeed + Math.random() * 2,
          character: char,
          brightness: brightness,
          changeTimer: Math.random() * 2
        };

        columnGroup.add(dropMesh);
      }

      group.add(columnGroup);
    }

    // 🌐 Background grid
    const gridSize = 50;
    const gridGeometry = new THREE.PlaneGeometry(gridSize, gridSize, 20, 20);
    const gridMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        opacity: { value: 0.1 }
      },
      vertexShader: `
        varying vec2 vUv;
        uniform float time;
        
        void main() {
          vUv = uv;
          vec3 pos = position;
          
          // Add subtle wave effect
          pos.z += sin(pos.x * 0.5 + time) * 0.1;
          pos.z += cos(pos.y * 0.3 + time * 0.7) * 0.1;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float opacity;
        varying vec2 vUv;
        
        void main() {
          // Grid pattern
          vec2 grid = abs(fract(vUv * 20.0) - 0.5) / fwidth(vUv * 20.0);
          float line = min(grid.x, grid.y);
          float gridPattern = 1.0 - min(line, 1.0);
          
          // Pulsing effect
          float pulse = sin(time * 2.0) * 0.5 + 0.5;
          
          vec3 color = vec3(0.0, 1.0, 0.25) * gridPattern * pulse;
          gl_FragColor = vec4(color, gridPattern * opacity * pulse);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.DoubleSide
    });

    const grid = new THREE.Mesh(gridGeometry, gridMaterial);
    grid.rotation.x = -Math.PI / 2;
    grid.position.y = -10;
    group.add(grid);

    return group;
  },

  update: (object: THREE.Object3D, deltaTime: number) => {
    const matrix = object as THREE.Group;
    const time = performance.now() * 0.001;

    matrix.traverse((child) => {
      if (child instanceof THREE.Mesh && child.userData.fallSpeed !== undefined) {
        // Update falling animation
        child.position.y -= child.userData.fallSpeed * deltaTime;
        
        // Reset position when off screen
        if (child.position.y < -15) {
          child.position.y = 15 + Math.random() * 10;
        }

        // Change character occasionally
        child.userData.changeTimer -= deltaTime;
        if (child.userData.changeTimer <= 0) {
          child.userData.changeTimer = Math.random() * 2 + 0.5;
          
          // Update character
          const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
          const newChar = characters[Math.floor(Math.random() * characters.length)];
          
          // Update texture
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d')!;
          canvas.width = 64;
          canvas.height = 64;
          
          const alpha = child.userData.brightness;
          context.fillStyle = `rgba(0, 255, 65, ${alpha})`;
          context.font = '48px monospace';
          context.textAlign = 'center';
          context.textBaseline = 'middle';
          context.fillText(newChar, 32, 32);
          
          const material = child.material as THREE.MeshBasicMaterial;
          if (material.map) {
            material.map.dispose();
          }
          material.map = new THREE.CanvasTexture(canvas);
          material.map.needsUpdate = true;
        }

        // Add subtle glow effect
        const material = child.material as THREE.MeshBasicMaterial;
        const glowIntensity = 0.8 + Math.sin(time * 5 + child.position.x) * 0.2;
        material.opacity = child.userData.brightness * glowIntensity;
      }

      // Update grid shader
      if (child instanceof THREE.Mesh && child.material instanceof THREE.ShaderMaterial) {
        if (child.material.uniforms?.time) {
          child.material.uniforms.time.value = time;
        }
      }
    });

    // Subtle camera movement
    matrix.rotation.y = Math.sin(time * 0.1) * 0.02;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        child.geometry?.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => {
            if (material.map) material.map.dispose();
            material.dispose();
          });
        } else {
          if (child.material.map) child.material.map.dispose();
          child.material?.dispose();
        }
      }
    });
  }
};
