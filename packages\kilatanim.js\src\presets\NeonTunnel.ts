import * as THREE from 'three';
import type { PresetFactory, NeonTunnelConfig } from '../types';

/**
 * Neon Tunnel Preset - Cyberpunk tunnel with glowing rings
 * Creates an infinite tunnel effect with pulsing neon lights
 */
export const NeonTunnelPreset: PresetFactory = {
  create: (config: NeonTunnelConfig = {}) => {
    const {
      tunnelLength = 100,
      ringCount = 50,
      ringRadius = 5,
      glowIntensity = 1,
      pulseBeat = 1,
      colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00']
    } = config;

    const group = new THREE.Group();
    group.name = 'neonTunnel';

    // 🌀 Create tunnel rings
    for (let i = 0; i < ringCount; i++) {
      const ringGroup = new THREE.Group();
      
      // Position rings along Z axis
      const z = (i / ringCount) * tunnelLength - tunnelLength / 2;
      ringGroup.position.z = z;
      
      // Ring geometry
      const ringGeometry = new THREE.RingGeometry(ringRadius * 0.9, ringRadius, 32);
      
      // Animated material with glow
      const ringMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          color: { value: new THREE.Color(colors[i % colors.length]) },
          glowIntensity: { value: glowIntensity },
          ringIndex: { value: i },
          pulseBeat: { value: pulseBeat }
        },
        vertexShader: `
          varying vec2 vUv;
          varying vec3 vPosition;
          
          void main() {
            vUv = uv;
            vPosition = position;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform float time;
          uniform vec3 color;
          uniform float glowIntensity;
          uniform float ringIndex;
          uniform float pulseBeat;
          varying vec2 vUv;
          varying vec3 vPosition;
          
          void main() {
            // Distance from center
            vec2 center = vec2(0.5);
            float dist = distance(vUv, center);
            
            // Ring pattern
            float ring = smoothstep(0.4, 0.45, dist) * (1.0 - smoothstep(0.48, 0.5, dist));
            
            // Pulsing effect
            float pulse = sin(time * pulseBeat * 3.0 + ringIndex * 0.5) * 0.5 + 0.5;
            
            // Glow effect
            float glow = 1.0 / (dist * 10.0 + 1.0);
            
            // Combine effects
            float intensity = (ring + glow * 0.3) * glowIntensity * (0.7 + pulse * 0.3);
            
            gl_FragColor = vec4(color * intensity, intensity);
          }
        `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide
      });

      const ring = new THREE.Mesh(ringGeometry, ringMaterial);
      ringGroup.add(ring);

      // Add inner glow ring
      const innerRingGeometry = new THREE.RingGeometry(ringRadius * 0.7, ringRadius * 0.8, 16);
      const innerRingMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          color: { value: new THREE.Color(colors[i % colors.length]) },
          ringIndex: { value: i }
        },
        vertexShader: `
          varying vec2 vUv;
          
          void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform float time;
          uniform vec3 color;
          uniform float ringIndex;
          varying vec2 vUv;
          
          void main() {
            float pulse = sin(time * 4.0 + ringIndex * 0.3) * 0.5 + 0.5;
            float intensity = 0.5 + pulse * 0.5;
            gl_FragColor = vec4(color * intensity, intensity * 0.6);
          }
        `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide
      });

      const innerRing = new THREE.Mesh(innerRingGeometry, innerRingMaterial);
      ringGroup.add(innerRing);

      group.add(ringGroup);
    }

    // 🌟 Particle stream
    const particleCount = 1000;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleColors = new Float32Array(particleCount * 3);
    const particleSizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Random position in tunnel
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * ringRadius * 0.8;
      
      particlePositions[i3] = Math.cos(angle) * radius;
      particlePositions[i3 + 1] = Math.sin(angle) * radius;
      particlePositions[i3 + 2] = Math.random() * tunnelLength - tunnelLength / 2;
      
      // Random color from palette
      const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);
      particleColors[i3] = color.r;
      particleColors[i3 + 1] = color.g;
      particleColors[i3 + 2] = color.b;
      
      particleSizes[i] = Math.random() * 0.1 + 0.05;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        pixelRatio: { value: Math.min(window.devicePixelRatio, 2) }
      },
      vertexShader: `
        attribute float size;
        varying vec3 vColor;
        uniform float time;
        uniform float pixelRatio;
        
        void main() {
          vColor = color;
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          
          gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        
        void main() {
          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
          float strength = 0.05 / distanceToCenter - 0.1;
          
          gl_FragColor = vec4(vColor * strength, strength);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      vertexColors: true
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    return group;
  },

  update: (object: THREE.Object3D, deltaTime: number) => {
    const tunnel = object as THREE.Group;
    const time = performance.now() * 0.001;

    tunnel.traverse((child) => {
      // Update ring materials
      if (child instanceof THREE.Mesh && child.material instanceof THREE.ShaderMaterial) {
        if (child.material.uniforms?.time) {
          child.material.uniforms.time.value = time;
        }
      }

      // Move rings towards camera for infinite tunnel effect
      if (child instanceof THREE.Group && child.position.z !== undefined) {
        child.position.z += deltaTime * 10; // Speed of tunnel movement
        
        // Reset position when ring passes camera
        if (child.position.z > 50) {
          child.position.z = -50;
        }
      }

      // Update particles
      if (child instanceof THREE.Points) {
        const positions = child.geometry.attributes.position.array as Float32Array;
        
        for (let i = 0; i < positions.length; i += 3) {
          // Move particles towards camera
          positions[i + 2] += deltaTime * 15;
          
          // Reset particle when it passes camera
          if (positions[i + 2] > 50) {
            positions[i + 2] = -50;
            
            // Randomize position
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 4;
            positions[i] = Math.cos(angle) * radius;
            positions[i + 1] = Math.sin(angle) * radius;
          }
        }
        
        child.geometry.attributes.position.needsUpdate = true;
        
        // Update particle material
        const material = child.material as THREE.ShaderMaterial;
        if (material.uniforms?.time) {
          material.uniforms.time.value = time;
        }
      }
    });

    // Subtle rotation for dynamic effect
    tunnel.rotation.z += deltaTime * 0.1;
  },

  dispose: (object: THREE.Object3D) => {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Points) {
        child.geometry?.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material?.dispose();
        }
      }
    });
  }
};
