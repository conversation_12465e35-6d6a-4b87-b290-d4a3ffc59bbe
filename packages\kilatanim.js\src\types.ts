import { ReactNode } from 'react';
import * as THREE from 'three';

// 🌌 Animation Preset Types
export type KilatAnimationPreset =
  | 'galaxy'
  | 'matrix'
  | 'neonTunnel'
  | 'cyberwave'
  | 'glowParticles'
  | 'hologram'
  | 'plasma'
  | 'nusantara';

// 🎯 Scene Configuration
export interface KilatSceneProps {
  preset: KilatAnimationPreset;
  interactive?: boolean;
  autoRotate?: boolean;
  background?: string;
  ambientSound?: boolean;
  className?: string;
  style?: React.CSSProperties;
  children?: ReactNode;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

// 🎨 Animation Configuration
export interface KilatAnimationConfig {
  speed?: number;
  intensity?: number;
  particleCount?: number;
  colors?: string[];
  size?: {
    width?: number;
    height?: number;
  };
  camera?: {
    position?: [number, number, number];
    fov?: number;
    near?: number;
    far?: number;
  };
  lights?: {
    ambient?: {
      color?: string;
      intensity?: number;
    };
    directional?: {
      color?: string;
      intensity?: number;
      position?: [number, number, number];
    };
  };
}

// 🌟 Galaxy Preset Configuration
export interface GalaxyConfig extends KilatAnimationConfig {
  starCount?: number;
  galaxyRadius?: number;
  spiralArms?: number;
  coreSize?: number;
  starSizes?: {
    min?: number;
    max?: number;
  };
}

// 🔢 Matrix Preset Configuration
export interface MatrixConfig extends KilatAnimationConfig {
  columns?: number;
  fallSpeed?: number;
  characters?: string;
  fontSize?: number;
  trailLength?: number;
}

// 🌀 Neon Tunnel Configuration
export interface NeonTunnelConfig extends KilatAnimationConfig {
  tunnelLength?: number;
  ringCount?: number;
  ringRadius?: number;
  glowIntensity?: number;
  pulseBeat?: number;
}

// 🌊 Cyberwave Configuration
export interface CyberwaveConfig extends KilatAnimationConfig {
  waveHeight?: number;
  waveFrequency?: number;
  gridSize?: number;
  wireframe?: boolean;
  glitchEffect?: boolean;
}

// ✨ Glow Particles Configuration
export interface GlowParticlesConfig extends KilatAnimationConfig {
  particleSize?: number;
  floatSpeed?: number;
  glowRadius?: number;
  connectionDistance?: number;
  showConnections?: boolean;
}

// 🎮 Interactive Controls
export interface InteractiveControls {
  enableZoom?: boolean;
  enablePan?: boolean;
  enableRotate?: boolean;
  autoRotateSpeed?: number;
  dampingFactor?: number;
  minDistance?: number;
  maxDistance?: number;
}

// 🔊 Audio Configuration
export interface AudioConfig {
  enabled?: boolean;
  volume?: number;
  loop?: boolean;
  autoplay?: boolean;
  src?: string;
  analyser?: {
    enabled?: boolean;
    fftSize?: number;
    smoothingTimeConstant?: number;
  };
}

// 🎯 Preset Factory Interface
export interface PresetFactory {
  create: (config?: KilatAnimationConfig) => THREE.Object3D;
  update: (object: THREE.Object3D, deltaTime: number) => void;
  dispose: (object: THREE.Object3D) => void;
}

// 🌐 Scene Manager Interface
export interface SceneManager {
  scene: THREE.Scene;
  camera: THREE.Camera;
  renderer: THREE.WebGLRenderer;
  controls?: any;
  audio?: THREE.Audio;
  currentPreset?: KilatAnimationPreset;
  animationFrame?: number;
  
  init: (canvas: HTMLCanvasElement, config: KilatSceneProps) => void;
  loadPreset: (preset: KilatAnimationPreset, config?: KilatAnimationConfig) => void;
  startAnimation: () => void;
  stopAnimation: () => void;
  resize: (width: number, height: number) => void;
  dispose: () => void;
}

// 🎨 Shader Material Types
export interface ShaderConfig {
  vertexShader?: string;
  fragmentShader?: string;
  uniforms?: { [key: string]: THREE.IUniform };
}

// 🌟 Particle System Types
export interface ParticleSystem {
  geometry: THREE.BufferGeometry;
  material: THREE.Material;
  mesh: THREE.Points;
  count: number;
  positions: Float32Array;
  colors: Float32Array;
  sizes: Float32Array;
  velocities: Float32Array;
}

// 🎭 Animation State
export interface AnimationState {
  isPlaying: boolean;
  currentTime: number;
  deltaTime: number;
  frameCount: number;
  fps: number;
}

// 🔧 Performance Monitoring
export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  drawCalls: number;
  triangles: number;
  points: number;
}

// 🎪 Event Types
export interface KilatAnimationEvents {
  onLoad?: () => void;
  onError?: (error: Error) => void;
  onResize?: (width: number, height: number) => void;
  onInteraction?: (event: MouseEvent | TouchEvent) => void;
  onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
}

// 🔮 Hologram Configuration
export interface HologramConfig extends KilatAnimationConfig {
  gridSize?: number;
  scanLineSpeed?: number;
  glitchIntensity?: number;
  hologramColor?: string;
  opacity?: number;
  wireframe?: boolean;
  scanLines?: boolean;
  glitchEffect?: boolean;
}

// 🌊 Plasma Configuration
export interface PlasmaConfig extends KilatAnimationConfig {
  intensity?: number;
  speed?: number;
  colors?: string[];
  resolution?: number;
  turbulence?: number;
  electricArcs?: boolean;
  pulseEffect?: boolean;
}

// 🏝️ Nusantara Configuration
export interface NusantaraConfig extends Omit<KilatAnimationConfig, 'colors'> {
  islandCount?: number;
  batikPatterns?: boolean;
  floatingHeight?: number;
  colors?: {
    earth?: string;
    water?: string;
    gold?: string;
    emerald?: string;
    batik?: string;
  };
  animationSpeed?: number;
  mysticalParticles?: boolean;
  traditionalElements?: boolean;
}
