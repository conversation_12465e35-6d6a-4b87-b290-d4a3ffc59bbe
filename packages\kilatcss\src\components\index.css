/**
 * KilatCSS Components
 * Ready-to-use UI components with glow effects
 */

/* 🎯 Button Components */
.k-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: var(--k-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--k-transition-normal);
  text-decoration: none;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.k-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.k-btn-primary {
  background-color: var(--k-primary);
  color: var(--k-background);
  border-color: var(--k-primary);
}

.k-btn-primary:hover {
  box-shadow: 0 0 20px var(--k-primary);
  transform: translateY(-1px);
}

.k-btn-secondary {
  background-color: transparent;
  color: var(--k-secondary);
  border-color: var(--k-secondary);
}

.k-btn-secondary:hover {
  background-color: var(--k-secondary);
  color: var(--k-background);
  box-shadow: 0 0 15px var(--k-secondary);
}

.k-btn-ghost {
  background-color: transparent;
  color: var(--k-text);
  border-color: var(--k-text-muted);
}

.k-btn-ghost:hover {
  background-color: var(--k-surface);
  border-color: var(--k-text);
  box-shadow: 0 0 10px var(--k-text-muted);
}

/* Button Sizes */
.k-btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.k-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.k-btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* 🎴 Card Components */
.k-card {
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--k-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--k-shadow-md);
  transition: all var(--k-transition-normal);
}

.k-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--k-shadow-lg);
}

.k-card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--k-text);
}

.k-card-description {
  color: var(--k-text-muted);
  margin: 0.5rem 0 0 0;
}

.k-card-content {
  margin-bottom: 1rem;
}

.k-card-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 0.5rem;
}

/* 📝 Input Components */
.k-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--k-text);
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--k-radius-md);
  transition: all var(--k-transition-normal);
}

.k-input:focus {
  outline: none;
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(var(--k-primary), 0.1);
}

.k-input::placeholder {
  color: var(--k-text-muted);
}

.k-input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--k-text);
}

.k-input-error {
  border-color: var(--k-neon-red);
  box-shadow: 0 0 0 3px rgba(255, 0, 64, 0.1);
}

.k-error-message {
  font-size: 0.75rem;
  color: var(--k-neon-red);
  margin-top: 0.25rem;
}

/* 🏷️ Badge Components */
.k-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--k-radius-full);
  background-color: var(--k-primary);
  color: var(--k-background);
}

.k-badge-secondary {
  background-color: var(--k-secondary);
}

.k-badge-success {
  background-color: var(--k-neon-green);
}

.k-badge-warning {
  background-color: var(--k-neon-yellow);
  color: var(--k-background);
}

.k-badge-error {
  background-color: var(--k-neon-red);
}

.k-badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
  color: var(--k-text);
}

/* 🎛️ Modal Components */
.k-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--k-z-modal);
  animation: k-fade-in 0.2s ease-out;
}

.k-modal {
  background-color: var(--k-surface);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--k-radius-lg);
  box-shadow: var(--k-shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: k-scale-in 0.2s ease-out;
}

.k-modal-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.k-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.k-modal-close {
  background: none;
  border: none;
  color: var(--k-text-muted);
  cursor: pointer;
  font-size: 1.5rem;
  padding: 0;
  transition: color var(--k-transition-fast);
}

.k-modal-close:hover {
  color: var(--k-text);
}

.k-modal-content {
  padding: 1.5rem;
}

.k-modal-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* 🧭 Navigation Components */
.k-nav {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-nav-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--k-primary);
  text-decoration: none;
  margin-right: auto;
}

.k-nav-links {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.k-nav-link {
  color: var(--k-text-muted);
  text-decoration: none;
  transition: color var(--k-transition-fast);
  position: relative;
}

.k-nav-link:hover,
.k-nav-link.active {
  color: var(--k-primary);
}

.k-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--k-primary);
  box-shadow: 0 0 8px var(--k-primary);
}

/* 📱 Sidebar Components */
.k-sidebar {
  width: 16rem;
  height: 100vh;
  background-color: var(--k-surface);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 1rem;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  transition: transform var(--k-transition-normal);
}

.k-sidebar-hidden {
  transform: translateX(-100%);
}

.k-sidebar-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.k-sidebar-nav {
  list-style: none;
  margin: 0;
  padding: 0;
}

.k-sidebar-nav-item {
  margin-bottom: 0.5rem;
}

.k-sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--k-text-muted);
  text-decoration: none;
  border-radius: var(--k-radius-md);
  transition: all var(--k-transition-fast);
}

.k-sidebar-nav-link:hover,
.k-sidebar-nav-link.active {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--k-primary);
}

/* 🎭 Animation Keyframes */
@keyframes k-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes k-scale-in {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}
