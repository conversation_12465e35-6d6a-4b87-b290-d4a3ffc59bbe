import { createLogger } from 'kilat-utils';
import type { 
  KilatCSSConfig, 
  Theme, 
  Component, 
  Utility, 
  Animation,
  Breakpoint,
  ColorPalette,
  Typography,
  Spacing,
  CSSRule,
  CSSGenerator
} from '../types';
import { ThemeManager } from './ThemeManager';
import { ComponentManager } from './ComponentManager';
import { UtilityManager } from './UtilityManager';
import { AnimationManager } from './AnimationManager';
import { ResponsiveManager } from './ResponsiveManager';

/**
 * 🎨 KilatCSS - Advanced CSS Framework
 * Atomic CSS with themes, components, and animations
 */
export class KilatCSS implements CSSGenerator {
  private config: KilatCSSConfig;
  private themeManager: ThemeManager;
  private componentManager: ComponentManager;
  private utilityManager: UtilityManager;
  private animationManager: AnimationManager;
  private responsiveManager: ResponsiveManager;
  private logger = createLogger({ prefix: 'KilatCSS' });
  private generatedCSS = new Map<string, string>();
  private usedClasses = new Set<string>();

  constructor(config: KilatCSSConfig) {
    this.config = {
      // Default configuration
      prefix: 'k-',
      themes: ['cyberpunk', 'nusantara', 'retro'],
      defaultTheme: 'cyberpunk',
      purge: { enabled: false, content: [] },
      minify: false,
      sourcemap: false,
      autoprefixer: true,
      customProperties: true,
      darkMode: 'class',
      responsive: true,
      animations: true,
      components: true,
      utilities: true,
      ...config
    };

    // Initialize managers
    this.themeManager = new ThemeManager(this.config);
    this.componentManager = new ComponentManager(this.config);
    this.utilityManager = new UtilityManager(this.config);
    this.animationManager = new AnimationManager(this.config);
    this.responsiveManager = new ResponsiveManager(this.config);

    this.logger.info('KilatCSS initialized', { 
      themes: this.config.themes,
      defaultTheme: this.config.defaultTheme 
    });
  }

  // 🎨 Generate complete CSS
  async generate(): Promise<string> {
    this.logger.info('Generating CSS...');
    
    const startTime = Date.now();
    const cssBlocks: string[] = [];

    try {
      // 1. CSS Reset and Base Styles
      cssBlocks.push(this.generateReset());
      cssBlocks.push(this.generateBase());

      // 2. CSS Custom Properties (Variables)
      if (this.config.customProperties) {
        cssBlocks.push(this.generateCustomProperties());
      }

      // 3. Theme Styles
      for (const themeName of this.config.themes) {
        const themeCSS = await this.themeManager.generateTheme(themeName);
        cssBlocks.push(themeCSS);
      }

      // 4. Component Styles
      if (this.config.components) {
        const componentCSS = await this.componentManager.generateComponents();
        cssBlocks.push(componentCSS);
      }

      // 5. Utility Classes
      if (this.config.utilities) {
        const utilityCSS = await this.utilityManager.generateUtilities();
        cssBlocks.push(utilityCSS);
      }

      // 6. Animation Classes
      if (this.config.animations) {
        const animationCSS = await this.animationManager.generateAnimations();
        cssBlocks.push(animationCSS);
      }

      // 7. Responsive Utilities
      if (this.config.responsive) {
        const responsiveCSS = await this.responsiveManager.generateResponsive();
        cssBlocks.push(responsiveCSS);
      }

      // 8. Dark Mode Styles
      if (this.config.darkMode) {
        cssBlocks.push(this.generateDarkMode());
      }

      // Combine all CSS
      let finalCSS = cssBlocks.filter(Boolean).join('\n\n');

      // 9. Post-processing
      if (this.config.purge?.enabled) {
        finalCSS = this.purgeUnusedCSS(finalCSS);
      }

      if (this.config.autoprefixer) {
        finalCSS = this.addVendorPrefixes(finalCSS);
      }

      if (this.config.minify) {
        finalCSS = this.minifyCSS(finalCSS);
      }

      const duration = Date.now() - startTime;
      this.logger.success(`CSS generated in ${duration}ms`);

      return finalCSS;

    } catch (error) {
      this.logger.error('CSS generation failed', error);
      throw error;
    }
  }

  // 🔄 Generate CSS Reset
  private generateReset(): string {
    return `
/* KilatCSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--k-text-primary);
  background-color: var(--k-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}
`;
  }

  // 🏗️ Generate base styles
  private generateBase(): string {
    return `
/* KilatCSS Base */
:root {
  --k-font-sans: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
  --k-font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
  --k-font-size-xs: 0.75rem;
  --k-font-size-sm: 0.875rem;
  --k-font-size-base: 1rem;
  --k-font-size-lg: 1.125rem;
  --k-font-size-xl: 1.25rem;
  --k-font-size-2xl: 1.5rem;
  --k-font-size-3xl: 1.875rem;
  --k-font-size-4xl: 2.25rem;
  --k-font-size-5xl: 3rem;
  --k-font-size-6xl: 3.75rem;
  
  --k-spacing-0: 0;
  --k-spacing-1: 0.25rem;
  --k-spacing-2: 0.5rem;
  --k-spacing-3: 0.75rem;
  --k-spacing-4: 1rem;
  --k-spacing-5: 1.25rem;
  --k-spacing-6: 1.5rem;
  --k-spacing-8: 2rem;
  --k-spacing-10: 2.5rem;
  --k-spacing-12: 3rem;
  --k-spacing-16: 4rem;
  --k-spacing-20: 5rem;
  --k-spacing-24: 6rem;
  --k-spacing-32: 8rem;
  
  --k-border-radius-none: 0;
  --k-border-radius-sm: 0.125rem;
  --k-border-radius: 0.25rem;
  --k-border-radius-md: 0.375rem;
  --k-border-radius-lg: 0.5rem;
  --k-border-radius-xl: 0.75rem;
  --k-border-radius-2xl: 1rem;
  --k-border-radius-3xl: 1.5rem;
  --k-border-radius-full: 9999px;
  
  --k-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --k-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --k-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --k-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --k-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --k-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  --k-transition-duration: 150ms;
  --k-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  --k-transition-all: all var(--k-transition-duration) var(--k-transition-timing);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles */
.${this.config.prefix}focus-visible:focus-visible {
  outline: 2px solid var(--k-primary);
  outline-offset: 2px;
}

/* Screen reader only */
.${this.config.prefix}sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
`;
  }

  // 🎨 Generate custom properties for themes
  private generateCustomProperties(): string {
    const properties: string[] = [];

    // Generate for each theme
    for (const themeName of this.config.themes) {
      const theme = this.themeManager.getTheme(themeName);
      if (theme) {
        properties.push(this.generateThemeProperties(theme, themeName));
      }
    }

    return properties.join('\n\n');
  }

  // 🎨 Generate theme properties
  private generateThemeProperties(theme: Theme, themeName: string): string {
    const selector = themeName === this.config.defaultTheme 
      ? ':root' 
      : `[data-kilat-theme="${themeName}"]`;

    const properties: string[] = [];

    // Colors
    if (theme.colors) {
      Object.entries(theme.colors).forEach(([key, value]) => {
        if (typeof value === 'string') {
          properties.push(`  --k-${key}: ${value};`);
        } else if (typeof value === 'object') {
          Object.entries(value).forEach(([shade, color]) => {
            properties.push(`  --k-${key}-${shade}: ${color};`);
          });
        }
      });
    }

    // Typography
    if (theme.typography) {
      Object.entries(theme.typography).forEach(([key, value]) => {
        properties.push(`  --k-font-${key}: ${value};`);
      });
    }

    // Spacing
    if (theme.spacing) {
      Object.entries(theme.spacing).forEach(([key, value]) => {
        properties.push(`  --k-spacing-${key}: ${value};`);
      });
    }

    return `${selector} {\n${properties.join('\n')}\n}`;
  }

  // 🌙 Generate dark mode styles
  private generateDarkMode(): string {
    const darkModeSelector = this.config.darkMode === 'class' 
      ? '.dark' 
      : '@media (prefers-color-scheme: dark)';

    return `
/* Dark Mode */
${darkModeSelector} {
  --k-bg-primary: #0f0f0f;
  --k-bg-secondary: #1a1a1a;
  --k-bg-tertiary: #262626;
  --k-text-primary: #ffffff;
  --k-text-secondary: #a3a3a3;
  --k-text-tertiary: #737373;
  --k-border-primary: #404040;
  --k-border-secondary: #262626;
}

${darkModeSelector} img {
  opacity: 0.9;
}

${darkModeSelector} .${this.config.prefix}shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}
`;
  }

  // 🧹 Purge unused CSS
  private purgeUnusedCSS(css: string): string {
    if (!this.config.purge?.content?.length) {
      return css;
    }

    // Simple purging implementation
    // In production, use a proper CSS purging tool like PurgeCSS
    const usedClassesRegex = new RegExp(
      `\\.(${this.config.prefix}[a-zA-Z0-9-_]+)`,
      'g'
    );

    const usedClasses = new Set<string>();
    
    // Scan content files for used classes
    this.config.purge.content.forEach(pattern => {
      // This would scan actual files in a real implementation
      const matches = css.match(usedClassesRegex);
      if (matches) {
        matches.forEach(match => usedClasses.add(match.slice(1)));
      }
    });

    // Filter CSS to only include used classes
    const cssRules = css.split('}');
    const filteredRules = cssRules.filter(rule => {
      if (!rule.includes(this.config.prefix)) return true;
      
      const classMatch = rule.match(usedClassesRegex);
      return classMatch && classMatch.some(cls => usedClasses.has(cls.slice(1)));
    });

    return filteredRules.join('}');
  }

  // 🔧 Add vendor prefixes
  private addVendorPrefixes(css: string): string {
    // Simple autoprefixer implementation
    // In production, use the actual autoprefixer library
    return css
      .replace(/transform:/g, '-webkit-transform: $&\n  transform:')
      .replace(/transition:/g, '-webkit-transition: $&\n  transition:')
      .replace(/animation:/g, '-webkit-animation: $&\n  animation:')
      .replace(/user-select:/g, '-webkit-user-select: $&\n  -moz-user-select: $&\n  user-select:')
      .replace(/backdrop-filter:/g, '-webkit-backdrop-filter: $&\n  backdrop-filter:');
  }

  // 🗜️ Minify CSS
  private minifyCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
      .replace(/\s*{\s*/g, '{') // Remove spaces around braces
      .replace(/}\s*/g, '}') // Remove spaces after braces
      .replace(/;\s*/g, ';') // Remove spaces after semicolons
      .replace(/:\s*/g, ':') // Remove spaces after colons
      .trim();
  }

  // 🔧 Utility methods
  addClass(className: string): void {
    this.usedClasses.add(className);
  }

  removeClass(className: string): void {
    this.usedClasses.delete(className);
  }

  hasClass(className: string): boolean {
    return this.usedClasses.has(className);
  }

  getUsedClasses(): string[] {
    return Array.from(this.usedClasses);
  }

  // 🎨 Theme management
  setTheme(themeName: string): void {
    if (this.config.themes.includes(themeName)) {
      this.config.defaultTheme = themeName;
      this.logger.info(`Theme changed to: ${themeName}`);
    } else {
      this.logger.warn(`Theme not found: ${themeName}`);
    }
  }

  getTheme(themeName?: string): Theme | null {
    return this.themeManager.getTheme(themeName || this.config.defaultTheme);
  }

  addTheme(name: string, theme: Theme): void {
    this.themeManager.addTheme(name, theme);
    if (!this.config.themes.includes(name)) {
      this.config.themes.push(name);
    }
  }

  // 📊 Get generation stats
  getStats(): {
    themes: number;
    components: number;
    utilities: number;
    animations: number;
    usedClasses: number;
    totalSize: number;
  } {
    return {
      themes: this.config.themes.length,
      components: this.componentManager.getComponentCount(),
      utilities: this.utilityManager.getUtilityCount(),
      animations: this.animationManager.getAnimationCount(),
      usedClasses: this.usedClasses.size,
      totalSize: Array.from(this.generatedCSS.values()).join('').length
    };
  }

  // 🔧 Configuration
  getConfig(): KilatCSSConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<KilatCSSConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('Configuration updated');
  }
}

// 🏭 Factory function
export function createKilatCSS(config: Partial<KilatCSSConfig> = {}): KilatCSS {
  return new KilatCSS(config as KilatCSSConfig);
}

// 🎯 Default export
export default {
  KilatCSS,
  createKilatCSS
};
