/**
 * KilatCSS Asymmetric Theme ⚡
 * Bold, unconventional, and creative design
 */

[data-kilat-theme="asymetric"] {
  /* 🎨 Color Palette */
  --k-primary: #ff6b35;
  --k-secondary: #004e89;
  --k-accent: #ffd23f;
  --k-background: #f7f9fc;
  --k-surface: #ffffff;
  --k-text: #1a1a1a;
  --k-text-muted: #6b7280;
  --k-border: #e5e7eb;
  
  /* 🌈 Extended Colors */
  --k-purple: #8b5cf6;
  --k-pink: #ec4899;
  --k-green: #10b981;
  --k-blue: #3b82f6;
  --k-red: #ef4444;
  --k-orange: #f97316;
  
  /* 🔮 Glow Effects */
  --k-glow-sm: 0 0 8px rgba(255, 107, 53, 0.3);
  --k-glow-md: 0 0 16px rgba(255, 107, 53, 0.4);
  --k-glow-lg: 0 0 24px rgba(255, 107, 53, 0.5);
  --k-glow-xl: 0 0 32px rgba(255, 107, 53, 0.6);
  
  /* 📏 Asymmetric Spacing */
  --k-space-xs: 4px;
  --k-space-sm: 8px;
  --k-space-md: 16px;
  --k-space-lg: 24px;
  --k-space-xl: 32px;
  --k-space-2xl: 48px;
  --k-space-3xl: 64px;
  
  /* 🔤 Typography */
  --k-font-sans: 'Space Grotesk', 'Inter', sans-serif;
  --k-font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --k-font-display: 'Space Grotesk', 'Orbitron', sans-serif;
  
  /* 🎭 Animations */
  --k-transition-fast: 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --k-transition-normal: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --k-transition-slow: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 🌊 Asymmetric Border Radius */
  --k-radius-asymmetric-1: 4px 16px 4px 16px;
  --k-radius-asymmetric-2: 8px 24px 8px 24px;
  --k-radius-asymmetric-3: 12px 32px 12px 32px;
  --k-radius-asymmetric-4: 16px 40px 16px 40px;
  --k-radius-blob-1: 30% 70% 70% 30% / 30% 30% 70% 70%;
  --k-radius-blob-2: 40% 60% 60% 40% / 60% 30% 70% 40%;
  --k-radius-blob-3: 50% 50% 80% 20% / 25% 75% 25% 75%;
}

/* 🌙 Dark Mode */
[data-kilat-theme="asymetric"][data-kilat-mode="dark"] {
  --k-background: #0f0f23;
  --k-surface: #1a1a2e;
  --k-text: #eee6ff;
  --k-text-muted: #a0a0a0;
  --k-border: #16213e;
}

/* 🧱 Components */

/* 🔘 Asymmetric Buttons */
[data-kilat-theme="asymetric"] .k-btn {
  background: linear-gradient(135deg, var(--k-primary), var(--k-secondary));
  color: white;
  border: none;
  padding: 12px 28px 16px 20px;
  border-radius: var(--k-radius-asymmetric-2);
  font-family: var(--k-font-display);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--k-transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
  transform: skew(-5deg);
}

[data-kilat-theme="asymetric"] .k-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--k-transition-normal);
}

[data-kilat-theme="asymetric"] .k-btn:hover {
  transform: skew(-5deg) translateY(-2px) scale(1.05);
  box-shadow: var(--k-glow-lg);
}

[data-kilat-theme="asymetric"] .k-btn:hover::before {
  left: 100%;
}

[data-kilat-theme="asymetric"] .k-btn:active {
  transform: skew(-5deg) translateY(0) scale(1.02);
}

[data-kilat-theme="asymetric"] .k-btn-outline {
  background: transparent;
  color: var(--k-primary);
  border: 2px solid var(--k-primary);
  transform: skew(5deg);
}

[data-kilat-theme="asymetric"] .k-btn-outline:hover {
  background: var(--k-primary);
  color: white;
  transform: skew(5deg) translateY(-2px) scale(1.05);
}

/* 📦 Asymmetric Cards */
[data-kilat-theme="asymetric"] .k-card {
  background: var(--k-surface);
  border: 2px solid var(--k-border);
  border-radius: var(--k-radius-blob-1);
  padding: 24px 32px 28px 24px;
  transition: all var(--k-transition-normal);
  box-shadow: 
    8px 8px 0 var(--k-accent),
    16px 16px 0 var(--k-primary);
  position: relative;
  transform: rotate(-1deg);
}

[data-kilat-theme="asymetric"] .k-card:hover {
  transform: rotate(0deg) translateY(-4px);
  box-shadow: 
    12px 12px 0 var(--k-accent),
    24px 24px 0 var(--k-primary);
}

[data-kilat-theme="asymetric"] .k-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--k-primary), var(--k-accent), var(--k-secondary));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--k-transition-normal);
}

[data-kilat-theme="asymetric"] .k-card:hover::before {
  opacity: 0.1;
}

[data-kilat-theme="asymetric"] .k-card-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--k-text);
  margin: 0 0 16px 0;
  transform: skew(-2deg);
  font-family: var(--k-font-display);
}

/* 📝 Asymmetric Forms */
[data-kilat-theme="asymetric"] .k-input {
  background: var(--k-surface);
  border: 2px solid var(--k-border);
  border-radius: var(--k-radius-asymmetric-1);
  padding: 14px 20px 16px 16px;
  font-family: var(--k-font-sans);
  font-size: 14px;
  color: var(--k-text);
  transition: all var(--k-transition-normal);
  outline: none;
  width: 100%;
  transform: skew(-1deg);
}

[data-kilat-theme="asymetric"] .k-input:focus {
  border-color: var(--k-primary);
  box-shadow: var(--k-glow-sm);
  transform: skew(0deg) scale(1.02);
}

/* 🚨 Asymmetric Alerts */
[data-kilat-theme="asymetric"] .k-alert {
  padding: 16px 24px 20px 16px;
  border-radius: var(--k-radius-asymmetric-2);
  border-left: 6px solid;
  margin-bottom: 16px;
  font-size: 14px;
  position: relative;
  transform: skew(-1deg);
  transition: transform var(--k-transition-normal);
}

[data-kilat-theme="asymetric"] .k-alert:hover {
  transform: skew(0deg);
}

[data-kilat-theme="asymetric"] .k-alert-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
  border-left-color: var(--k-green);
  color: var(--k-green);
}

[data-kilat-theme="asymetric"] .k-alert-warning {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.1), rgba(249, 115, 22, 0.05));
  border-left-color: var(--k-orange);
  color: var(--k-orange);
}

[data-kilat-theme="asymetric"] .k-alert-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
  border-left-color: var(--k-red);
  color: var(--k-red);
}

/* 🎯 Asymmetric Badges */
[data-kilat-theme="asymetric"] .k-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px 8px 10px;
  font-size: 12px;
  font-weight: 600;
  border-radius: var(--k-radius-asymmetric-1);
  background: linear-gradient(135deg, var(--k-accent), var(--k-primary));
  color: var(--k-text);
  transform: skew(-3deg);
  transition: transform var(--k-transition-fast);
}

[data-kilat-theme="asymetric"] .k-badge:hover {
  transform: skew(0deg) scale(1.1);
}

/* 📊 Asymmetric Progress */
[data-kilat-theme="asymetric"] .k-progress {
  width: 100%;
  height: 12px;
  background: var(--k-border);
  border-radius: var(--k-radius-asymmetric-1);
  overflow: hidden;
  position: relative;
  transform: skew(-2deg);
}

[data-kilat-theme="asymetric"] .k-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--k-primary), var(--k-accent));
  transition: width var(--k-transition-normal);
  border-radius: inherit;
  position: relative;
}

[data-kilat-theme="asymetric"] .k-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: k-asymmetric-shine 2s infinite;
}

@keyframes k-asymmetric-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 🎛️ Asymmetric Switches */
[data-kilat-theme="asymetric"] .k-switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  transform: skew(-5deg);
}

[data-kilat-theme="asymetric"] .k-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--k-border);
  transition: var(--k-transition-normal);
  border-radius: var(--k-radius-asymmetric-1);
  border: 2px solid var(--k-border);
}

[data-kilat-theme="asymetric"] .k-switch-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background: linear-gradient(135deg, var(--k-primary), var(--k-accent));
  transition: var(--k-transition-normal);
  border-radius: var(--k-radius-asymmetric-1);
  transform: skew(5deg);
}

[data-kilat-theme="asymetric"] .k-switch input:checked + .k-switch-slider {
  background: var(--k-primary);
  border-color: var(--k-primary);
}

[data-kilat-theme="asymetric"] .k-switch input:checked + .k-switch-slider:before {
  transform: translateX(24px) skew(5deg);
}

/* 🎨 Asymmetric Utilities */
[data-kilat-theme="asymetric"] .k-skew-1 { transform: skew(-1deg); }
[data-kilat-theme="asymetric"] .k-skew-2 { transform: skew(-2deg); }
[data-kilat-theme="asymetric"] .k-skew-3 { transform: skew(-3deg); }
[data-kilat-theme="asymetric"] .k-skew-reverse-1 { transform: skew(1deg); }
[data-kilat-theme="asymetric"] .k-skew-reverse-2 { transform: skew(2deg); }
[data-kilat-theme="asymetric"] .k-skew-reverse-3 { transform: skew(3deg); }

[data-kilat-theme="asymetric"] .k-border-asymmetric-1 { border-radius: var(--k-radius-asymmetric-1); }
[data-kilat-theme="asymetric"] .k-border-asymmetric-2 { border-radius: var(--k-radius-asymmetric-2); }
[data-kilat-theme="asymetric"] .k-border-asymmetric-3 { border-radius: var(--k-radius-asymmetric-3); }
[data-kilat-theme="asymetric"] .k-border-blob-1 { border-radius: var(--k-radius-blob-1); }
[data-kilat-theme="asymetric"] .k-border-blob-2 { border-radius: var(--k-radius-blob-2); }
[data-kilat-theme="asymetric"] .k-border-blob-3 { border-radius: var(--k-radius-blob-3); }

[data-kilat-theme="asymetric"] .k-gradient-primary {
  background: linear-gradient(135deg, var(--k-primary), var(--k-secondary));
}

[data-kilat-theme="asymetric"] .k-gradient-accent {
  background: linear-gradient(135deg, var(--k-accent), var(--k-primary));
}

[data-kilat-theme="asymetric"] .k-shadow-asymmetric {
  box-shadow: 8px 8px 0 var(--k-accent);
}

[data-kilat-theme="asymetric"] .k-shadow-asymmetric-lg {
  box-shadow: 
    8px 8px 0 var(--k-accent),
    16px 16px 0 var(--k-primary);
}
