/**
 * KilatCSS Aurora Theme 🌌
 * Northern lights inspired theme with flowing gradients
 */

[data-kilat-theme="aurora"] {
  /* 🌌 Aurora Color Palette */
  --k-primary: #00d4aa;        /* Aurora Green */
  --k-secondary: #5b73ff;      /* Aurora Blue */
  --k-accent: #ff6b9d;         /* Aurora Pink */
  --k-background: #0f0f23;     /* Deep Night */
  --k-surface: #1a1a3a;       /* Dark Purple */
  --k-text: #e8f4f8;          /* Ice White */
  --k-text-muted: #a8b8c8;    /* Muted Blue */
  
  /* 🎨 Aurora Spectrum */
  --k-aurora-green: #00d4aa;
  --k-aurora-blue: #5b73ff;
  --k-aurora-purple: #9c88ff;
  --k-aurora-pink: #ff6b9d;
  --k-aurora-yellow: #ffd93d;
  --k-aurora-cyan: #6bcf7f;
  --k-aurora-violet: #a855f7;
  
  /* 🌈 Aurora Gradients */
  --k-aurora-flow: linear-gradient(135deg, #00d4aa, #5b73ff, #9c88ff, #ff6b9d);
  --k-aurora-wave: linear-gradient(90deg, #00d4aa, #5b73ff, #00d4aa);
  --k-aurora-dance: linear-gradient(45deg, #ff6b9d, #9c88ff, #5b73ff, #00d4aa);
  --k-aurora-shimmer: linear-gradient(135deg, #ffd93d, #ff6b9d, #9c88ff, #5b73ff);
  
  /* ✨ Aurora Glow Effects */
  --k-aurora-glow-sm: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  --k-aurora-glow-md: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor, 0 0 60px currentColor;
  --k-aurora-glow-lg: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;
  
  /* 🌊 Animation Timings */
  --k-aurora-slow: 8s ease-in-out infinite;
  --k-aurora-medium: 4s ease-in-out infinite;
  --k-aurora-fast: 2s ease-in-out infinite;
}

/* 🌌 Aurora Body Styling */
[data-kilat-theme="aurora"] body,
[data-kilat-theme="aurora"] .kilat {
  background: 
    radial-gradient(ellipse at top, rgba(91, 115, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom left, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(255, 107, 157, 0.1) 0%, transparent 50%),
    linear-gradient(180deg, #0f0f23 0%, #1a1a3a 100%);
  color: var(--k-text);
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Aurora Background Animation */
[data-kilat-theme="aurora"] body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--k-aurora-flow);
  opacity: 0.03;
  animation: aurora-flow var(--k-aurora-slow);
  z-index: -1;
}

/* 🌟 Aurora Text Effects */
[data-kilat-theme="aurora"] .k-text-aurora {
  background: var(--k-aurora-flow);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 400% 400%;
  animation: aurora-text var(--k-aurora-medium);
  font-weight: 700;
}

[data-kilat-theme="aurora"] .k-text-glow-aurora {
  color: var(--k-aurora-green);
  text-shadow: var(--k-aurora-glow-md);
  animation: aurora-pulse var(--k-aurora-fast) alternate;
}

[data-kilat-theme="aurora"] .k-text-shimmer {
  background: var(--k-aurora-shimmer);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: aurora-shimmer 3s linear infinite;
}

/* 🎭 Aurora Animations */
@keyframes aurora-flow {
  0%, 100% { 
    background-position: 0% 50%; 
    transform: rotate(0deg) scale(1);
  }
  25% { 
    background-position: 100% 50%; 
    transform: rotate(90deg) scale(1.1);
  }
  50% { 
    background-position: 100% 100%; 
    transform: rotate(180deg) scale(1);
  }
  75% { 
    background-position: 0% 100%; 
    transform: rotate(270deg) scale(1.1);
  }
}

@keyframes aurora-text {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes aurora-pulse {
  0% { 
    text-shadow: var(--k-aurora-glow-sm);
    filter: brightness(1);
  }
  100% { 
    text-shadow: var(--k-aurora-glow-lg);
    filter: brightness(1.3);
  }
}

@keyframes aurora-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes aurora-wave {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(0) rotate(0deg); }
  75% { transform: translateY(10px) rotate(-1deg); }
}

@keyframes aurora-float {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-20px) scale(1.05); }
}

/* 🎯 Aurora Buttons */
[data-kilat-theme="aurora"] .k-btn-aurora {
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  border: 2px solid transparent;
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: aurora-text var(--k-aurora-medium);
  box-shadow: 
    0 8px 32px rgba(0, 212, 170, 0.2),
    0 0 20px rgba(91, 115, 255, 0.1);
}

[data-kilat-theme="aurora"] .k-btn-aurora::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

[data-kilat-theme="aurora"] .k-btn-aurora:hover::before {
  left: 100%;
}

[data-kilat-theme="aurora"] .k-btn-aurora:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 12px 40px rgba(0, 212, 170, 0.3),
    0 0 30px rgba(91, 115, 255, 0.2),
    0 0 40px rgba(255, 107, 157, 0.1);
  animation: aurora-wave 0.6s ease-in-out;
}

/* 🎮 Aurora Cards */
[data-kilat-theme="aurora"] .k-card-aurora {
  background: 
    linear-gradient(135deg, rgba(26, 26, 58, 0.9), rgba(15, 15, 35, 0.9)),
    var(--k-aurora-flow);
  background-size: 100% 100%, 400% 400%;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  animation: aurora-text 8s ease-in-out infinite;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 212, 170, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
}

[data-kilat-theme="aurora"] .k-card-aurora::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--k-aurora-shimmer);
  background-size: 200% 200%;
  animation: aurora-shimmer 3s linear infinite;
}

[data-kilat-theme="aurora"] .k-card-aurora:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(0, 212, 170, 0.4);
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(0, 212, 170, 0.2),
    0 0 60px rgba(91, 115, 255, 0.1),
    inset 0 0 30px rgba(255, 255, 255, 0.1);
  animation: aurora-float 1s ease-in-out;
}

/* 🔍 Aurora Inputs */
[data-kilat-theme="aurora"] .k-input-aurora {
  background: rgba(26, 26, 58, 0.8);
  border: 2px solid rgba(0, 212, 170, 0.3);
  color: var(--k-text);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-family: inherit;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 
    inset 0 0 20px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(0, 212, 170, 0.1);
}

[data-kilat-theme="aurora"] .k-input-aurora:focus {
  outline: none;
  border-color: var(--k-aurora-green);
  background: rgba(26, 26, 58, 0.9);
  box-shadow: 
    0 0 20px rgba(0, 212, 170, 0.3),
    0 0 30px rgba(91, 115, 255, 0.1),
    inset 0 0 20px rgba(0, 212, 170, 0.05);
  animation: aurora-pulse 0.5s ease-in-out;
}

[data-kilat-theme="aurora"] .k-input-aurora::placeholder {
  color: var(--k-text-muted);
  opacity: 0.7;
}

/* 🎚️ Aurora Progress Bar */
[data-kilat-theme="aurora"] .k-progress-aurora {
  width: 100%;
  height: 8px;
  background: rgba(26, 26, 58, 0.8);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

[data-kilat-theme="aurora"] .k-progress-aurora-fill {
  height: 100%;
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  border-radius: 4px;
  position: relative;
  animation: aurora-text var(--k-aurora-medium);
  box-shadow: 
    0 0 10px rgba(0, 212, 170, 0.5),
    0 0 20px rgba(91, 115, 255, 0.3);
}

[data-kilat-theme="aurora"] .k-progress-aurora-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: aurora-shimmer 2s linear infinite;
}

/* 🌐 Aurora Scrollbar */
[data-kilat-theme="aurora"] ::-webkit-scrollbar {
  width: 8px;
}

[data-kilat-theme="aurora"] ::-webkit-scrollbar-track {
  background: rgba(26, 26, 58, 0.5);
  border-radius: 4px;
}

[data-kilat-theme="aurora"] ::-webkit-scrollbar-thumb {
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  border-radius: 4px;
  animation: aurora-text var(--k-aurora-medium);
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

[data-kilat-theme="aurora"] ::-webkit-scrollbar-thumb:hover {
  box-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
}

/* 🎨 Aurora Utilities */
[data-kilat-theme="aurora"] .k-bg-aurora { 
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  animation: aurora-text var(--k-aurora-medium);
}

[data-kilat-theme="aurora"] .k-border-aurora { 
  border: 2px solid var(--k-aurora-green);
  box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
}

[data-kilat-theme="aurora"] .k-glow-aurora {
  box-shadow: var(--k-aurora-glow-md);
  animation: aurora-pulse var(--k-aurora-fast) alternate;
}

/* 🏷️ Aurora Tags */
[data-kilat-theme="aurora"] .k-tag-aurora {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  color: white;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: aurora-text var(--k-aurora-medium);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);
}

/* 🎯 Aurora Icon Buttons */
[data-kilat-theme="aurora"] .k-icon-btn-aurora {
  width: 48px;
  height: 48px;
  background: var(--k-aurora-flow);
  background-size: 400% 400%;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
  animation: aurora-text var(--k-aurora-medium);
  box-shadow: 
    0 8px 24px rgba(0, 212, 170, 0.2),
    0 0 20px rgba(91, 115, 255, 0.1);
}

[data-kilat-theme="aurora"] .k-icon-btn-aurora:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow: 
    0 12px 32px rgba(0, 212, 170, 0.3),
    0 0 30px rgba(91, 115, 255, 0.2);
  animation: aurora-wave 0.6s ease-in-out;
}
