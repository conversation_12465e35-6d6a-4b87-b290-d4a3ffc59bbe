/**
 * KilatCSS Carbon Theme ⚫
 * IBM Carbon Design System inspired dark theme
 */

[data-kilat-theme="carbon"] {
  /* ⚫ Carbon Color Palette */
  --k-primary: #0f62fe;        /* Blue 60 */
  --k-secondary: #ee5396;      /* Magenta 50 */
  --k-accent: #42be65;         /* Green 50 */
  --k-background: #161616;     /* Gray 100 */
  --k-surface: #262626;        /* Gray 90 */
  --k-text: #f4f4f4;          /* Gray 10 */
  --k-text-muted: #c6c6c6;    /* Gray 30 */
  
  /* 🎨 Carbon Gray Scale */
  --k-gray-10: #f4f4f4;
  --k-gray-20: #e0e0e0;
  --k-gray-30: #c6c6c6;
  --k-gray-40: #a8a8a8;
  --k-gray-50: #8d8d8d;
  --k-gray-60: #6f6f6f;
  --k-gray-70: #525252;
  --k-gray-80: #393939;
  --k-gray-90: #262626;
  --k-gray-100: #161616;
  
  /* 🔵 Carbon Blue Scale */
  --k-blue-10: #edf5ff;
  --k-blue-20: #d0e2ff;
  --k-blue-30: #a6c8ff;
  --k-blue-40: #78a9ff;
  --k-blue-50: #4589ff;
  --k-blue-60: #0f62fe;
  --k-blue-70: #0043ce;
  --k-blue-80: #002d9c;
  --k-blue-90: #001d6c;
  --k-blue-100: #001141;
  
  /* 🟢 Carbon Support Colors */
  --k-support-error: #fa4d56;
  --k-support-warning: #f1c21b;
  --k-support-success: #42be65;
  --k-support-info: #4589ff;
  
  /* 📦 Carbon Layers */
  --k-layer-01: #262626;
  --k-layer-02: #393939;
  --k-layer-03: #525252;
  --k-layer-accent-01: #393939;
  --k-layer-accent-02: #4c4c4c;
  --k-layer-accent-03: #6f6f6f;
  
  /* 🔲 Carbon Borders */
  --k-border-subtle: #393939;
  --k-border-strong: #6f6f6f;
  --k-border-inverse: #161616;
  --k-border-interactive: #4589ff;
  
  /* 🎭 Carbon Motion */
  --k-motion-productive: cubic-bezier(0.2, 0, 0.38, 0.9);
  --k-motion-expressive: cubic-bezier(0.4, 0.14, 0.3, 1);
  --k-motion-entrance: cubic-bezier(0, 0, 0.38, 0.9);
  --k-motion-exit: cubic-bezier(0.2, 0, 1, 0.9);
  
  /* ⏱️ Carbon Duration */
  --k-duration-fast-01: 70ms;
  --k-duration-fast-02: 110ms;
  --k-duration-moderate-01: 150ms;
  --k-duration-moderate-02: 240ms;
  --k-duration-slow-01: 400ms;
  --k-duration-slow-02: 700ms;
}

/* ⚫ Carbon Body Styling */
[data-kilat-theme="carbon"] body,
[data-kilat-theme="carbon"] .kilat {
  background-color: var(--k-background);
  color: var(--k-text);
  font-family: 'IBM Plex Sans', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 🎯 Carbon Buttons */
[data-kilat-theme="carbon"] .k-btn-carbon {
  background-color: var(--k-primary);
  color: var(--k-text);
  border: none;
  padding: 0.875rem 1rem;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.125rem;
  cursor: pointer;
  transition: all var(--k-duration-fast-02) var(--k-motion-productive);
  min-height: 3rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="carbon"] .k-btn-carbon:hover {
  background-color: var(--k-blue-70);
}

[data-kilat-theme="carbon"] .k-btn-carbon:active {
  background-color: var(--k-blue-80);
}

[data-kilat-theme="carbon"] .k-btn-carbon:focus {
  outline: 2px solid var(--k-primary);
  outline-offset: -2px;
}

/* Carbon Button Variants */
[data-kilat-theme="carbon"] .k-btn-secondary {
  background-color: var(--k-layer-02);
  border: 1px solid var(--k-primary);
  color: var(--k-primary);
}

[data-kilat-theme="carbon"] .k-btn-secondary:hover {
  background-color: var(--k-layer-03);
}

[data-kilat-theme="carbon"] .k-btn-tertiary {
  background-color: transparent;
  border: 1px solid var(--k-primary);
  color: var(--k-primary);
}

[data-kilat-theme="carbon"] .k-btn-tertiary:hover {
  background-color: var(--k-primary);
  color: var(--k-text);
}

[data-kilat-theme="carbon"] .k-btn-ghost {
  background-color: transparent;
  border: none;
  color: var(--k-primary);
  padding: 0.875rem 1rem;
}

[data-kilat-theme="carbon"] .k-btn-ghost:hover {
  background-color: var(--k-layer-01);
}

[data-kilat-theme="carbon"] .k-btn-danger {
  background-color: var(--k-support-error);
  color: var(--k-text);
}

[data-kilat-theme="carbon"] .k-btn-danger:hover {
  background-color: #e5393f;
}

/* 🎮 Carbon Cards */
[data-kilat-theme="carbon"] .k-card-carbon {
  background-color: var(--k-layer-01);
  border: 1px solid var(--k-border-subtle);
  padding: 1rem;
  transition: all var(--k-duration-moderate-01) var(--k-motion-productive);
}

[data-kilat-theme="carbon"] .k-card-carbon:hover {
  background-color: var(--k-layer-02);
}

[data-kilat-theme="carbon"] .k-card-carbon-elevated {
  background-color: var(--k-layer-02);
  border: 1px solid var(--k-border-strong);
}

/* 🔍 Carbon Inputs */
[data-kilat-theme="carbon"] .k-input-carbon {
  background-color: var(--k-layer-01);
  border: none;
  border-bottom: 1px solid var(--k-border-strong);
  color: var(--k-text);
  padding: 0.875rem 1rem;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  line-height: 1.125rem;
  transition: all var(--k-duration-fast-02) var(--k-motion-productive);
  outline: none;
  width: 100%;
  min-height: 2.5rem;
}

[data-kilat-theme="carbon"] .k-input-carbon:focus {
  border-bottom-color: var(--k-primary);
  border-bottom-width: 2px;
  outline: 2px solid var(--k-primary);
  outline-offset: -2px;
}

[data-kilat-theme="carbon"] .k-input-carbon::placeholder {
  color: var(--k-text-muted);
}

[data-kilat-theme="carbon"] .k-input-carbon:invalid {
  border-bottom-color: var(--k-support-error);
}

/* Carbon Text Area */
[data-kilat-theme="carbon"] .k-textarea-carbon {
  background-color: var(--k-layer-01);
  border: 1px solid var(--k-border-strong);
  color: var(--k-text);
  padding: 0.875rem 1rem;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  line-height: 1.125rem;
  resize: vertical;
  min-height: 5rem;
}

[data-kilat-theme="carbon"] .k-textarea-carbon:focus {
  border-color: var(--k-primary);
  outline: 2px solid var(--k-primary);
  outline-offset: -2px;
}

/* 📋 Carbon Lists */
[data-kilat-theme="carbon"] .k-list-carbon {
  background-color: var(--k-layer-01);
  border: 1px solid var(--k-border-subtle);
}

[data-kilat-theme="carbon"] .k-list-item-carbon {
  padding: 0.875rem 1rem;
  border-bottom: 1px solid var(--k-border-subtle);
  cursor: pointer;
  transition: background-color var(--k-duration-fast-02) var(--k-motion-productive);
  display: flex;
  align-items: center;
  min-height: 3rem;
}

[data-kilat-theme="carbon"] .k-list-item-carbon:hover {
  background-color: var(--k-layer-02);
}

[data-kilat-theme="carbon"] .k-list-item-carbon:active {
  background-color: var(--k-layer-03);
}

[data-kilat-theme="carbon"] .k-list-item-carbon:last-child {
  border-bottom: none;
}

/* 🎚️ Carbon Toggle */
[data-kilat-theme="carbon"] .k-toggle-carbon {
  position: relative;
  width: 3rem;
  height: 1.5rem;
  background-color: var(--k-border-strong);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: background-color var(--k-duration-fast-02) var(--k-motion-productive);
}

[data-kilat-theme="carbon"] .k-toggle-carbon::after {
  content: '';
  position: absolute;
  top: 0.1875rem;
  left: 0.1875rem;
  width: 1.125rem;
  height: 1.125rem;
  background-color: var(--k-text);
  border-radius: 50%;
  transition: transform var(--k-duration-moderate-01) var(--k-motion-productive);
}

[data-kilat-theme="carbon"] .k-toggle-carbon.active {
  background-color: var(--k-primary);
}

[data-kilat-theme="carbon"] .k-toggle-carbon.active::after {
  transform: translateX(1.5rem);
}

/* 📊 Carbon Progress Bar */
[data-kilat-theme="carbon"] .k-progress-carbon {
  width: 100%;
  height: 0.5rem;
  background-color: var(--k-layer-02);
  overflow: hidden;
}

[data-kilat-theme="carbon"] .k-progress-carbon-fill {
  height: 100%;
  background-color: var(--k-primary);
  transition: width var(--k-duration-moderate-02) var(--k-motion-productive);
}

/* 🔔 Carbon Notifications */
[data-kilat-theme="carbon"] .k-notification-carbon {
  background-color: var(--k-layer-02);
  border-left: 3px solid var(--k-primary);
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

[data-kilat-theme="carbon"] .k-notification-error {
  border-left-color: var(--k-support-error);
}

[data-kilat-theme="carbon"] .k-notification-warning {
  border-left-color: var(--k-support-warning);
}

[data-kilat-theme="carbon"] .k-notification-success {
  border-left-color: var(--k-support-success);
}

[data-kilat-theme="carbon"] .k-notification-info {
  border-left-color: var(--k-support-info);
}

/* 🌐 Carbon Scrollbar */
[data-kilat-theme="carbon"] ::-webkit-scrollbar {
  width: 0.75rem;
}

[data-kilat-theme="carbon"] ::-webkit-scrollbar-track {
  background: var(--k-layer-01);
}

[data-kilat-theme="carbon"] ::-webkit-scrollbar-thumb {
  background: var(--k-border-strong);
  border-radius: 0.375rem;
}

[data-kilat-theme="carbon"] ::-webkit-scrollbar-thumb:hover {
  background: var(--k-gray-60);
}

/* 🎨 Carbon Utilities */
[data-kilat-theme="carbon"] .k-layer-01 { background-color: var(--k-layer-01); }
[data-kilat-theme="carbon"] .k-layer-02 { background-color: var(--k-layer-02); }
[data-kilat-theme="carbon"] .k-layer-03 { background-color: var(--k-layer-03); }

[data-kilat-theme="carbon"] .k-border-subtle { border: 1px solid var(--k-border-subtle); }
[data-kilat-theme="carbon"] .k-border-strong { border: 1px solid var(--k-border-strong); }

[data-kilat-theme="carbon"] .k-text-primary { color: var(--k-primary); }
[data-kilat-theme="carbon"] .k-text-secondary { color: var(--k-secondary); }
[data-kilat-theme="carbon"] .k-text-error { color: var(--k-support-error); }
[data-kilat-theme="carbon"] .k-text-warning { color: var(--k-support-warning); }
[data-kilat-theme="carbon"] .k-text-success { color: var(--k-support-success); }
[data-kilat-theme="carbon"] .k-text-info { color: var(--k-support-info); }

/* 📱 Carbon Typography */
[data-kilat-theme="carbon"] .k-heading-01 { font-size: 0.875rem; font-weight: 600; line-height: 1.125rem; }
[data-kilat-theme="carbon"] .k-heading-02 { font-size: 1rem; font-weight: 600; line-height: 1.375rem; }
[data-kilat-theme="carbon"] .k-heading-03 { font-size: 1.25rem; font-weight: 400; line-height: 1.625rem; }
[data-kilat-theme="carbon"] .k-heading-04 { font-size: 1.75rem; font-weight: 400; line-height: 2.25rem; }
[data-kilat-theme="carbon"] .k-heading-05 { font-size: 2rem; font-weight: 300; line-height: 2.5rem; }
[data-kilat-theme="carbon"] .k-heading-06 { font-size: 2.625rem; font-weight: 300; line-height: 3.125rem; }
[data-kilat-theme="carbon"] .k-heading-07 { font-size: 3.375rem; font-weight: 300; line-height: 4rem; }

[data-kilat-theme="carbon"] .k-body-compact-01 { font-size: 0.875rem; font-weight: 400; line-height: 1.125rem; }
[data-kilat-theme="carbon"] .k-body-compact-02 { font-size: 1rem; font-weight: 400; line-height: 1.375rem; }
[data-kilat-theme="carbon"] .k-body-01 { font-size: 0.875rem; font-weight: 400; line-height: 1.25rem; }
[data-kilat-theme="carbon"] .k-body-02 { font-size: 1rem; font-weight: 400; line-height: 1.5rem; }

[data-kilat-theme="carbon"] .k-code-01 { font-family: 'IBM Plex Mono', monospace; font-size: 0.75rem; line-height: 1rem; }
[data-kilat-theme="carbon"] .k-code-02 { font-family: 'IBM Plex Mono', monospace; font-size: 0.875rem; line-height: 1.25rem; }

[data-kilat-theme="carbon"] .k-label-01 { font-size: 0.75rem; font-weight: 400; line-height: 1rem; }
[data-kilat-theme="carbon"] .k-label-02 { font-size: 0.875rem; font-weight: 400; line-height: 1.125rem; }

[data-kilat-theme="carbon"] .k-helper-text-01 { font-size: 0.75rem; line-height: 1rem; font-style: italic; }
[data-kilat-theme="carbon"] .k-helper-text-02 { font-size: 0.875rem; line-height: 1.125rem; font-style: italic; }
