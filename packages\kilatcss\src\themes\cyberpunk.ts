import type { Theme } from '../types';

/**
 * 🌆 Cyberpunk Theme - Futuristic neon-lit interface
 * Inspired by cyberpunk aesthetics with glowing effects
 */
export const cyberpunkTheme: Theme = {
  name: 'cyberpunk',
  displayName: 'Cyberpunk',
  description: 'Futuristic neon-lit interface with glowing effects',
  category: 'futuristic',
  
  colors: {
    // Primary neon cyan
    primary: {
      50: '#ecfeff',
      100: '#cffafe',
      200: '#a5f3fc',
      300: '#67e8f9',
      400: '#22d3ee',
      500: '#00ffff', // Main neon cyan
      600: '#0891b2',
      700: '#0e7490',
      800: '#155e75',
      900: '#164e63',
      950: '#083344'
    },
    
    // Secondary neon pink/magenta
    secondary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#ff0080', // Main neon pink
      600: '#db2777',
      700: '#be185d',
      800: '#9d174d',
      900: '#831843',
      950: '#500724'
    },
    
    // Accent neon green
    accent: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#00ff41', // Neon green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // Warning neon orange
    warning: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#ff6600', // Neon orange
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
      950: '#431407'
    },
    
    // Error neon red
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ff0040', // Neon red
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    
    // Success (same as accent)
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#00ff41',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // Neutral dark colors
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a'
    },
    
    // Background colors
    background: {
      primary: '#0a0a0a',
      secondary: '#171717',
      tertiary: '#262626',
      elevated: '#404040'
    },
    
    // Text colors
    text: {
      primary: '#ffffff',
      secondary: '#a3a3a3',
      tertiary: '#737373',
      inverse: '#0a0a0a',
      accent: '#00ffff'
    },
    
    // Border colors
    border: {
      primary: '#404040',
      secondary: '#262626',
      accent: '#00ffff',
      glow: 'rgba(0, 255, 255, 0.5)'
    },
    
    // Special effect colors
    glow: {
      cyan: 'rgba(0, 255, 255, 0.8)',
      pink: 'rgba(255, 0, 128, 0.8)',
      green: 'rgba(0, 255, 65, 0.8)',
      orange: 'rgba(255, 102, 0, 0.8)',
      red: 'rgba(255, 0, 64, 0.8)'
    }
  },
  
  typography: {
    fontFamily: {
      sans: ['Orbitron', 'system-ui', 'sans-serif'],
      mono: ['Fira Code', 'Monaco', 'Consolas', 'monospace'],
      display: ['Exo 2', 'Orbitron', 'sans-serif']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem'
    },
    fontWeight: {
      thin: '100',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    },
    lineHeight: {
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2'
    },
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    }
  },
  
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
  },
  
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px'
  },
  
  boxShadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
    // Cyberpunk glow effects
    'glow-sm': '0 0 5px rgba(0, 255, 255, 0.5)',
    'glow': '0 0 10px rgba(0, 255, 255, 0.6)',
    'glow-md': '0 0 15px rgba(0, 255, 255, 0.7)',
    'glow-lg': '0 0 20px rgba(0, 255, 255, 0.8)',
    'glow-xl': '0 0 30px rgba(0, 255, 255, 0.9)',
    'glow-pink': '0 0 15px rgba(255, 0, 128, 0.7)',
    'glow-green': '0 0 15px rgba(0, 255, 65, 0.7)',
    'glow-orange': '0 0 15px rgba(255, 102, 0, 0.7)'
  },
  
  animation: {
    // Cyberpunk-specific animations
    'pulse-glow': 'pulse-glow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    'flicker': 'flicker 0.15s infinite linear',
    'scan-line': 'scan-line 2s linear infinite',
    'matrix-rain': 'matrix-rain 20s linear infinite',
    'neon-flicker': 'neon-flicker 1.5s ease-in-out infinite alternate',
    'data-stream': 'data-stream 3s linear infinite',
    'hologram': 'hologram 4s ease-in-out infinite',
    'glitch': 'glitch 0.3s ease-in-out infinite'
  },
  
  keyframes: {
    'pulse-glow': {
      '0%, 100%': {
        boxShadow: '0 0 5px rgba(0, 255, 255, 0.5)',
        opacity: '1'
      },
      '50%': {
        boxShadow: '0 0 20px rgba(0, 255, 255, 0.9)',
        opacity: '0.8'
      }
    },
    'flicker': {
      '0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100%': {
        opacity: '0.99'
      },
      '20%, 21.999%, 63%, 63.999%, 65%, 69.999%': {
        opacity: '0.4'
      }
    },
    'scan-line': {
      '0%': {
        transform: 'translateY(-100%)'
      },
      '100%': {
        transform: 'translateY(100vh)'
      }
    },
    'matrix-rain': {
      '0%': {
        transform: 'translateY(-100%)'
      },
      '100%': {
        transform: 'translateY(100vh)'
      }
    },
    'neon-flicker': {
      '0%': {
        textShadow: '0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.8), 0 0 15px rgba(0, 255, 255, 0.8)'
      },
      '100%': {
        textShadow: '0 0 2px rgba(0, 255, 255, 0.8), 0 0 5px rgba(0, 255, 255, 0.8), 0 0 8px rgba(0, 255, 255, 0.8)'
      }
    },
    'data-stream': {
      '0%': {
        transform: 'translateX(-100%)',
        opacity: '0'
      },
      '50%': {
        opacity: '1'
      },
      '100%': {
        transform: 'translateX(100%)',
        opacity: '0'
      }
    },
    'hologram': {
      '0%, 100%': {
        opacity: '1',
        transform: 'translateY(0px)'
      },
      '50%': {
        opacity: '0.8',
        transform: 'translateY(-2px)'
      }
    },
    'glitch': {
      '0%': {
        transform: 'translate(0)'
      },
      '20%': {
        transform: 'translate(-2px, 2px)'
      },
      '40%': {
        transform: 'translate(-2px, -2px)'
      },
      '60%': {
        transform: 'translate(2px, 2px)'
      },
      '80%': {
        transform: 'translate(2px, -2px)'
      },
      '100%': {
        transform: 'translate(0)'
      }
    }
  },
  
  // Custom CSS properties specific to cyberpunk theme
  customProperties: {
    '--k-cyberpunk-glow-primary': 'rgba(0, 255, 255, 0.8)',
    '--k-cyberpunk-glow-secondary': 'rgba(255, 0, 128, 0.8)',
    '--k-cyberpunk-scan-line': 'linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.8), transparent)',
    '--k-cyberpunk-grid': 'linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)',
    '--k-cyberpunk-noise': 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 256 256\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.9\' numOctaves=\'4\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\' opacity=\'0.05\'/%3E%3C/svg%3E")'
  },
  
  // Theme-specific component variants
  components: {
    button: {
      cyberpunk: {
        base: 'relative overflow-hidden border border-cyan-500 bg-transparent text-cyan-400 hover:text-black hover:bg-cyan-400 transition-all duration-300',
        glow: 'shadow-glow hover:shadow-glow-lg',
        scan: 'before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-cyan-400 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700'
      }
    },
    card: {
      cyberpunk: {
        base: 'bg-black/80 border border-cyan-500/30 backdrop-blur-sm',
        glow: 'shadow-glow-sm hover:shadow-glow',
        grid: 'bg-[image:var(--k-cyberpunk-grid)] bg-[size:20px_20px]'
      }
    },
    input: {
      cyberpunk: {
        base: 'bg-black/50 border border-cyan-500/50 text-cyan-400 placeholder-cyan-600 focus:border-cyan-400 focus:shadow-glow-sm',
        glow: 'focus:shadow-glow'
      }
    }
  }
};

export default cyberpunkTheme;
