/**
 * <PERSON><PERSON><PERSON><PERSON> Dana Theme 💙
 * Indonesian fintech Dana app inspired theme
 */

[data-kilat-theme="dana"] {
  /* 💙 Dana Color Palette */
  --k-primary: #118EEA;        /* Dana Blue */
  --k-secondary: #FF6B35;      /* Dana Orange */
  --k-accent: #00D4AA;         /* Dana <PERSON>l */
  --k-background: #F8FAFC;     /* Light Gray */
  --k-surface: #FFFFFF;        /* White */
  --k-text: #1A202C;          /* Dark Gray */
  --k-text-muted: #718096;    /* Medium Gray */
  
  /* 🎨 Dana Blue Scale */
  --k-dana-blue-50: #EBF8FF;
  --k-dana-blue-100: #BEE3F8;
  --k-dana-blue-200: #90CDF4;
  --k-dana-blue-300: #63B3ED;
  --k-dana-blue-400: #4299E1;
  --k-dana-blue-500: #118EEA;
  --k-dana-blue-600: #2B77E6;
  --k-dana-blue-700: #2C5AA0;
  --k-dana-blue-800: #2A4365;
  --k-dana-blue-900: #1A365D;
  
  /* 🧡 Dana Orange Scale */
  --k-dana-orange-50: #FFFAF0;
  --k-dana-orange-100: #FEEBC8;
  --k-dana-orange-200: #FBD38D;
  --k-dana-orange-300: #F6AD55;
  --k-dana-orange-400: #ED8936;
  --k-dana-orange-500: #FF6B35;
  --k-dana-orange-600: #DD6B20;
  --k-dana-orange-700: #C05621;
  --k-dana-orange-800: #9C4221;
  --k-dana-orange-900: #7B341E;
  
  /* 💚 Dana Teal Scale */
  --k-dana-teal-50: #E6FFFA;
  --k-dana-teal-100: #B2F5EA;
  --k-dana-teal-200: #81E6D9;
  --k-dana-teal-300: #4FD1C7;
  --k-dana-teal-400: #38B2AC;
  --k-dana-teal-500: #00D4AA;
  --k-dana-teal-600: #319795;
  --k-dana-teal-700: #2C7A7B;
  --k-dana-teal-800: #285E61;
  --k-dana-teal-900: #234E52;
  
  /* 🎯 Dana Functional Colors */
  --k-dana-success: #48BB78;
  --k-dana-warning: #ED8936;
  --k-dana-error: #F56565;
  --k-dana-info: #4299E1;
  
  /* 📦 Dana Shadows */
  --k-dana-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --k-dana-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --k-dana-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --k-dana-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  
  /* 🎭 Dana Motion */
  --k-dana-transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --k-dana-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 🌙 Dana Dark Mode */
[data-kilat-theme="dana"][data-kilat-mode="dark"] {
  --k-background: #1A202C;
  --k-surface: #2D3748;
  --k-text: #F7FAFC;
  --k-text-muted: #A0AEC0;
}

/* 💙 Dana Body Styling */
[data-kilat-theme="dana"] body,
[data-kilat-theme="dana"] .kilat {
  background: var(--k-background);
  color: var(--k-text);
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 🎯 Dana Buttons */
[data-kilat-theme="dana"] .k-btn-dana {
  background: var(--k-primary);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--k-dana-transition);
  box-shadow: var(--k-dana-shadow-md);
  min-height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="dana"] .k-btn-dana:hover {
  background: var(--k-dana-blue-600);
  transform: translateY(-2px);
  box-shadow: var(--k-dana-shadow-lg);
}

[data-kilat-theme="dana"] .k-btn-dana:active {
  transform: translateY(0);
  box-shadow: var(--k-dana-shadow-sm);
}

/* Dana Button Variants */
[data-kilat-theme="dana"] .k-btn-secondary {
  background: var(--k-secondary);
  color: white;
}

[data-kilat-theme="dana"] .k-btn-secondary:hover {
  background: var(--k-dana-orange-600);
}

[data-kilat-theme="dana"] .k-btn-outline {
  background: transparent;
  color: var(--k-primary);
  border: 2px solid var(--k-primary);
  box-shadow: none;
}

[data-kilat-theme="dana"] .k-btn-outline:hover {
  background: var(--k-primary);
  color: white;
}

[data-kilat-theme="dana"] .k-btn-ghost {
  background: transparent;
  color: var(--k-primary);
  box-shadow: none;
}

[data-kilat-theme="dana"] .k-btn-ghost:hover {
  background: var(--k-dana-blue-50);
}

/* 🎮 Dana Cards */
[data-kilat-theme="dana"] .k-card-dana {
  background: var(--k-surface);
  border-radius: 16px;
  padding: 20px;
  box-shadow: var(--k-dana-shadow-md);
  transition: all var(--k-dana-transition);
  border: 1px solid rgba(17, 142, 234, 0.1);
}

[data-kilat-theme="dana"] .k-card-dana:hover {
  transform: translateY(-4px);
  box-shadow: var(--k-dana-shadow-xl);
  border-color: rgba(17, 142, 234, 0.2);
}

/* Dana Feature Card */
[data-kilat-theme="dana"] .k-card-feature {
  background: linear-gradient(135deg, var(--k-primary), var(--k-dana-blue-600));
  color: white;
  border: none;
}

[data-kilat-theme="dana"] .k-card-feature:hover {
  background: linear-gradient(135deg, var(--k-dana-blue-600), var(--k-dana-blue-700));
}

/* Dana Balance Card */
[data-kilat-theme="dana"] .k-card-balance {
  background: linear-gradient(135deg, var(--k-accent), var(--k-dana-teal-600));
  color: white;
  text-align: center;
  padding: 32px 20px;
}

/* 🔍 Dana Inputs */
[data-kilat-theme="dana"] .k-input-dana {
  background: var(--k-surface);
  border: 2px solid rgba(17, 142, 234, 0.1);
  color: var(--k-text);
  padding: 12px 16px;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  transition: all var(--k-dana-transition);
  outline: none;
  width: 100%;
  min-height: 48px;
}

[data-kilat-theme="dana"] .k-input-dana:focus {
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(17, 142, 234, 0.1);
}

[data-kilat-theme="dana"] .k-input-dana::placeholder {
  color: var(--k-text-muted);
}

/* Dana Search Input */
[data-kilat-theme="dana"] .k-input-search {
  background: var(--k-dana-blue-50);
  border: none;
  border-radius: 24px;
  padding: 12px 20px 12px 48px;
  position: relative;
}

[data-kilat-theme="dana"] .k-input-search::before {
  content: '🔍';
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--k-text-muted);
}

/* 💰 Dana Amount Input */
[data-kilat-theme="dana"] .k-input-amount {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: var(--k-primary);
  border: none;
  background: transparent;
  padding: 20px;
}

[data-kilat-theme="dana"] .k-input-amount::before {
  content: 'Rp ';
  color: var(--k-text-muted);
  font-size: 24px;
}

/* 📋 Dana Lists */
[data-kilat-theme="dana"] .k-list-dana {
  background: var(--k-surface);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--k-dana-shadow-sm);
}

[data-kilat-theme="dana"] .k-list-item-dana {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(17, 142, 234, 0.05);
  cursor: pointer;
  transition: all var(--k-dana-transition);
  display: flex;
  align-items: center;
  gap: 12px;
}

[data-kilat-theme="dana"] .k-list-item-dana:hover {
  background: var(--k-dana-blue-50);
}

[data-kilat-theme="dana"] .k-list-item-dana:last-child {
  border-bottom: none;
}

/* Dana Transaction Item */
[data-kilat-theme="dana"] .k-transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

[data-kilat-theme="dana"] .k-transaction-amount {
  font-weight: 600;
  font-size: 16px;
}

[data-kilat-theme="dana"] .k-transaction-amount.positive {
  color: var(--k-dana-success);
}

[data-kilat-theme="dana"] .k-transaction-amount.negative {
  color: var(--k-dana-error);
}

/* 🎚️ Dana Toggle */
[data-kilat-theme="dana"] .k-toggle-dana {
  position: relative;
  width: 48px;
  height: 28px;
  background: var(--k-text-muted);
  border-radius: 14px;
  cursor: pointer;
  transition: all var(--k-dana-transition);
}

[data-kilat-theme="dana"] .k-toggle-dana::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: all var(--k-dana-bounce);
  box-shadow: var(--k-dana-shadow-sm);
}

[data-kilat-theme="dana"] .k-toggle-dana.active {
  background: var(--k-primary);
}

[data-kilat-theme="dana"] .k-toggle-dana.active::after {
  transform: translateX(20px);
}

/* 📊 Dana Progress Bar */
[data-kilat-theme="dana"] .k-progress-dana {
  width: 100%;
  height: 8px;
  background: var(--k-dana-blue-100);
  border-radius: 4px;
  overflow: hidden;
}

[data-kilat-theme="dana"] .k-progress-dana-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--k-primary), var(--k-accent));
  border-radius: 4px;
  transition: width var(--k-dana-transition);
  position: relative;
}

[data-kilat-theme="dana"] .k-progress-dana-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: dana-shimmer 2s infinite;
}

/* 🔔 Dana Notifications */
[data-kilat-theme="dana"] .k-notification-dana {
  background: var(--k-surface);
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: var(--k-dana-shadow-lg);
  border-left: 4px solid var(--k-primary);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

[data-kilat-theme="dana"] .k-notification-success {
  border-left-color: var(--k-dana-success);
}

[data-kilat-theme="dana"] .k-notification-warning {
  border-left-color: var(--k-dana-warning);
}

[data-kilat-theme="dana"] .k-notification-error {
  border-left-color: var(--k-dana-error);
}

/* 🎭 Dana Animations */
@keyframes dana-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes dana-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes dana-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 🌐 Dana Scrollbar */
[data-kilat-theme="dana"] ::-webkit-scrollbar {
  width: 6px;
}

[data-kilat-theme="dana"] ::-webkit-scrollbar-track {
  background: var(--k-dana-blue-50);
  border-radius: 3px;
}

[data-kilat-theme="dana"] ::-webkit-scrollbar-thumb {
  background: var(--k-primary);
  border-radius: 3px;
}

[data-kilat-theme="dana"] ::-webkit-scrollbar-thumb:hover {
  background: var(--k-dana-blue-600);
}

/* 🎨 Dana Utilities */
[data-kilat-theme="dana"] .k-bg-primary { background: var(--k-primary); }
[data-kilat-theme="dana"] .k-bg-secondary { background: var(--k-secondary); }
[data-kilat-theme="dana"] .k-bg-accent { background: var(--k-accent); }

[data-kilat-theme="dana"] .k-text-primary { color: var(--k-primary); }
[data-kilat-theme="dana"] .k-text-secondary { color: var(--k-secondary); }
[data-kilat-theme="dana"] .k-text-accent { color: var(--k-accent); }
[data-kilat-theme="dana"] .k-text-success { color: var(--k-dana-success); }
[data-kilat-theme="dana"] .k-text-warning { color: var(--k-dana-warning); }
[data-kilat-theme="dana"] .k-text-error { color: var(--k-dana-error); }

[data-kilat-theme="dana"] .k-shadow-sm { box-shadow: var(--k-dana-shadow-sm); }
[data-kilat-theme="dana"] .k-shadow-md { box-shadow: var(--k-dana-shadow-md); }
[data-kilat-theme="dana"] .k-shadow-lg { box-shadow: var(--k-dana-shadow-lg); }
[data-kilat-theme="dana"] .k-shadow-xl { box-shadow: var(--k-dana-shadow-xl); }

/* 🏷️ Dana Badges */
[data-kilat-theme="dana"] .k-badge-dana {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background: var(--k-primary);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

[data-kilat-theme="dana"] .k-badge-success {
  background: var(--k-dana-success);
}

[data-kilat-theme="dana"] .k-badge-warning {
  background: var(--k-dana-warning);
}

[data-kilat-theme="dana"] .k-badge-error {
  background: var(--k-dana-error);
}

/* 💳 Dana Payment Card */
[data-kilat-theme="dana"] .k-payment-card {
  background: linear-gradient(135deg, var(--k-primary), var(--k-dana-blue-700));
  color: white;
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

[data-kilat-theme="dana"] .k-payment-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

[data-kilat-theme="dana"] .k-payment-card-number {
  font-family: 'Fira Code', monospace;
  font-size: 18px;
  letter-spacing: 2px;
  margin: 20px 0;
}

[data-kilat-theme="dana"] .k-payment-card-name {
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
}
