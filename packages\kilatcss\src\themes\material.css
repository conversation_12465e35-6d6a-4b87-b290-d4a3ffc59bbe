/**
 * KilatCSS Material Theme 🧱
 * Google Material Design inspired theme with elevation and motion
 */

[data-kilat-theme="material"] {
  /* 🧱 Material Color Palette */
  --k-primary: #1976d2;        /* Blue 700 */
  --k-secondary: #dc004e;      /* Pink A400 */
  --k-accent: #00bcd4;         /* Cyan 500 */
  --k-background: #fafafa;     /* Grey 50 */
  --k-surface: #ffffff;        /* White */
  --k-text: #212121;          /* Grey 900 */
  --k-text-muted: #757575;    /* Grey 600 */
  
  /* 🎨 Material Color System */
  --k-primary-50: #e3f2fd;
  --k-primary-100: #bbdefb;
  --k-primary-200: #90caf9;
  --k-primary-300: #64b5f6;
  --k-primary-400: #42a5f5;
  --k-primary-500: #2196f3;
  --k-primary-600: #1e88e5;
  --k-primary-700: #1976d2;
  --k-primary-800: #1565c0;
  --k-primary-900: #0d47a1;
  
  /* 🌈 Secondary Colors */
  --k-secondary-50: #fce4ec;
  --k-secondary-100: #f8bbd9;
  --k-secondary-200: #f48fb1;
  --k-secondary-300: #f06292;
  --k-secondary-400: #ec407a;
  --k-secondary-500: #e91e63;
  --k-secondary-600: #d81b60;
  --k-secondary-700: #c2185b;
  --k-secondary-800: #ad1457;
  --k-secondary-900: #880e4f;
  
  /* 🎯 Accent Colors */
  --k-accent-50: #e0f7fa;
  --k-accent-100: #b2ebf2;
  --k-accent-200: #80deea;
  --k-accent-300: #4dd0e1;
  --k-accent-400: #26c6da;
  --k-accent-500: #00bcd4;
  --k-accent-600: #00acc1;
  --k-accent-700: #0097a7;
  --k-accent-800: #00838f;
  --k-accent-900: #006064;
  
  /* 🌫️ Material Elevation Shadows */
  --k-elevation-0: none;
  --k-elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --k-elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --k-elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  --k-elevation-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
  --k-elevation-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
  
  /* 🎭 Material Motion */
  --k-motion-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --k-motion-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
  --k-motion-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
  --k-motion-sharp: cubic-bezier(0.4, 0.0, 0.6, 1);
  
  /* ⏱️ Material Duration */
  --k-duration-short1: 75ms;
  --k-duration-short2: 150ms;
  --k-duration-medium1: 200ms;
  --k-duration-medium2: 250ms;
  --k-duration-long1: 300ms;
  --k-duration-long2: 375ms;
}

/* 🌙 Material Dark Mode */
[data-kilat-theme="material"][data-kilat-mode="dark"] {
  --k-background: #121212;
  --k-surface: #1e1e1e;
  --k-text: #ffffff;
  --k-text-muted: #b3b3b3;
  
  /* Dark elevation surfaces */
  --k-surface-1: #1e1e1e;
  --k-surface-2: #232323;
  --k-surface-3: #252525;
  --k-surface-4: #272727;
  --k-surface-6: #2c2c2c;
  --k-surface-8: #2e2e2e;
  --k-surface-12: #333333;
  --k-surface-16: #353535;
  --k-surface-24: #383838;
}

/* 🧱 Material Body Styling */
[data-kilat-theme="material"] body,
[data-kilat-theme="material"] .kilat {
  background-color: var(--k-background);
  color: var(--k-text);
  font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 🎯 Material Buttons */
[data-kilat-theme="material"] .k-btn-material {
  background-color: var(--k-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.75px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 
    background-color var(--k-duration-short2) var(--k-motion-standard),
    box-shadow var(--k-duration-short2) var(--k-motion-standard),
    transform var(--k-duration-short1) var(--k-motion-standard);
  box-shadow: var(--k-elevation-2);
  min-width: 64px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

[data-kilat-theme="material"] .k-btn-material:hover {
  background-color: var(--k-primary-600);
  box-shadow: var(--k-elevation-4);
}

[data-kilat-theme="material"] .k-btn-material:active {
  box-shadow: var(--k-elevation-8);
  transform: translateY(1px);
}

/* Material Ripple Effect */
[data-kilat-theme="material"] .k-btn-material::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--k-duration-medium1) var(--k-motion-decelerate), 
              height var(--k-duration-medium1) var(--k-motion-decelerate);
}

[data-kilat-theme="material"] .k-btn-material:active::before {
  width: 300px;
  height: 300px;
}

/* 🔘 Material Button Variants */
[data-kilat-theme="material"] .k-btn-outlined {
  background-color: transparent;
  color: var(--k-primary);
  border: 1px solid var(--k-primary);
  box-shadow: none;
}

[data-kilat-theme="material"] .k-btn-outlined:hover {
  background-color: var(--k-primary-50);
}

[data-kilat-theme="material"] .k-btn-text {
  background-color: transparent;
  color: var(--k-primary);
  box-shadow: none;
  padding: 8px 8px;
}

[data-kilat-theme="material"] .k-btn-text:hover {
  background-color: var(--k-primary-50);
}

/* 🎮 Material Cards */
[data-kilat-theme="material"] .k-card-material {
  background-color: var(--k-surface);
  border-radius: 4px;
  box-shadow: var(--k-elevation-1);
  transition: box-shadow var(--k-duration-medium1) var(--k-motion-standard);
  overflow: hidden;
}

[data-kilat-theme="material"] .k-card-material:hover {
  box-shadow: var(--k-elevation-3);
}

[data-kilat-theme="material"] .k-card-elevated {
  box-shadow: var(--k-elevation-2);
}

[data-kilat-theme="material"] .k-card-elevated:hover {
  box-shadow: var(--k-elevation-4);
}

/* 🔍 Material Inputs */
[data-kilat-theme="material"] .k-input-material {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid var(--k-text-muted);
  color: var(--k-text);
  padding: 8px 0;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  transition: border-color var(--k-duration-short2) var(--k-motion-standard);
  outline: none;
  width: 100%;
}

[data-kilat-theme="material"] .k-input-material:focus {
  border-bottom-color: var(--k-primary);
  border-bottom-width: 2px;
}

[data-kilat-theme="material"] .k-input-material::placeholder {
  color: var(--k-text-muted);
}

/* Material Outlined Input */
[data-kilat-theme="material"] .k-input-outlined {
  border: 1px solid var(--k-text-muted);
  border-radius: 4px;
  padding: 16px 12px;
  background-color: transparent;
}

[data-kilat-theme="material"] .k-input-outlined:focus {
  border-color: var(--k-primary);
  border-width: 2px;
  padding: 15px 11px; /* Adjust for border width change */
}

/* Material Filled Input */
[data-kilat-theme="material"] .k-input-filled {
  background-color: var(--k-primary-50);
  border: none;
  border-bottom: 1px solid var(--k-text-muted);
  border-radius: 4px 4px 0 0;
  padding: 16px 12px 8px;
}

[data-kilat-theme="material"] .k-input-filled:focus {
  background-color: var(--k-primary-100);
  border-bottom-color: var(--k-primary);
  border-bottom-width: 2px;
}

/* 📋 Material Lists */
[data-kilat-theme="material"] .k-list-material {
  background-color: var(--k-surface);
  border-radius: 4px;
  box-shadow: var(--k-elevation-1);
  padding: 8px 0;
}

[data-kilat-theme="material"] .k-list-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color var(--k-duration-short1) var(--k-motion-standard);
  display: flex;
  align-items: center;
  min-height: 48px;
}

[data-kilat-theme="material"] .k-list-item:hover {
  background-color: var(--k-primary-50);
}

[data-kilat-theme="material"] .k-list-item:active {
  background-color: var(--k-primary-100);
}

/* 🎚️ Material Sliders */
[data-kilat-theme="material"] .k-slider-material {
  -webkit-appearance: none;
  appearance: none;
  height: 2px;
  background: var(--k-text-muted);
  outline: none;
  border-radius: 1px;
}

[data-kilat-theme="material"] .k-slider-material::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--k-primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--k-elevation-1);
  transition: box-shadow var(--k-duration-short1) var(--k-motion-standard);
}

[data-kilat-theme="material"] .k-slider-material::-webkit-slider-thumb:hover {
  box-shadow: var(--k-elevation-2);
}

/* 🌐 Material Scrollbar */
[data-kilat-theme="material"] ::-webkit-scrollbar {
  width: 8px;
}

[data-kilat-theme="material"] ::-webkit-scrollbar-track {
  background: var(--k-background);
}

[data-kilat-theme="material"] ::-webkit-scrollbar-thumb {
  background: var(--k-text-muted);
  border-radius: 4px;
}

[data-kilat-theme="material"] ::-webkit-scrollbar-thumb:hover {
  background: var(--k-primary);
}

/* 🎨 Material Elevation Utilities */
[data-kilat-theme="material"] .k-elevation-0 { box-shadow: var(--k-elevation-0); }
[data-kilat-theme="material"] .k-elevation-1 { box-shadow: var(--k-elevation-1); }
[data-kilat-theme="material"] .k-elevation-2 { box-shadow: var(--k-elevation-2); }
[data-kilat-theme="material"] .k-elevation-3 { box-shadow: var(--k-elevation-3); }
[data-kilat-theme="material"] .k-elevation-4 { box-shadow: var(--k-elevation-4); }
[data-kilat-theme="material"] .k-elevation-5 { box-shadow: var(--k-elevation-5); }

/* 🎭 Material Motion Utilities */
[data-kilat-theme="material"] .k-motion-standard { transition-timing-function: var(--k-motion-standard); }
[data-kilat-theme="material"] .k-motion-decelerate { transition-timing-function: var(--k-motion-decelerate); }
[data-kilat-theme="material"] .k-motion-accelerate { transition-timing-function: var(--k-motion-accelerate); }
[data-kilat-theme="material"] .k-motion-sharp { transition-timing-function: var(--k-motion-sharp); }

/* 📱 Material Typography */
[data-kilat-theme="material"] .k-headline-1 { font-size: 96px; font-weight: 300; line-height: 1.167; letter-spacing: -1.5px; }
[data-kilat-theme="material"] .k-headline-2 { font-size: 60px; font-weight: 300; line-height: 1.2; letter-spacing: -0.5px; }
[data-kilat-theme="material"] .k-headline-3 { font-size: 48px; font-weight: 400; line-height: 1.167; }
[data-kilat-theme="material"] .k-headline-4 { font-size: 34px; font-weight: 400; line-height: 1.235; letter-spacing: 0.25px; }
[data-kilat-theme="material"] .k-headline-5 { font-size: 24px; font-weight: 400; line-height: 1.334; }
[data-kilat-theme="material"] .k-headline-6 { font-size: 20px; font-weight: 500; line-height: 1.6; letter-spacing: 0.15px; }
[data-kilat-theme="material"] .k-subtitle-1 { font-size: 16px; font-weight: 400; line-height: 1.75; letter-spacing: 0.15px; }
[data-kilat-theme="material"] .k-subtitle-2 { font-size: 14px; font-weight: 500; line-height: 1.57; letter-spacing: 0.1px; }
[data-kilat-theme="material"] .k-body-1 { font-size: 16px; font-weight: 400; line-height: 1.5; letter-spacing: 0.15px; }
[data-kilat-theme="material"] .k-body-2 { font-size: 14px; font-weight: 400; line-height: 1.43; letter-spacing: 0.17px; }
[data-kilat-theme="material"] .k-button-text { font-size: 14px; font-weight: 500; line-height: 1.75; letter-spacing: 0.4px; text-transform: uppercase; }
[data-kilat-theme="material"] .k-caption { font-size: 12px; font-weight: 400; line-height: 1.66; letter-spacing: 0.4px; }
[data-kilat-theme="material"] .k-overline { font-size: 12px; font-weight: 400; line-height: 2.66; letter-spacing: 1px; text-transform: uppercase; }
