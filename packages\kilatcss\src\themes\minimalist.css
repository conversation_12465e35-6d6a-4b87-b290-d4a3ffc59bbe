/**
 * KilatCSS Minimalist Theme ⚡
 * Clean, simple, and elegant design
 */

[data-kilat-theme="minimalist"] {
  /* 🎨 Color Palette */
  --k-primary: #000000;
  --k-secondary: #666666;
  --k-accent: #333333;
  --k-background: #ffffff;
  --k-surface: #f8f9fa;
  --k-text: #000000;
  --k-text-muted: #666666;
  --k-border: #e1e5e9;
  
  /* 🌈 Semantic Colors */
  --k-success: #28a745;
  --k-warning: #ffc107;
  --k-error: #dc3545;
  --k-info: #17a2b8;
  
  /* 🔮 Glow Effects (Subtle) */
  --k-glow-sm: 0 0 2px rgba(0, 0, 0, 0.1);
  --k-glow-md: 0 0 4px rgba(0, 0, 0, 0.1);
  --k-glow-lg: 0 0 8px rgba(0, 0, 0, 0.1);
  --k-glow-xl: 0 0 12px rgba(0, 0, 0, 0.1);
  
  /* 📏 Spacing */
  --k-space-unit: 8px;
  
  /* 🔤 Typography */
  --k-font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --k-font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
  --k-font-display: 'Inter', sans-serif;
  
  /* 🎭 Animations */
  --k-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --k-transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --k-transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 🌊 Border Radius */
  --k-radius-sm: 4px;
  --k-radius-md: 6px;
  --k-radius-lg: 8px;
  --k-radius-xl: 12px;
  --k-radius-full: 9999px;
}

/* 🌙 Dark Mode */
[data-kilat-theme="minimalist"][data-kilat-mode="dark"] {
  --k-primary: #ffffff;
  --k-secondary: #a0a0a0;
  --k-accent: #cccccc;
  --k-background: #000000;
  --k-surface: #111111;
  --k-text: #ffffff;
  --k-text-muted: #a0a0a0;
  --k-border: #333333;
}

/* 🧱 Components */

/* 🔘 Buttons */
[data-kilat-theme="minimalist"] .k-btn {
  background: var(--k-primary);
  color: var(--k-background);
  border: 1px solid var(--k-primary);
  padding: 12px 24px;
  border-radius: var(--k-radius-md);
  font-family: var(--k-font-sans);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--k-transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  outline: none;
}

[data-kilat-theme="minimalist"] .k-btn:hover {
  background: var(--k-secondary);
  border-color: var(--k-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

[data-kilat-theme="minimalist"] .k-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-kilat-theme="minimalist"] .k-btn-outline {
  background: transparent;
  color: var(--k-primary);
  border: 1px solid var(--k-primary);
}

[data-kilat-theme="minimalist"] .k-btn-outline:hover {
  background: var(--k-primary);
  color: var(--k-background);
}

[data-kilat-theme="minimalist"] .k-btn-ghost {
  background: transparent;
  color: var(--k-primary);
  border: 1px solid transparent;
}

[data-kilat-theme="minimalist"] .k-btn-ghost:hover {
  background: var(--k-surface);
  border-color: var(--k-border);
}

/* 📦 Cards */
[data-kilat-theme="minimalist"] .k-card {
  background: var(--k-surface);
  border: 1px solid var(--k-border);
  border-radius: var(--k-radius-lg);
  padding: 24px;
  transition: all var(--k-transition-normal);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

[data-kilat-theme="minimalist"] .k-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

[data-kilat-theme="minimalist"] .k-card-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--k-border);
}

[data-kilat-theme="minimalist"] .k-card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--k-text);
  margin: 0;
}

[data-kilat-theme="minimalist"] .k-card-content {
  color: var(--k-text-muted);
  line-height: 1.6;
}

/* 📝 Forms */
[data-kilat-theme="minimalist"] .k-input {
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: var(--k-radius-md);
  padding: 12px 16px;
  font-family: var(--k-font-sans);
  font-size: 14px;
  color: var(--k-text);
  transition: all var(--k-transition-normal);
  outline: none;
  width: 100%;
}

[data-kilat-theme="minimalist"] .k-input:focus {
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

[data-kilat-theme="minimalist"] .k-input::placeholder {
  color: var(--k-text-muted);
}

[data-kilat-theme="minimalist"] .k-textarea {
  background: var(--k-background);
  border: 1px solid var(--k-border);
  border-radius: var(--k-radius-md);
  padding: 12px 16px;
  font-family: var(--k-font-sans);
  font-size: 14px;
  color: var(--k-text);
  transition: all var(--k-transition-normal);
  outline: none;
  width: 100%;
  min-height: 100px;
  resize: vertical;
}

[data-kilat-theme="minimalist"] .k-textarea:focus {
  border-color: var(--k-primary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* 🏷️ Labels */
[data-kilat-theme="minimalist"] .k-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--k-text);
  margin-bottom: 8px;
}

/* 🚨 Alerts */
[data-kilat-theme="minimalist"] .k-alert {
  padding: 16px;
  border-radius: var(--k-radius-md);
  border: 1px solid;
  margin-bottom: 16px;
  font-size: 14px;
}

[data-kilat-theme="minimalist"] .k-alert-success {
  background: rgba(40, 167, 69, 0.1);
  border-color: var(--k-success);
  color: var(--k-success);
}

[data-kilat-theme="minimalist"] .k-alert-warning {
  background: rgba(255, 193, 7, 0.1);
  border-color: var(--k-warning);
  color: #856404;
}

[data-kilat-theme="minimalist"] .k-alert-error {
  background: rgba(220, 53, 69, 0.1);
  border-color: var(--k-error);
  color: var(--k-error);
}

[data-kilat-theme="minimalist"] .k-alert-info {
  background: rgba(23, 162, 184, 0.1);
  border-color: var(--k-info);
  color: var(--k-info);
}

/* 🎯 Badges */
[data-kilat-theme="minimalist"] .k-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: var(--k-radius-full);
  background: var(--k-surface);
  color: var(--k-text);
  border: 1px solid var(--k-border);
}

[data-kilat-theme="minimalist"] .k-badge-primary {
  background: var(--k-primary);
  color: var(--k-background);
  border-color: var(--k-primary);
}

/* 📊 Progress */
[data-kilat-theme="minimalist"] .k-progress {
  width: 100%;
  height: 8px;
  background: var(--k-surface);
  border-radius: var(--k-radius-full);
  overflow: hidden;
  border: 1px solid var(--k-border);
}

[data-kilat-theme="minimalist"] .k-progress-bar {
  height: 100%;
  background: var(--k-primary);
  transition: width var(--k-transition-normal);
  border-radius: var(--k-radius-full);
}

/* 🔗 Links */
[data-kilat-theme="minimalist"] .k-link {
  color: var(--k-primary);
  text-decoration: none;
  transition: all var(--k-transition-fast);
  border-bottom: 1px solid transparent;
}

[data-kilat-theme="minimalist"] .k-link:hover {
  border-bottom-color: var(--k-primary);
}

/* 📋 Tables */
[data-kilat-theme="minimalist"] .k-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--k-background);
  border-radius: var(--k-radius-lg);
  overflow: hidden;
  border: 1px solid var(--k-border);
}

[data-kilat-theme="minimalist"] .k-table th,
[data-kilat-theme="minimalist"] .k-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--k-border);
}

[data-kilat-theme="minimalist"] .k-table th {
  background: var(--k-surface);
  font-weight: 600;
  color: var(--k-text);
}

[data-kilat-theme="minimalist"] .k-table td {
  color: var(--k-text-muted);
}

[data-kilat-theme="minimalist"] .k-table tr:hover {
  background: var(--k-surface);
}

/* 🎛️ Switches */
[data-kilat-theme="minimalist"] .k-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

[data-kilat-theme="minimalist"] .k-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

[data-kilat-theme="minimalist"] .k-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--k-border);
  transition: var(--k-transition-normal);
  border-radius: var(--k-radius-full);
}

[data-kilat-theme="minimalist"] .k-switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: var(--k-background);
  transition: var(--k-transition-normal);
  border-radius: 50%;
}

[data-kilat-theme="minimalist"] .k-switch input:checked + .k-switch-slider {
  background: var(--k-primary);
}

[data-kilat-theme="minimalist"] .k-switch input:checked + .k-switch-slider:before {
  transform: translateX(20px);
}

/* 🎨 Utilities */
[data-kilat-theme="minimalist"] .k-text-primary { color: var(--k-primary); }
[data-kilat-theme="minimalist"] .k-text-secondary { color: var(--k-secondary); }
[data-kilat-theme="minimalist"] .k-text-muted { color: var(--k-text-muted); }
[data-kilat-theme="minimalist"] .k-bg-primary { background-color: var(--k-primary); }
[data-kilat-theme="minimalist"] .k-bg-secondary { background-color: var(--k-secondary); }
[data-kilat-theme="minimalist"] .k-bg-surface { background-color: var(--k-surface); }
[data-kilat-theme="minimalist"] .k-border-primary { border-color: var(--k-primary); }
[data-kilat-theme="minimalist"] .k-border-secondary { border-color: var(--k-secondary); }
