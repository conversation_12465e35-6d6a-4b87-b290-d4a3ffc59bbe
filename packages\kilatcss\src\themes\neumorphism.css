/**
 * KilatCSS Neumorphism Theme 🧼
 * Soft UI design with inset shadows and subtle depth
 */

[data-kilat-theme="neumorphism"] {
  /* 🧼 Neumorphism Color Palette */
  --k-primary: #667eea;        /* Soft Blue */
  --k-secondary: #764ba2;      /* Soft Purple */
  --k-accent: #f093fb;         /* Soft Pink */
  --k-background: #e0e5ec;     /* Light Gray */
  --k-surface: #e0e5ec;        /* Same as background */
  --k-text: #4a5568;          /* Dark Gray */
  --k-text-muted: #718096;    /* Medium Gray */
  
  /* 🎨 Neumorphism Grays */
  --k-gray-50: #f7fafc;
  --k-gray-100: #edf2f7;
  --k-gray-200: #e2e8f0;
  --k-gray-300: #cbd5e0;
  --k-gray-400: #a0aec0;
  --k-gray-500: #718096;
  --k-gray-600: #4a5568;
  --k-gray-700: #2d3748;
  --k-gray-800: #1a202c;
  --k-gray-900: #171923;
  
  /* 🌈 Soft Colors */
  --k-soft-blue: #667eea;
  --k-soft-purple: #764ba2;
  --k-soft-pink: #f093fb;
  --k-soft-green: #4fd1c7;
  --k-soft-yellow: #fed7aa;
  --k-soft-red: #fc8181;
  --k-soft-orange: #f6ad55;
  
  /* 🌫️ Neumorphism Shadows */
  --k-shadow-light: #ffffff;
  --k-shadow-dark: #a3b1c6;
  
  /* 📦 Neumorphism Box Shadows */
  --k-neu-flat: 
    20px 20px 60px var(--k-shadow-dark),
    -20px -20px 60px var(--k-shadow-light);
  
  --k-neu-pressed: 
    inset 20px 20px 60px var(--k-shadow-dark),
    inset -20px -20px 60px var(--k-shadow-light);
  
  --k-neu-convex: 
    5px 5px 10px var(--k-shadow-dark),
    -5px -5px 10px var(--k-shadow-light);
  
  --k-neu-concave: 
    inset 5px 5px 10px var(--k-shadow-dark),
    inset -5px -5px 10px var(--k-shadow-light);
  
  --k-neu-float: 
    10px 10px 20px var(--k-shadow-dark),
    -10px -10px 20px var(--k-shadow-light);
  
  /* 🎭 Smooth Transitions */
  --k-transition-smooth: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --k-transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 🌙 Neumorphism Dark Mode */
[data-kilat-theme="neumorphism"][data-kilat-mode="dark"] {
  --k-background: #2d3748;
  --k-surface: #2d3748;
  --k-text: #e2e8f0;
  --k-text-muted: #a0aec0;
  --k-shadow-light: #3a4a5c;
  --k-shadow-dark: #1e2530;
}

/* 🧼 Neumorphism Body Styling */
[data-kilat-theme="neumorphism"] body,
[data-kilat-theme="neumorphism"] .kilat {
  background: var(--k-background);
  color: var(--k-text);
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 🎯 Neumorphism Buttons */
[data-kilat-theme="neumorphism"] .k-btn-neu {
  background: var(--k-surface);
  border: none;
  border-radius: 20px;
  padding: 12px 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: var(--k-text);
  cursor: pointer;
  position: relative;
  transition: all var(--k-transition-smooth);
  box-shadow: var(--k-neu-convex);
  min-width: 120px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

[data-kilat-theme="neumorphism"] .k-btn-neu:hover {
  transform: translateY(-2px);
  box-shadow: var(--k-neu-float);
}

[data-kilat-theme="neumorphism"] .k-btn-neu:active {
  transform: translateY(0);
  box-shadow: var(--k-neu-pressed);
  transition: all 0.1s ease;
}

/* Neumorphism Button Variants */
[data-kilat-theme="neumorphism"] .k-btn-neu-primary {
  background: linear-gradient(145deg, var(--k-soft-blue), #5a6fd8);
  color: white;
  box-shadow: 
    8px 8px 16px var(--k-shadow-dark),
    -8px -8px 16px var(--k-shadow-light);
}

[data-kilat-theme="neumorphism"] .k-btn-neu-secondary {
  background: linear-gradient(145deg, var(--k-soft-purple), #6b4190);
  color: white;
  box-shadow: 
    8px 8px 16px var(--k-shadow-dark),
    -8px -8px 16px var(--k-shadow-light);
}

/* 🎮 Neumorphism Cards */
[data-kilat-theme="neumorphism"] .k-card-neu {
  background: var(--k-surface);
  border-radius: 20px;
  padding: 24px;
  box-shadow: var(--k-neu-flat);
  transition: all var(--k-transition-smooth);
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="neumorphism"] .k-card-neu:hover {
  transform: translateY(-4px);
  box-shadow: 
    25px 25px 75px var(--k-shadow-dark),
    -25px -25px 75px var(--k-shadow-light);
}

[data-kilat-theme="neumorphism"] .k-card-neu-pressed {
  box-shadow: var(--k-neu-pressed);
}

[data-kilat-theme="neumorphism"] .k-card-neu-flat {
  box-shadow: var(--k-neu-convex);
}

/* 🔍 Neumorphism Inputs */
[data-kilat-theme="neumorphism"] .k-input-neu {
  background: var(--k-surface);
  border: none;
  border-radius: 15px;
  padding: 16px 20px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--k-text);
  box-shadow: var(--k-neu-concave);
  transition: all var(--k-transition-smooth);
  outline: none;
  width: 100%;
}

[data-kilat-theme="neumorphism"] .k-input-neu:focus {
  box-shadow: 
    inset 8px 8px 16px var(--k-shadow-dark),
    inset -8px -8px 16px var(--k-shadow-light),
    0 0 0 3px rgba(102, 126, 234, 0.1);
}

[data-kilat-theme="neumorphism"] .k-input-neu::placeholder {
  color: var(--k-text-muted);
}

/* 🎚️ Neumorphism Sliders */
[data-kilat-theme="neumorphism"] .k-slider-neu {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  background: var(--k-surface);
  border-radius: 4px;
  outline: none;
  box-shadow: var(--k-neu-concave);
}

[data-kilat-theme="neumorphism"] .k-slider-neu::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  background: var(--k-surface);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--k-neu-convex);
  transition: all var(--k-transition-smooth);
}

[data-kilat-theme="neumorphism"] .k-slider-neu::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: var(--k-neu-float);
}

/* 🔘 Neumorphism Radio & Checkbox */
[data-kilat-theme="neumorphism"] .k-radio-neu,
[data-kilat-theme="neumorphism"] .k-checkbox-neu {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--k-surface);
  border-radius: 50%;
  box-shadow: var(--k-neu-concave);
  cursor: pointer;
  position: relative;
  transition: all var(--k-transition-smooth);
}

[data-kilat-theme="neumorphism"] .k-checkbox-neu {
  border-radius: 6px;
}

[data-kilat-theme="neumorphism"] .k-radio-neu:checked,
[data-kilat-theme="neumorphism"] .k-checkbox-neu:checked {
  background: linear-gradient(145deg, var(--k-soft-blue), #5a6fd8);
  box-shadow: var(--k-neu-pressed);
}

[data-kilat-theme="neumorphism"] .k-radio-neu:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

[data-kilat-theme="neumorphism"] .k-checkbox-neu:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  color: white;
  font-size: 12px;
  font-weight: bold;
  transform: translate(-50%, -50%);
}

/* 🎭 Neumorphism Switch */
[data-kilat-theme="neumorphism"] .k-switch-neu {
  width: 60px;
  height: 30px;
  background: var(--k-surface);
  border-radius: 15px;
  box-shadow: var(--k-neu-concave);
  position: relative;
  cursor: pointer;
  transition: all var(--k-transition-smooth);
}

[data-kilat-theme="neumorphism"] .k-switch-neu::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: var(--k-surface);
  border-radius: 50%;
  box-shadow: var(--k-neu-convex);
  transition: all var(--k-transition-bounce);
}

[data-kilat-theme="neumorphism"] .k-switch-neu.active {
  background: linear-gradient(145deg, var(--k-soft-blue), #5a6fd8);
}

[data-kilat-theme="neumorphism"] .k-switch-neu.active::after {
  left: 33px;
  background: white;
}

/* 📋 Neumorphism Progress Bar */
[data-kilat-theme="neumorphism"] .k-progress-neu {
  width: 100%;
  height: 12px;
  background: var(--k-surface);
  border-radius: 6px;
  box-shadow: var(--k-neu-concave);
  overflow: hidden;
  position: relative;
}

[data-kilat-theme="neumorphism"] .k-progress-neu-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--k-soft-blue), var(--k-soft-purple));
  border-radius: 6px;
  box-shadow: 
    2px 2px 4px rgba(102, 126, 234, 0.3),
    -2px -2px 4px rgba(255, 255, 255, 0.8);
  transition: width var(--k-transition-smooth);
}

/* 🌐 Neumorphism Scrollbar */
[data-kilat-theme="neumorphism"] ::-webkit-scrollbar {
  width: 12px;
}

[data-kilat-theme="neumorphism"] ::-webkit-scrollbar-track {
  background: var(--k-surface);
  border-radius: 6px;
  box-shadow: var(--k-neu-concave);
}

[data-kilat-theme="neumorphism"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(145deg, var(--k-soft-blue), #5a6fd8);
  border-radius: 6px;
  box-shadow: var(--k-neu-convex);
}

[data-kilat-theme="neumorphism"] ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(145deg, #5a6fd8, var(--k-soft-blue));
}

/* 🎨 Neumorphism Utilities */
[data-kilat-theme="neumorphism"] .k-neu-flat { box-shadow: var(--k-neu-flat); }
[data-kilat-theme="neumorphism"] .k-neu-pressed { box-shadow: var(--k-neu-pressed); }
[data-kilat-theme="neumorphism"] .k-neu-convex { box-shadow: var(--k-neu-convex); }
[data-kilat-theme="neumorphism"] .k-neu-concave { box-shadow: var(--k-neu-concave); }
[data-kilat-theme="neumorphism"] .k-neu-float { box-shadow: var(--k-neu-float); }

/* 🎯 Neumorphism Icon Buttons */
[data-kilat-theme="neumorphism"] .k-icon-btn-neu {
  width: 48px;
  height: 48px;
  background: var(--k-surface);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--k-neu-convex);
  transition: all var(--k-transition-smooth);
  color: var(--k-text);
  font-size: 18px;
}

[data-kilat-theme="neumorphism"] .k-icon-btn-neu:hover {
  transform: translateY(-2px);
  box-shadow: var(--k-neu-float);
}

[data-kilat-theme="neumorphism"] .k-icon-btn-neu:active {
  transform: translateY(0);
  box-shadow: var(--k-neu-pressed);
}

/* 🏷️ Neumorphism Tags */
[data-kilat-theme="neumorphism"] .k-tag-neu {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: var(--k-surface);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: var(--k-text);
  box-shadow: var(--k-neu-convex);
  margin: 2px;
}

[data-kilat-theme="neumorphism"] .k-tag-neu-primary {
  background: linear-gradient(145deg, var(--k-soft-blue), #5a6fd8);
  color: white;
}

[data-kilat-theme="neumorphism"] .k-tag-neu-secondary {
  background: linear-gradient(145deg, var(--k-soft-purple), #6b4190);
  color: white;
}
