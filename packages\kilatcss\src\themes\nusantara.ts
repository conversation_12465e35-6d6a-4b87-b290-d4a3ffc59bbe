import type { Theme } from '../types';

/**
 * 🇮🇩 Nusantara Theme - Indonesian cultural heritage
 * Inspired by traditional Indonesian art, batik patterns, and warm earth tones
 */
export const nusantaraTheme: Theme = {
  name: 'nusantara',
  displayName: 'Nusantara',
  description: 'Indonesian cultural theme with batik patterns and warm earth tones',
  category: 'cultural',
  
  colors: {
    // Primary warm brown (inspired by traditional wood)
    primary: {
      50: '#fdf8f6',
      100: '#f2e8e5',
      200: '#eaddd7',
      300: '#e0cfc5',
      400: '#d2bab0',
      500: '#8B4513', // Main warm brown
      600: '#a16207',
      700: '#854d0e',
      800: '#713f12',
      900: '#5c2e04',
      950: '#451a03'
    },
    
    // Secondary golden yellow (inspired by gold leaf)
    secondary: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#DAA520', // Main golden yellow
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
      950: '#422006'
    },
    
    // Accent deep red (inspired by traditional red)
    accent: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#DC143C', // Traditional red
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    
    // Warning orange (inspired by sunset)
    warning: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#FF8C00', // Sunset orange
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
      950: '#431407'
    },
    
    // Error deep red
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#B22222', // Fire brick red
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    
    // Success green (inspired by tropical leaves)
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#228B22', // Forest green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // Neutral warm grays
    neutral: {
      50: '#fafaf9',
      100: '#f5f5f4',
      200: '#e7e5e4',
      300: '#d6d3d1',
      400: '#a8a29e',
      500: '#78716c',
      600: '#57534e',
      700: '#44403c',
      800: '#292524',
      900: '#1c1917',
      950: '#0c0a09'
    },
    
    // Background colors (warm and earthy)
    background: {
      primary: '#faf9f7',
      secondary: '#f5f4f1',
      tertiary: '#ede9e3',
      elevated: '#ffffff'
    },
    
    // Text colors
    text: {
      primary: '#1c1917',
      secondary: '#44403c',
      tertiary: '#78716c',
      inverse: '#fafaf9',
      accent: '#8B4513'
    },
    
    // Border colors
    border: {
      primary: '#e7e5e4',
      secondary: '#d6d3d1',
      accent: '#8B4513',
      warm: 'rgba(139, 69, 19, 0.2)'
    },
    
    // Traditional Indonesian colors
    traditional: {
      batik: '#4A4A4A',
      indigo: '#4B0082',
      saffron: '#F4C430',
      terracotta: '#E2725B',
      bamboo: '#C7B377',
      teak: '#B8860B'
    }
  },
  
  typography: {
    fontFamily: {
      sans: ['Nunito', 'system-ui', 'sans-serif'],
      serif: ['Playfair Display', 'Georgia', 'serif'],
      mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace'],
      display: ['Poppins', 'Nunito', 'sans-serif'],
      traditional: ['Noto Serif', 'Georgia', 'serif']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem'
    },
    fontWeight: {
      thin: '100',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    },
    lineHeight: {
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2'
    },
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    }
  },
  
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
  },
  
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.375rem', // Slightly more rounded for warmth
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.25rem',
    '3xl': '1.75rem',
    full: '9999px'
  },
  
  boxShadow: {
    sm: '0 1px 2px 0 rgba(139, 69, 19, 0.05)',
    DEFAULT: '0 1px 3px 0 rgba(139, 69, 19, 0.1), 0 1px 2px 0 rgba(139, 69, 19, 0.06)',
    md: '0 4px 6px -1px rgba(139, 69, 19, 0.1), 0 2px 4px -1px rgba(139, 69, 19, 0.06)',
    lg: '0 10px 15px -3px rgba(139, 69, 19, 0.1), 0 4px 6px -2px rgba(139, 69, 19, 0.05)',
    xl: '0 20px 25px -5px rgba(139, 69, 19, 0.1), 0 10px 10px -5px rgba(139, 69, 19, 0.04)',
    '2xl': '0 25px 50px -12px rgba(139, 69, 19, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(139, 69, 19, 0.06)',
    none: 'none',
    // Warm glow effects
    'warm-sm': '0 0 5px rgba(218, 165, 32, 0.3)',
    'warm': '0 0 10px rgba(218, 165, 32, 0.4)',
    'warm-md': '0 0 15px rgba(218, 165, 32, 0.5)',
    'warm-lg': '0 0 20px rgba(218, 165, 32, 0.6)',
    'batik': '0 4px 12px rgba(74, 74, 74, 0.15)'
  },
  
  animation: {
    // Indonesian-inspired animations
    'gentle-sway': 'gentle-sway 3s ease-in-out infinite',
    'batik-flow': 'batik-flow 8s linear infinite',
    'warm-pulse': 'warm-pulse 2s ease-in-out infinite',
    'traditional-fade': 'traditional-fade 1s ease-in-out',
    'bamboo-grow': 'bamboo-grow 2s ease-out',
    'wave-pattern': 'wave-pattern 4s ease-in-out infinite',
    'golden-shimmer': 'golden-shimmer 3s ease-in-out infinite'
  },
  
  keyframes: {
    'gentle-sway': {
      '0%, 100%': {
        transform: 'translateX(0px) rotate(0deg)'
      },
      '50%': {
        transform: 'translateX(2px) rotate(0.5deg)'
      }
    },
    'batik-flow': {
      '0%': {
        backgroundPosition: '0% 0%'
      },
      '100%': {
        backgroundPosition: '100% 100%'
      }
    },
    'warm-pulse': {
      '0%, 100%': {
        boxShadow: '0 0 5px rgba(218, 165, 32, 0.3)',
        opacity: '1'
      },
      '50%': {
        boxShadow: '0 0 15px rgba(218, 165, 32, 0.6)',
        opacity: '0.9'
      }
    },
    'traditional-fade': {
      '0%': {
        opacity: '0',
        transform: 'translateY(10px)'
      },
      '100%': {
        opacity: '1',
        transform: 'translateY(0)'
      }
    },
    'bamboo-grow': {
      '0%': {
        height: '0%',
        opacity: '0'
      },
      '50%': {
        opacity: '1'
      },
      '100%': {
        height: '100%',
        opacity: '1'
      }
    },
    'wave-pattern': {
      '0%, 100%': {
        transform: 'translateY(0px)'
      },
      '50%': {
        transform: 'translateY(-5px)'
      }
    },
    'golden-shimmer': {
      '0%': {
        backgroundPosition: '-200% center'
      },
      '100%': {
        backgroundPosition: '200% center'
      }
    }
  },
  
  // Custom CSS properties specific to Nusantara theme
  customProperties: {
    '--k-nusantara-warm-gradient': 'linear-gradient(135deg, #DAA520, #8B4513)',
    '--k-nusantara-batik-pattern': 'url("data:image/svg+xml,%3Csvg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%234A4A4A\' fill-opacity=\'0.1\'%3E%3Cpath d=\'M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z\'/%3E%3C/g%3E%3C/svg%3E")',
    '--k-nusantara-wood-texture': 'linear-gradient(90deg, rgba(139, 69, 19, 0.1) 0%, rgba(160, 82, 45, 0.1) 50%, rgba(139, 69, 19, 0.1) 100%)',
    '--k-nusantara-traditional-border': 'repeating-linear-gradient(45deg, #DAA520, #DAA520 2px, transparent 2px, transparent 8px)',
    '--k-nusantara-golden-glow': 'radial-gradient(circle, rgba(218, 165, 32, 0.2) 0%, transparent 70%)'
  },
  
  // Theme-specific component variants
  components: {
    button: {
      nusantara: {
        base: 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border border-amber-600 hover:from-amber-600 hover:to-orange-600 transition-all duration-300',
        traditional: 'bg-primary-500 text-white border-2 border-secondary-500 hover:bg-secondary-500 hover:border-primary-500',
        batik: 'bg-[image:var(--k-nusantara-batik-pattern)] bg-amber-50 border border-amber-300 text-amber-900 hover:bg-amber-100'
      }
    },
    card: {
      nusantara: {
        base: 'bg-white border border-amber-200 shadow-warm',
        traditional: 'bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-300',
        batik: 'bg-[image:var(--k-nusantara-batik-pattern)] bg-white border-2 border-amber-400',
        wood: 'bg-[image:var(--k-nusantara-wood-texture)] bg-amber-100 border border-amber-300'
      }
    },
    input: {
      nusantara: {
        base: 'bg-white border border-amber-300 text-amber-900 placeholder-amber-500 focus:border-amber-500 focus:ring-amber-200',
        traditional: 'bg-amber-50 border-2 border-amber-400 focus:border-primary-500'
      }
    },
    text: {
      nusantara: {
        traditional: 'font-traditional text-amber-900',
        accent: 'text-primary-600 font-semibold',
        golden: 'text-secondary-600 font-medium'
      }
    }
  },
  
  // Traditional Indonesian patterns and motifs
  patterns: {
    batik: {
      kawung: 'radial-gradient(circle at 50% 50%, #4A4A4A 2px, transparent 2px)',
      parang: 'repeating-linear-gradient(45deg, transparent, transparent 10px, #4A4A4A 10px, #4A4A4A 12px)',
      mega: 'conic-gradient(from 0deg, #4A4A4A, transparent, #4A4A4A)',
      sido: 'linear-gradient(45deg, #4A4A4A 25%, transparent 25%, transparent 75%, #4A4A4A 75%)'
    },
    traditional: {
      songket: 'linear-gradient(0deg, #DAA520 1px, transparent 1px), linear-gradient(90deg, #DAA520 1px, transparent 1px)',
      ikat: 'repeating-linear-gradient(90deg, #8B4513, #8B4513 4px, #DAA520 4px, #DAA520 8px)',
      tenun: 'repeating-conic-gradient(#8B4513 0deg 90deg, #DAA520 90deg 180deg)'
    }
  }
};

export default nusantaraTheme;
