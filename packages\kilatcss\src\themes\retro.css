/**
 * <PERSON><PERSON>CSS Retro Theme 🕹️
 * 80s/90s arcade and synthwave inspired theme
 */

[data-kilat-theme="retro"] {
  /* 🕹️ Retro Color Palette */
  --k-primary: #ff0080;        /* Hot Pink */
  --k-secondary: #00ffff;      /* Cyan */
  --k-accent: #ffff00;         /* Electric Yellow */
  --k-background: #0a0a0a;     /* Deep Black */
  --k-surface: #1a1a2e;       /* Dark Purple */
  --k-text: #ffffff;          /* White */
  --k-text-muted: #cccccc;    /* Light Gray */
  
  /* 🌈 Synthwave Colors */
  --k-retro-pink: #ff0080;
  --k-retro-cyan: #00ffff;
  --k-retro-purple: #8000ff;
  --k-retro-yellow: #ffff00;
  --k-retro-orange: #ff8000;
  --k-retro-green: #00ff80;
  --k-retro-blue: #0080ff;
  
  /* 🎮 Arcade Colors */
  --k-arcade-red: #ff4444;
  --k-arcade-blue: #4444ff;
  --k-arcade-green: #44ff44;
  --k-arcade-yellow: #ffff44;
  
  /* 🌅 Gradient Combinations */
  --k-sunset: linear-gradient(135deg, #ff0080, #ff8000, #ffff00);
  --k-ocean: linear-gradient(135deg, #0080ff, #00ffff, #00ff80);
  --k-neon: linear-gradient(135deg, #ff0080, #8000ff, #00ffff);
  --k-synthwave: linear-gradient(135deg, #ff0080, #8000ff, #0080ff);
  
  /* 🔮 Retro Glow Effects */
  --k-glow-sm: 0 0 5px currentColor, 0 0 10px currentColor;
  --k-glow-md: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  --k-glow-lg: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor, 0 0 60px currentColor;
  --k-glow-xl: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;
  
  /* 🎭 Animation Speeds */
  --k-transition-fast: 150ms ease;
  --k-transition-normal: 300ms ease;
  --k-transition-slow: 500ms ease;
}

/* 🕹️ Retro Body Styling */
[data-kilat-theme="retro"] body,
[data-kilat-theme="retro"] .kilat {
  background: 
    linear-gradient(45deg, transparent 25%, rgba(255, 0, 128, 0.03) 25%, rgba(255, 0, 128, 0.03) 50%, transparent 50%, transparent 75%, rgba(0, 255, 255, 0.03) 75%),
    linear-gradient(-45deg, transparent 25%, rgba(128, 0, 255, 0.03) 25%, rgba(128, 0, 255, 0.03) 50%, transparent 50%, transparent 75%, rgba(255, 255, 0, 0.03) 75%),
    radial-gradient(circle at 20% 80%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    #0a0a0a;
  background-size: 40px 40px, 40px 40px, 100% 100%, 100% 100%, 100% 100%;
  color: var(--k-text);
  font-family: 'Orbitron', 'Press Start 2P', monospace;
}

/* 🌟 Retro Text Effects */
[data-kilat-theme="retro"] .k-text-glow-synthwave {
  color: var(--k-retro-pink);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
  animation: synthwave-pulse 2s ease-in-out infinite alternate;
}

[data-kilat-theme="retro"] .k-text-glow-arcade {
  color: var(--k-retro-cyan);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
  animation: arcade-flicker 1.5s linear infinite;
}

[data-kilat-theme="retro"] .k-text-glow-neon {
  background: var(--k-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 
    0 0 10px rgba(255, 0, 128, 0.5),
    0 0 20px rgba(0, 255, 255, 0.3);
  animation: neon-shift 3s linear infinite;
}

/* 🎭 Retro Animations */
@keyframes synthwave-pulse {
  0% { 
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  100% { 
    text-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

@keyframes arcade-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
  75% { opacity: 0.9; }
}

@keyframes neon-shift {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

@keyframes retro-scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes pixel-dance {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05) rotate(1deg); }
  50% { transform: scale(0.95) rotate(-1deg); }
  75% { transform: scale(1.02) rotate(0.5deg); }
}

/* 🔲 Retro Borders */
[data-kilat-theme="retro"] .k-border-retro {
  border: 2px solid var(--k-retro-pink);
  box-shadow: 
    0 0 15px var(--k-retro-pink),
    inset 0 0 15px rgba(255, 0, 128, 0.1);
  position: relative;
}

[data-kilat-theme="retro"] .k-border-retro::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--k-neon);
  z-index: -1;
  border-radius: inherit;
  opacity: 0.3;
  animation: neon-shift 3s linear infinite;
}

/* 🎯 Retro Buttons */
[data-kilat-theme="retro"] .k-btn-retro {
  background: var(--k-synthwave);
  border: 2px solid var(--k-retro-cyan);
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  position: relative;
  overflow: hidden;
  transition: all var(--k-transition-normal);
  box-shadow: 
    0 0 20px rgba(255, 0, 128, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  clip-path: polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%);
}

[data-kilat-theme="retro"] .k-btn-retro::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--k-transition-slow);
}

[data-kilat-theme="retro"] .k-btn-retro:hover::before {
  left: 100%;
}

[data-kilat-theme="retro"] .k-btn-retro:hover {
  box-shadow: 
    0 0 30px rgba(255, 0, 128, 0.5),
    0 0 40px rgba(0, 255, 255, 0.3),
    inset 0 0 30px rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) scale(1.02);
  animation: pixel-dance 0.5s ease-in-out;
}

/* 🎮 Retro Cards */
[data-kilat-theme="retro"] .k-card-retro {
  background: 
    var(--k-synthwave),
    rgba(26, 26, 46, 0.9);
  background-blend-mode: overlay;
  border: 2px solid var(--k-retro-cyan);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 0, 128, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  clip-path: polygon(15px 0%, 100% 0%, calc(100% - 15px) 100%, 0% 100%);
}

[data-kilat-theme="retro"] .k-card-retro::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--k-neon);
  animation: retro-scan 4s linear infinite;
}

/* 🔍 Retro Inputs */
[data-kilat-theme="retro"] .k-input-retro {
  background: rgba(26, 26, 46, 0.8);
  border: 2px solid rgba(0, 255, 255, 0.5);
  color: var(--k-text);
  padding: 0.75rem 1rem;
  font-family: 'Fira Code', monospace;
  transition: all var(--k-transition-normal);
  box-shadow: 
    inset 0 0 10px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(0, 255, 255, 0.2);
  clip-path: polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%);
}

[data-kilat-theme="retro"] .k-input-retro:focus {
  outline: none;
  border-color: var(--k-retro-pink);
  box-shadow: 
    0 0 20px rgba(255, 0, 128, 0.4),
    0 0 30px rgba(0, 255, 255, 0.2),
    inset 0 0 10px rgba(255, 0, 128, 0.1);
  animation: pixel-dance 0.3s ease-in-out;
}

[data-kilat-theme="retro"] .k-input-retro::placeholder {
  color: var(--k-text-muted);
  opacity: 0.7;
}

/* 🌐 Retro Scrollbar */
[data-kilat-theme="retro"] ::-webkit-scrollbar {
  width: 12px;
}

[data-kilat-theme="retro"] ::-webkit-scrollbar-track {
  background: rgba(26, 26, 46, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

[data-kilat-theme="retro"] ::-webkit-scrollbar-thumb {
  background: var(--k-synthwave);
  border: 1px solid var(--k-retro-cyan);
  box-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
}

[data-kilat-theme="retro"] ::-webkit-scrollbar-thumb:hover {
  box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
  animation: neon-shift 1s linear infinite;
}

/* 🎨 Retro Pattern Utilities */
[data-kilat-theme="retro"] .k-pattern-grid {
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

[data-kilat-theme="retro"] .k-pattern-circuit {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(255, 255, 0, 0.05) 40%, rgba(255, 255, 0, 0.05) 60%, transparent 60%);
  background-size: 40px 40px, 60px 60px, 80px 80px;
}

/* 🕹️ Retro Accent Classes */
[data-kilat-theme="retro"] .k-accent-synthwave {
  color: var(--k-retro-pink);
  font-weight: 700;
  text-shadow: 0 0 10px currentColor;
}

[data-kilat-theme="retro"] .k-accent-arcade {
  color: var(--k-retro-cyan);
  font-family: 'Press Start 2P', monospace;
  font-size: 0.8em;
}

[data-kilat-theme="retro"] .k-accent-neon {
  background: var(--k-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}
