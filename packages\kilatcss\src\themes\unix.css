/**
 * KilatCSS Unix Theme 💻
 * Terminal and command-line inspired monospace theme
 */

[data-kilat-theme="unix"] {
  /* 💻 Unix Color Palette */
  --k-primary: #00ff00;        /* Terminal Green */
  --k-secondary: #ffff00;      /* Terminal Yellow */
  --k-accent: #ff8800;         /* Terminal Orange */
  --k-background: #000000;     /* Terminal Black */
  --k-surface: #1a1a1a;       /* Dark Gray */
  --k-text: #00ff00;          /* Green Text */
  --k-text-muted: #808080;    /* Gray Text */
  
  /* 🎨 Terminal Colors */
  --k-term-black: #000000;
  --k-term-red: #ff0000;
  --k-term-green: #00ff00;
  --k-term-yellow: #ffff00;
  --k-term-blue: #0080ff;
  --k-term-magenta: #ff00ff;
  --k-term-cyan: #00ffff;
  --k-term-white: #ffffff;
  
  /* 🌈 Bright Terminal Colors */
  --k-term-bright-black: #808080;
  --k-term-bright-red: #ff8080;
  --k-term-bright-green: #80ff80;
  --k-term-bright-yellow: #ffff80;
  --k-term-bright-blue: #8080ff;
  --k-term-bright-magenta: #ff80ff;
  --k-term-bright-cyan: #80ffff;
  --k-term-bright-white: #ffffff;
  
  /* 📟 Unix System Colors */
  --k-unix-prompt: #00ff00;
  --k-unix-path: #0080ff;
  --k-unix-command: #ffff00;
  --k-unix-output: #ffffff;
  --k-unix-error: #ff0000;
  --k-unix-warning: #ff8800;
  --k-unix-success: #00ff00;
  
  /* 🔲 Unix Borders */
  --k-border-ascii: 1px solid var(--k-term-green);
  --k-border-double: 3px double var(--k-term-green);
  --k-border-dashed: 1px dashed var(--k-term-green);
  
  /* ⚡ Unix Animations */
  --k-blink-speed: 1s;
  --k-type-speed: 0.05s;
  --k-scan-speed: 2s;
}

/* 💻 Unix Body Styling */
[data-kilat-theme="unix"] body,
[data-kilat-theme="unix"] .kilat {
  background: var(--k-background);
  color: var(--k-text);
  font-family: 'Fira Code', 'JetBrains Mono', 'Courier New', monospace;
  line-height: 1.4;
  letter-spacing: 0.05em;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
  font-variant-ligatures: common-ligatures;
}

/* Terminal Scanlines Effect */
[data-kilat-theme="unix"] body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 0, 0.03) 2px,
      rgba(0, 255, 0, 0.03) 4px
    );
  pointer-events: none;
  z-index: 1000;
}

/* 🎯 Unix Buttons */
[data-kilat-theme="unix"] .k-btn-unix {
  background: transparent;
  border: var(--k-border-ascii);
  color: var(--k-term-green);
  padding: 0.5rem 1rem;
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 400;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

[data-kilat-theme="unix"] .k-btn-unix::before {
  content: '> ';
  color: var(--k-unix-prompt);
}

[data-kilat-theme="unix"] .k-btn-unix:hover {
  background: rgba(0, 255, 0, 0.1);
  box-shadow: 
    0 0 10px rgba(0, 255, 0, 0.3),
    inset 0 0 10px rgba(0, 255, 0, 0.1);
  text-shadow: 0 0 5px currentColor;
}

[data-kilat-theme="unix"] .k-btn-unix:active {
  background: rgba(0, 255, 0, 0.2);
  transform: scale(0.98);
}

/* Unix Button Variants */
[data-kilat-theme="unix"] .k-btn-error {
  border-color: var(--k-term-red);
  color: var(--k-term-red);
}

[data-kilat-theme="unix"] .k-btn-error::before {
  content: '! ';
}

[data-kilat-theme="unix"] .k-btn-warning {
  border-color: var(--k-term-yellow);
  color: var(--k-term-yellow);
}

[data-kilat-theme="unix"] .k-btn-warning::before {
  content: '? ';
}

[data-kilat-theme="unix"] .k-btn-info {
  border-color: var(--k-term-cyan);
  color: var(--k-term-cyan);
}

[data-kilat-theme="unix"] .k-btn-info::before {
  content: 'i ';
}

/* 🎮 Unix Cards */
[data-kilat-theme="unix"] .k-card-unix {
  background: rgba(26, 26, 26, 0.9);
  border: var(--k-border-ascii);
  padding: 1rem;
  position: relative;
  margin: 1rem 0;
}

[data-kilat-theme="unix"] .k-card-unix::before {
  content: '┌─ PROCESS ─┐';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  background: var(--k-background);
  color: var(--k-term-green);
  padding: 0 0.5rem;
  font-size: 0.75rem;
}

[data-kilat-theme="unix"] .k-card-unix::after {
  content: '└─────────────┘';
  position: absolute;
  bottom: -0.5rem;
  left: 1rem;
  background: var(--k-background);
  color: var(--k-term-green);
  padding: 0 0.5rem;
  font-size: 0.75rem;
}

/* Unix Window Card */
[data-kilat-theme="unix"] .k-card-window {
  border: var(--k-border-double);
  position: relative;
}

[data-kilat-theme="unix"] .k-card-window::before {
  content: '╔═══ WINDOW ═══╗';
  position: absolute;
  top: -0.75rem;
  left: 1rem;
  background: var(--k-background);
  color: var(--k-term-green);
  padding: 0 0.5rem;
  font-size: 0.75rem;
}

/* 🔍 Unix Inputs */
[data-kilat-theme="unix"] .k-input-unix {
  background: transparent;
  border: none;
  border-bottom: 1px solid var(--k-term-green);
  color: var(--k-text);
  padding: 0.5rem 0;
  font-family: inherit;
  font-size: 1rem;
  outline: none;
  width: 100%;
  position: relative;
}

[data-kilat-theme="unix"] .k-input-unix::before {
  content: '$ ';
  color: var(--k-unix-prompt);
  position: absolute;
  left: -1.5rem;
}

[data-kilat-theme="unix"] .k-input-unix:focus {
  border-bottom-color: var(--k-term-bright-green);
  text-shadow: 0 0 5px currentColor;
  box-shadow: 0 2px 0 0 rgba(0, 255, 0, 0.3);
}

[data-kilat-theme="unix"] .k-input-unix::placeholder {
  color: var(--k-text-muted);
  opacity: 0.7;
}

/* Unix Terminal Input */
[data-kilat-theme="unix"] .k-input-terminal {
  background: var(--k-background);
  border: var(--k-border-ascii);
  color: var(--k-term-green);
  padding: 1rem;
  font-family: inherit;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 8rem;
}

[data-kilat-theme="unix"] .k-input-terminal:focus {
  outline: none;
  box-shadow: 
    0 0 10px rgba(0, 255, 0, 0.3),
    inset 0 0 10px rgba(0, 255, 0, 0.1);
}

/* 📋 Unix Lists */
[data-kilat-theme="unix"] .k-list-unix {
  background: transparent;
  border: var(--k-border-ascii);
  list-style: none;
  padding: 0;
  margin: 0;
}

[data-kilat-theme="unix"] .k-list-item-unix {
  padding: 0.5rem 1rem;
  border-bottom: 1px dashed rgba(0, 255, 0, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

[data-kilat-theme="unix"] .k-list-item-unix::before {
  content: '• ';
  color: var(--k-unix-prompt);
  margin-right: 0.5rem;
}

[data-kilat-theme="unix"] .k-list-item-unix:hover {
  background: rgba(0, 255, 0, 0.1);
  text-shadow: 0 0 5px currentColor;
}

[data-kilat-theme="unix"] .k-list-item-unix:hover::before {
  content: '▶ ';
  animation: blink var(--k-blink-speed) infinite;
}

[data-kilat-theme="unix"] .k-list-item-unix:last-child {
  border-bottom: none;
}

/* 📊 Unix Progress Bar */
[data-kilat-theme="unix"] .k-progress-unix {
  width: 100%;
  height: 1.5rem;
  background: transparent;
  border: var(--k-border-ascii);
  position: relative;
  overflow: hidden;
}

[data-kilat-theme="unix"] .k-progress-unix::before {
  content: 'LOADING...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  color: var(--k-term-green);
  z-index: 2;
}

[data-kilat-theme="unix"] .k-progress-unix-fill {
  height: 100%;
  background: 
    repeating-linear-gradient(
      90deg,
      rgba(0, 255, 0, 0.3),
      rgba(0, 255, 0, 0.3) 10px,
      rgba(0, 255, 0, 0.1) 10px,
      rgba(0, 255, 0, 0.1) 20px
    );
  transition: width 0.3s ease;
  position: relative;
}

[data-kilat-theme="unix"] .k-progress-unix-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: var(--k-term-bright-green);
  animation: blink var(--k-blink-speed) infinite;
}

/* 🎭 Unix Animations */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes cursor {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: var(--k-term-green); }
}

@keyframes scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

/* 🌐 Unix Scrollbar */
[data-kilat-theme="unix"] ::-webkit-scrollbar {
  width: 12px;
}

[data-kilat-theme="unix"] ::-webkit-scrollbar-track {
  background: var(--k-background);
  border: 1px solid var(--k-term-green);
}

[data-kilat-theme="unix"] ::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 0, 0.3);
  border: 1px solid var(--k-term-green);
}

[data-kilat-theme="unix"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 0, 0.5);
}

/* 🎨 Unix Utilities */
[data-kilat-theme="unix"] .k-text-prompt { color: var(--k-unix-prompt); }
[data-kilat-theme="unix"] .k-text-path { color: var(--k-unix-path); }
[data-kilat-theme="unix"] .k-text-command { color: var(--k-unix-command); }
[data-kilat-theme="unix"] .k-text-output { color: var(--k-unix-output); }
[data-kilat-theme="unix"] .k-text-error { color: var(--k-unix-error); }
[data-kilat-theme="unix"] .k-text-warning { color: var(--k-unix-warning); }
[data-kilat-theme="unix"] .k-text-success { color: var(--k-unix-success); }

[data-kilat-theme="unix"] .k-blink {
  animation: blink var(--k-blink-speed) infinite;
}

[data-kilat-theme="unix"] .k-typing {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid var(--k-term-green);
  animation: 
    typing 3s steps(40, end),
    cursor 0.5s step-end infinite;
}

/* 🔲 Unix Borders */
[data-kilat-theme="unix"] .k-border-ascii { border: var(--k-border-ascii); }
[data-kilat-theme="unix"] .k-border-double { border: var(--k-border-double); }
[data-kilat-theme="unix"] .k-border-dashed { border: var(--k-border-dashed); }

/* 📱 Unix Typography */
[data-kilat-theme="unix"] .k-mono { font-family: 'Fira Code', monospace; }
[data-kilat-theme="unix"] .k-code {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  padding: 0.25rem 0.5rem;
  font-family: inherit;
  font-size: 0.875rem;
}

[data-kilat-theme="unix"] .k-pre {
  background: rgba(26, 26, 26, 0.9);
  border: var(--k-border-ascii);
  padding: 1rem;
  overflow-x: auto;
  white-space: pre;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* 🎯 Unix Status Indicators */
[data-kilat-theme="unix"] .k-status-online::before {
  content: '● ';
  color: var(--k-term-green);
  animation: blink 2s infinite;
}

[data-kilat-theme="unix"] .k-status-offline::before {
  content: '● ';
  color: var(--k-term-red);
}

[data-kilat-theme="unix"] .k-status-pending::before {
  content: '◐ ';
  color: var(--k-term-yellow);
  animation: blink 1s infinite;
}
