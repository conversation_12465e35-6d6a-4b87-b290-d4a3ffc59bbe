/**
 * KilatCSS Animation Utilities ⚡
 * Transitions, transforms, and keyframe animations
 */

/* 🎭 Transitions */
.k-transition-none { transition: none; }
.k-transition-all { transition: all var(--k-transition-normal); }
.k-transition { transition: color var(--k-transition-normal), background-color var(--k-transition-normal), border-color var(--k-transition-normal), text-decoration-color var(--k-transition-normal), fill var(--k-transition-normal), stroke var(--k-transition-normal), opacity var(--k-transition-normal), box-shadow var(--k-transition-normal), transform var(--k-transition-normal), filter var(--k-transition-normal), backdrop-filter var(--k-transition-normal); }
.k-transition-colors { transition: color var(--k-transition-normal), background-color var(--k-transition-normal), border-color var(--k-transition-normal), text-decoration-color var(--k-transition-normal), fill var(--k-transition-normal), stroke var(--k-transition-normal); }
.k-transition-opacity { transition: opacity var(--k-transition-normal); }
.k-transition-shadow { transition: box-shadow var(--k-transition-normal); }
.k-transition-transform { transition: transform var(--k-transition-normal); }

/* ⏱️ Transition Duration */
.k-duration-75 { transition-duration: 75ms; }
.k-duration-100 { transition-duration: 100ms; }
.k-duration-150 { transition-duration: 150ms; }
.k-duration-200 { transition-duration: 200ms; }
.k-duration-300 { transition-duration: 300ms; }
.k-duration-500 { transition-duration: 500ms; }
.k-duration-700 { transition-duration: 700ms; }
.k-duration-1000 { transition-duration: 1000ms; }

/* 📈 Transition Timing Function */
.k-ease-linear { transition-timing-function: linear; }
.k-ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.k-ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.k-ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* ⏰ Transition Delay */
.k-delay-75 { transition-delay: 75ms; }
.k-delay-100 { transition-delay: 100ms; }
.k-delay-150 { transition-delay: 150ms; }
.k-delay-200 { transition-delay: 200ms; }
.k-delay-300 { transition-delay: 300ms; }
.k-delay-500 { transition-delay: 500ms; }
.k-delay-700 { transition-delay: 700ms; }
.k-delay-1000 { transition-delay: 1000ms; }

/* 🔄 Transforms */
.k-transform { transform: var(--k-transform-value, none); }
.k-transform-gpu { transform: translate3d(var(--k-translate-x, 0), var(--k-translate-y, 0), 0) rotate(var(--k-rotate, 0)) skewX(var(--k-skew-x, 0)) skewY(var(--k-skew-y, 0)) scaleX(var(--k-scale-x, 1)) scaleY(var(--k-scale-y, 1)); }
.k-transform-none { transform: none; }

/* 📍 Translate */
.k-translate-x-0 { --k-translate-x: 0px; }
.k-translate-x-1 { --k-translate-x: 0.25rem; }
.k-translate-x-2 { --k-translate-x: 0.5rem; }
.k-translate-x-3 { --k-translate-x: 0.75rem; }
.k-translate-x-4 { --k-translate-x: 1rem; }
.k-translate-x-5 { --k-translate-x: 1.25rem; }
.k-translate-x-6 { --k-translate-x: 1.5rem; }
.k-translate-x-8 { --k-translate-x: 2rem; }
.k-translate-x-10 { --k-translate-x: 2.5rem; }
.k-translate-x-12 { --k-translate-x: 3rem; }
.k-translate-x-16 { --k-translate-x: 4rem; }
.k-translate-x-20 { --k-translate-x: 5rem; }
.k-translate-x-24 { --k-translate-x: 6rem; }
.k-translate-x-32 { --k-translate-x: 8rem; }
.k-translate-x-40 { --k-translate-x: 10rem; }
.k-translate-x-48 { --k-translate-x: 12rem; }
.k-translate-x-56 { --k-translate-x: 14rem; }
.k-translate-x-64 { --k-translate-x: 16rem; }
.k-translate-x-1\/2 { --k-translate-x: 50%; }
.k-translate-x-1\/3 { --k-translate-x: 33.333333%; }
.k-translate-x-2\/3 { --k-translate-x: 66.666667%; }
.k-translate-x-1\/4 { --k-translate-x: 25%; }
.k-translate-x-2\/4 { --k-translate-x: 50%; }
.k-translate-x-3\/4 { --k-translate-x: 75%; }
.k-translate-x-full { --k-translate-x: 100%; }

.k-translate-y-0 { --k-translate-y: 0px; }
.k-translate-y-1 { --k-translate-y: 0.25rem; }
.k-translate-y-2 { --k-translate-y: 0.5rem; }
.k-translate-y-3 { --k-translate-y: 0.75rem; }
.k-translate-y-4 { --k-translate-y: 1rem; }
.k-translate-y-5 { --k-translate-y: 1.25rem; }
.k-translate-y-6 { --k-translate-y: 1.5rem; }
.k-translate-y-8 { --k-translate-y: 2rem; }
.k-translate-y-10 { --k-translate-y: 2.5rem; }
.k-translate-y-12 { --k-translate-y: 3rem; }
.k-translate-y-16 { --k-translate-y: 4rem; }
.k-translate-y-20 { --k-translate-y: 5rem; }
.k-translate-y-24 { --k-translate-y: 6rem; }
.k-translate-y-32 { --k-translate-y: 8rem; }
.k-translate-y-40 { --k-translate-y: 10rem; }
.k-translate-y-48 { --k-translate-y: 12rem; }
.k-translate-y-56 { --k-translate-y: 14rem; }
.k-translate-y-64 { --k-translate-y: 16rem; }
.k-translate-y-1\/2 { --k-translate-y: 50%; }
.k-translate-y-1\/3 { --k-translate-y: 33.333333%; }
.k-translate-y-2\/3 { --k-translate-y: 66.666667%; }
.k-translate-y-1\/4 { --k-translate-y: 25%; }
.k-translate-y-2\/4 { --k-translate-y: 50%; }
.k-translate-y-3\/4 { --k-translate-y: 75%; }
.k-translate-y-full { --k-translate-y: 100%; }

/* 🔄 Rotate */
.k-rotate-0 { --k-rotate: 0deg; }
.k-rotate-1 { --k-rotate: 1deg; }
.k-rotate-2 { --k-rotate: 2deg; }
.k-rotate-3 { --k-rotate: 3deg; }
.k-rotate-6 { --k-rotate: 6deg; }
.k-rotate-12 { --k-rotate: 12deg; }
.k-rotate-45 { --k-rotate: 45deg; }
.k-rotate-90 { --k-rotate: 90deg; }
.k-rotate-180 { --k-rotate: 180deg; }

/* 📏 Scale */
.k-scale-0 { --k-scale-x: 0; --k-scale-y: 0; }
.k-scale-50 { --k-scale-x: 0.5; --k-scale-y: 0.5; }
.k-scale-75 { --k-scale-x: 0.75; --k-scale-y: 0.75; }
.k-scale-90 { --k-scale-x: 0.9; --k-scale-y: 0.9; }
.k-scale-95 { --k-scale-x: 0.95; --k-scale-y: 0.95; }
.k-scale-100 { --k-scale-x: 1; --k-scale-y: 1; }
.k-scale-105 { --k-scale-x: 1.05; --k-scale-y: 1.05; }
.k-scale-110 { --k-scale-x: 1.1; --k-scale-y: 1.1; }
.k-scale-125 { --k-scale-x: 1.25; --k-scale-y: 1.25; }
.k-scale-150 { --k-scale-x: 1.5; --k-scale-y: 1.5; }

.k-scale-x-0 { --k-scale-x: 0; }
.k-scale-x-50 { --k-scale-x: 0.5; }
.k-scale-x-75 { --k-scale-x: 0.75; }
.k-scale-x-90 { --k-scale-x: 0.9; }
.k-scale-x-95 { --k-scale-x: 0.95; }
.k-scale-x-100 { --k-scale-x: 1; }
.k-scale-x-105 { --k-scale-x: 1.05; }
.k-scale-x-110 { --k-scale-x: 1.1; }
.k-scale-x-125 { --k-scale-x: 1.25; }
.k-scale-x-150 { --k-scale-x: 1.5; }

.k-scale-y-0 { --k-scale-y: 0; }
.k-scale-y-50 { --k-scale-y: 0.5; }
.k-scale-y-75 { --k-scale-y: 0.75; }
.k-scale-y-90 { --k-scale-y: 0.9; }
.k-scale-y-95 { --k-scale-y: 0.95; }
.k-scale-y-100 { --k-scale-y: 1; }
.k-scale-y-105 { --k-scale-y: 1.05; }
.k-scale-y-110 { --k-scale-y: 1.1; }
.k-scale-y-125 { --k-scale-y: 1.25; }
.k-scale-y-150 { --k-scale-y: 1.5; }

/* 🎭 Skew */
.k-skew-x-0 { --k-skew-x: 0deg; }
.k-skew-x-1 { --k-skew-x: 1deg; }
.k-skew-x-2 { --k-skew-x: 2deg; }
.k-skew-x-3 { --k-skew-x: 3deg; }
.k-skew-x-6 { --k-skew-x: 6deg; }
.k-skew-x-12 { --k-skew-x: 12deg; }

.k-skew-y-0 { --k-skew-y: 0deg; }
.k-skew-y-1 { --k-skew-y: 1deg; }
.k-skew-y-2 { --k-skew-y: 2deg; }
.k-skew-y-3 { --k-skew-y: 3deg; }
.k-skew-y-6 { --k-skew-y: 6deg; }
.k-skew-y-12 { --k-skew-y: 12deg; }

/* 🎬 Keyframe Animations */
.k-animate-none { animation: none; }
.k-animate-spin { animation: k-spin 1s linear infinite; }
.k-animate-ping { animation: k-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.k-animate-pulse { animation: k-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.k-animate-bounce { animation: k-bounce 1s infinite; }
.k-animate-glitch { animation: k-glitch 0.3s ease-in-out infinite; }
.k-animate-glow { animation: k-glow 2s ease-in-out infinite alternate; }
.k-animate-float { animation: k-float 3s ease-in-out infinite; }
.k-animate-slide-up { animation: k-slide-up 0.5s ease-out; }
.k-animate-slide-down { animation: k-slide-down 0.5s ease-out; }
.k-animate-slide-left { animation: k-slide-left 0.5s ease-out; }
.k-animate-slide-right { animation: k-slide-right 0.5s ease-out; }
.k-animate-fade-in { animation: k-fade-in 0.5s ease-out; }
.k-animate-fade-out { animation: k-fade-out 0.5s ease-out; }
.k-animate-zoom-in { animation: k-zoom-in 0.5s ease-out; }
.k-animate-zoom-out { animation: k-zoom-out 0.5s ease-out; }

/* 🎯 Keyframes */
@keyframes k-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes k-ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes k-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes k-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes k-glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes k-glow {
  0% { 
    box-shadow: 0 0 5px currentColor;
    filter: brightness(1);
  }
  100% { 
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    filter: brightness(1.2);
  }
}

@keyframes k-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes k-slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes k-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes k-slide-left {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes k-slide-right {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes k-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes k-fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes k-zoom-in {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes k-zoom-out {
  from {
    transform: scale(1.1);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 🌟 Cyberpunk Animations */
@keyframes k-cyber-pulse {
  0%, 100% {
    box-shadow: 
      0 0 5px var(--k-neon-blue),
      0 0 10px var(--k-neon-blue),
      0 0 15px var(--k-neon-blue);
  }
  50% {
    box-shadow: 
      0 0 10px var(--k-neon-blue),
      0 0 20px var(--k-neon-blue),
      0 0 30px var(--k-neon-blue),
      0 0 40px var(--k-neon-blue);
  }
}

@keyframes k-neon-flicker {
  0%, 100% { opacity: 1; }
  2% { opacity: 0.8; }
  4% { opacity: 1; }
  8% { opacity: 0.8; }
  10% { opacity: 1; }
  12% { opacity: 0.6; }
  14% { opacity: 1; }
  16% { opacity: 0.4; }
  18% { opacity: 1; }
  20% { opacity: 0.8; }
  22% { opacity: 1; }
}

@keyframes k-matrix-rain {
  0% { transform: translateY(-100vh); }
  100% { transform: translateY(100vh); }
}

.k-animate-cyber-pulse { animation: k-cyber-pulse 2s ease-in-out infinite; }
.k-animate-neon-flicker { animation: k-neon-flicker 3s linear infinite; }
.k-animate-matrix-rain { animation: k-matrix-rain 2s linear infinite; }

/* 🎮 Hover Animations */
.k-hover\:scale-105:hover { transform: scale(1.05); }
.k-hover\:scale-110:hover { transform: scale(1.1); }
.k-hover\:scale-125:hover { transform: scale(1.25); }
.k-hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.k-hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }
.k-hover\:rotate-6:hover { transform: rotate(6deg); }
.k-hover\:rotate-12:hover { transform: rotate(12deg); }

/* 📱 Responsive Animations */
@media (prefers-reduced-motion: reduce) {
  .k-animate-spin,
  .k-animate-ping,
  .k-animate-pulse,
  .k-animate-bounce,
  .k-animate-glitch,
  .k-animate-glow,
  .k-animate-float,
  .k-animate-cyber-pulse,
  .k-animate-neon-flicker,
  .k-animate-matrix-rain {
    animation: none;
  }
}
