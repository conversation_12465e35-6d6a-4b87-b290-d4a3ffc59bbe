/**
 * KilatCSS Color Utilities
 * Background, Text, Border colors with glow effects
 */

/* 🎨 Background Colors */
.k-bg-transparent { background-color: transparent; }
.k-bg-current { background-color: currentColor; }

/* Base Colors */
.k-bg-black { background-color: #000000; }
.k-bg-white { background-color: #ffffff; }
.k-bg-gray-50 { background-color: #f9fafb; }
.k-bg-gray-100 { background-color: #f3f4f6; }
.k-bg-gray-200 { background-color: #e5e7eb; }
.k-bg-gray-300 { background-color: #d1d5db; }
.k-bg-gray-400 { background-color: #9ca3af; }
.k-bg-gray-500 { background-color: #6b7280; }
.k-bg-gray-600 { background-color: #4b5563; }
.k-bg-gray-700 { background-color: #374151; }
.k-bg-gray-800 { background-color: #1f2937; }
.k-bg-gray-900 { background-color: #111827; }

/* Theme Colors */
.k-bg-primary { background-color: var(--k-primary); }
.k-bg-secondary { background-color: var(--k-secondary); }
.k-bg-accent { background-color: var(--k-accent); }
.k-bg-background { background-color: var(--k-background); }
.k-bg-surface { background-color: var(--k-surface); }

/* Neon Colors */
.k-bg-neon-blue { background-color: var(--k-neon-blue); }
.k-bg-neon-pink { background-color: var(--k-neon-pink); }
.k-bg-neon-green { background-color: var(--k-neon-green); }
.k-bg-neon-yellow { background-color: var(--k-neon-yellow); }
.k-bg-neon-purple { background-color: var(--k-neon-purple); }
.k-bg-neon-orange { background-color: var(--k-neon-orange); }
.k-bg-neon-red { background-color: var(--k-neon-red); }

/* Semantic Colors */
.k-bg-success { background-color: #10b981; }
.k-bg-warning { background-color: #f59e0b; }
.k-bg-error { background-color: #ef4444; }
.k-bg-info { background-color: #3b82f6; }

/* Dark/Light Mode Adaptive */
.k-bg-dark { background-color: var(--k-background); }
.k-bg-light { background-color: var(--k-surface); }

/* 🔤 Text Colors */
.k-text-transparent { color: transparent; }
.k-text-current { color: currentColor; }

/* Base Text Colors */
.k-text-black { color: #000000; }
.k-text-white { color: #ffffff; }
.k-text-gray-50 { color: #f9fafb; }
.k-text-gray-100 { color: #f3f4f6; }
.k-text-gray-200 { color: #e5e7eb; }
.k-text-gray-300 { color: #d1d5db; }
.k-text-gray-400 { color: #9ca3af; }
.k-text-gray-500 { color: #6b7280; }
.k-text-gray-600 { color: #4b5563; }
.k-text-gray-700 { color: #374151; }
.k-text-gray-800 { color: #1f2937; }
.k-text-gray-900 { color: #111827; }

/* Theme Text Colors */
.k-text-primary { color: var(--k-primary); }
.k-text-secondary { color: var(--k-secondary); }
.k-text-accent { color: var(--k-accent); }
.k-text-text { color: var(--k-text); }
.k-text-muted { color: var(--k-text-muted); }

/* Neon Text Colors */
.k-text-neon-blue { color: var(--k-neon-blue); }
.k-text-neon-pink { color: var(--k-neon-pink); }
.k-text-neon-green { color: var(--k-neon-green); }
.k-text-neon-yellow { color: var(--k-neon-yellow); }
.k-text-neon-purple { color: var(--k-neon-purple); }
.k-text-neon-orange { color: var(--k-neon-orange); }
.k-text-neon-red { color: var(--k-neon-red); }

/* Semantic Text Colors */
.k-text-success { color: #10b981; }
.k-text-warning { color: #f59e0b; }
.k-text-error { color: #ef4444; }
.k-text-info { color: #3b82f6; }

/* Dark/Light Mode Adaptive */
.k-text-dark { color: var(--k-text); }
.k-text-light { color: var(--k-text-muted); }

/* 🔲 Border Colors */
.k-border-transparent { border-color: transparent; }
.k-border-current { border-color: currentColor; }

/* Base Border Colors */
.k-border-black { border-color: #000000; }
.k-border-white { border-color: #ffffff; }
.k-border-gray-200 { border-color: #e5e7eb; }
.k-border-gray-300 { border-color: #d1d5db; }
.k-border-gray-400 { border-color: #9ca3af; }
.k-border-gray-500 { border-color: #6b7280; }
.k-border-gray-600 { border-color: #4b5563; }
.k-border-gray-700 { border-color: #374151; }
.k-border-gray-800 { border-color: #1f2937; }

/* Theme Border Colors */
.k-border-primary { border-color: var(--k-primary); }
.k-border-secondary { border-color: var(--k-secondary); }
.k-border-accent { border-color: var(--k-accent); }

/* Neon Border Colors */
.k-border-neon-blue { border-color: var(--k-neon-blue); }
.k-border-neon-pink { border-color: var(--k-neon-pink); }
.k-border-neon-green { border-color: var(--k-neon-green); }
.k-border-neon-yellow { border-color: var(--k-neon-yellow); }
.k-border-neon-purple { border-color: var(--k-neon-purple); }
.k-border-neon-orange { border-color: var(--k-neon-orange); }
.k-border-neon-red { border-color: var(--k-neon-red); }

/* Neon Border Colors with Glow */
.k-border-cyan { 
  border-color: var(--k-neon-blue); 
  box-shadow: 0 0 10px var(--k-neon-blue);
}

.k-border-magenta { 
  border-color: var(--k-neon-pink); 
  box-shadow: 0 0 10px var(--k-neon-pink);
}

.k-border-lime { 
  border-color: var(--k-neon-green); 
  box-shadow: 0 0 10px var(--k-neon-green);
}

/* 🌈 Gradient Backgrounds */
.k-bg-gradient-cyber {
  background: linear-gradient(135deg, var(--k-neon-blue), var(--k-neon-pink));
}

.k-bg-gradient-neon {
  background: linear-gradient(135deg, var(--k-neon-green), var(--k-neon-yellow));
}

.k-bg-gradient-sunset {
  background: linear-gradient(135deg, var(--k-neon-orange), var(--k-neon-red));
}

.k-bg-gradient-aurora {
  background: linear-gradient(135deg, var(--k-neon-purple), var(--k-neon-blue), var(--k-neon-green));
}

.k-bg-gradient-dark {
  background: linear-gradient(135deg, #000000, #1a1a1a, #333333);
}

/* 🔮 Shadow Effects */
.k-shadow-none { box-shadow: none; }
.k-shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.k-shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.k-shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.k-shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Cyber Shadow Effects */
.k-shadow-cyber {
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(255, 0, 255, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.3);
}

.k-shadow-neon {
  box-shadow: 
    0 0 15px currentColor,
    0 0 30px currentColor,
    0 4px 8px rgba(0, 0, 0, 0.3);
}

.k-shadow-glow {
  box-shadow: 
    0 0 10px currentColor,
    0 0 20px currentColor,
    0 0 30px currentColor;
}
