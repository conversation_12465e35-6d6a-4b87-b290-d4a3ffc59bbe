/**
 * KilatCSS Effects Utilities ⚡
 * Shadows, borders, filters, and visual effects
 */

/* 🌫️ Box Shadow */
.k-shadow-none { box-shadow: none; }
.k-shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.k-shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.k-shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.k-shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.k-shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.k-shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.k-shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }

/* 🔮 Neon Shadows */
.k-shadow-neon-blue { 
  box-shadow: 
    0 0 5px var(--k-neon-blue),
    0 0 10px var(--k-neon-blue),
    0 0 15px var(--k-neon-blue);
}

.k-shadow-neon-pink { 
  box-shadow: 
    0 0 5px var(--k-neon-pink),
    0 0 10px var(--k-neon-pink),
    0 0 15px var(--k-neon-pink);
}

.k-shadow-neon-green { 
  box-shadow: 
    0 0 5px var(--k-neon-green),
    0 0 10px var(--k-neon-green),
    0 0 15px var(--k-neon-green);
}

.k-shadow-neon-yellow { 
  box-shadow: 
    0 0 5px var(--k-neon-yellow),
    0 0 10px var(--k-neon-yellow),
    0 0 15px var(--k-neon-yellow);
}

.k-shadow-neon-purple { 
  box-shadow: 
    0 0 5px var(--k-neon-purple),
    0 0 10px var(--k-neon-purple),
    0 0 15px var(--k-neon-purple);
}

.k-shadow-neon-orange { 
  box-shadow: 
    0 0 5px var(--k-neon-orange),
    0 0 10px var(--k-neon-orange),
    0 0 15px var(--k-neon-orange);
}

.k-shadow-neon-red { 
  box-shadow: 
    0 0 5px var(--k-neon-red),
    0 0 10px var(--k-neon-red),
    0 0 15px var(--k-neon-red);
}

/* 🔲 Borders */
.k-border-0 { border-width: 0; }
.k-border { border-width: 1px; }
.k-border-2 { border-width: 2px; }
.k-border-4 { border-width: 4px; }
.k-border-8 { border-width: 8px; }

.k-border-t-0 { border-top-width: 0; }
.k-border-t { border-top-width: 1px; }
.k-border-t-2 { border-top-width: 2px; }
.k-border-t-4 { border-top-width: 4px; }

.k-border-r-0 { border-right-width: 0; }
.k-border-r { border-right-width: 1px; }
.k-border-r-2 { border-right-width: 2px; }
.k-border-r-4 { border-right-width: 4px; }

.k-border-b-0 { border-bottom-width: 0; }
.k-border-b { border-bottom-width: 1px; }
.k-border-b-2 { border-bottom-width: 2px; }
.k-border-b-4 { border-bottom-width: 4px; }

.k-border-l-0 { border-left-width: 0; }
.k-border-l { border-left-width: 1px; }
.k-border-l-2 { border-left-width: 2px; }
.k-border-l-4 { border-left-width: 4px; }

/* 🎨 Border Colors */
.k-border-transparent { border-color: transparent; }
.k-border-current { border-color: currentColor; }
.k-border-black { border-color: #000000; }
.k-border-white { border-color: #ffffff; }
.k-border-gray-100 { border-color: #f7fafc; }
.k-border-gray-200 { border-color: #edf2f7; }
.k-border-gray-300 { border-color: #e2e8f0; }
.k-border-gray-400 { border-color: #cbd5e0; }
.k-border-gray-500 { border-color: #a0aec0; }
.k-border-gray-600 { border-color: #718096; }
.k-border-gray-700 { border-color: #4a5568; }
.k-border-gray-800 { border-color: #2d3748; }
.k-border-gray-900 { border-color: #1a202c; }

.k-border-primary { border-color: var(--k-primary); }
.k-border-secondary { border-color: var(--k-secondary); }
.k-border-accent { border-color: var(--k-accent); }

.k-border-neon-blue { border-color: var(--k-neon-blue); }
.k-border-neon-pink { border-color: var(--k-neon-pink); }
.k-border-neon-green { border-color: var(--k-neon-green); }
.k-border-neon-yellow { border-color: var(--k-neon-yellow); }
.k-border-neon-purple { border-color: var(--k-neon-purple); }
.k-border-neon-orange { border-color: var(--k-neon-orange); }
.k-border-neon-red { border-color: var(--k-neon-red); }

/* 🔄 Border Style */
.k-border-solid { border-style: solid; }
.k-border-dashed { border-style: dashed; }
.k-border-dotted { border-style: dotted; }
.k-border-double { border-style: double; }
.k-border-none { border-style: none; }

/* 🌊 Border Radius */
.k-rounded-none { border-radius: 0; }
.k-rounded-sm { border-radius: var(--k-radius-sm); }
.k-rounded { border-radius: var(--k-radius-md); }
.k-rounded-md { border-radius: var(--k-radius-md); }
.k-rounded-lg { border-radius: var(--k-radius-lg); }
.k-rounded-xl { border-radius: var(--k-radius-xl); }
.k-rounded-2xl { border-radius: 1rem; }
.k-rounded-3xl { border-radius: 1.5rem; }
.k-rounded-full { border-radius: var(--k-radius-full); }

.k-rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }
.k-rounded-t-sm { border-top-left-radius: var(--k-radius-sm); border-top-right-radius: var(--k-radius-sm); }
.k-rounded-t { border-top-left-radius: var(--k-radius-md); border-top-right-radius: var(--k-radius-md); }
.k-rounded-t-lg { border-top-left-radius: var(--k-radius-lg); border-top-right-radius: var(--k-radius-lg); }

.k-rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.k-rounded-r-sm { border-top-right-radius: var(--k-radius-sm); border-bottom-right-radius: var(--k-radius-sm); }
.k-rounded-r { border-top-right-radius: var(--k-radius-md); border-bottom-right-radius: var(--k-radius-md); }
.k-rounded-r-lg { border-top-right-radius: var(--k-radius-lg); border-bottom-right-radius: var(--k-radius-lg); }

.k-rounded-b-none { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }
.k-rounded-b-sm { border-bottom-right-radius: var(--k-radius-sm); border-bottom-left-radius: var(--k-radius-sm); }
.k-rounded-b { border-bottom-right-radius: var(--k-radius-md); border-bottom-left-radius: var(--k-radius-md); }
.k-rounded-b-lg { border-bottom-right-radius: var(--k-radius-lg); border-bottom-left-radius: var(--k-radius-lg); }

.k-rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }
.k-rounded-l-sm { border-top-left-radius: var(--k-radius-sm); border-bottom-left-radius: var(--k-radius-sm); }
.k-rounded-l { border-top-left-radius: var(--k-radius-md); border-bottom-left-radius: var(--k-radius-md); }
.k-rounded-l-lg { border-top-left-radius: var(--k-radius-lg); border-bottom-left-radius: var(--k-radius-lg); }

/* 🎭 Opacity */
.k-opacity-0 { opacity: 0; }
.k-opacity-5 { opacity: 0.05; }
.k-opacity-10 { opacity: 0.1; }
.k-opacity-20 { opacity: 0.2; }
.k-opacity-25 { opacity: 0.25; }
.k-opacity-30 { opacity: 0.3; }
.k-opacity-40 { opacity: 0.4; }
.k-opacity-50 { opacity: 0.5; }
.k-opacity-60 { opacity: 0.6; }
.k-opacity-70 { opacity: 0.7; }
.k-opacity-75 { opacity: 0.75; }
.k-opacity-80 { opacity: 0.8; }
.k-opacity-90 { opacity: 0.9; }
.k-opacity-95 { opacity: 0.95; }
.k-opacity-100 { opacity: 1; }

/* 🔍 Filters */
.k-blur-none { filter: blur(0); }
.k-blur-sm { filter: blur(4px); }
.k-blur { filter: blur(8px); }
.k-blur-md { filter: blur(12px); }
.k-blur-lg { filter: blur(16px); }
.k-blur-xl { filter: blur(24px); }
.k-blur-2xl { filter: blur(40px); }
.k-blur-3xl { filter: blur(64px); }

.k-brightness-0 { filter: brightness(0); }
.k-brightness-50 { filter: brightness(0.5); }
.k-brightness-75 { filter: brightness(0.75); }
.k-brightness-90 { filter: brightness(0.9); }
.k-brightness-95 { filter: brightness(0.95); }
.k-brightness-100 { filter: brightness(1); }
.k-brightness-105 { filter: brightness(1.05); }
.k-brightness-110 { filter: brightness(1.1); }
.k-brightness-125 { filter: brightness(1.25); }
.k-brightness-150 { filter: brightness(1.5); }
.k-brightness-200 { filter: brightness(2); }

.k-contrast-0 { filter: contrast(0); }
.k-contrast-50 { filter: contrast(0.5); }
.k-contrast-75 { filter: contrast(0.75); }
.k-contrast-100 { filter: contrast(1); }
.k-contrast-125 { filter: contrast(1.25); }
.k-contrast-150 { filter: contrast(1.5); }
.k-contrast-200 { filter: contrast(2); }

.k-grayscale-0 { filter: grayscale(0); }
.k-grayscale { filter: grayscale(100%); }

.k-invert-0 { filter: invert(0); }
.k-invert { filter: invert(100%); }

.k-saturate-0 { filter: saturate(0); }
.k-saturate-50 { filter: saturate(0.5); }
.k-saturate-100 { filter: saturate(1); }
.k-saturate-150 { filter: saturate(1.5); }
.k-saturate-200 { filter: saturate(2); }

.k-sepia-0 { filter: sepia(0); }
.k-sepia { filter: sepia(100%); }

/* 🌈 Backdrop Filters */
.k-backdrop-blur-none { backdrop-filter: blur(0); }
.k-backdrop-blur-sm { backdrop-filter: blur(4px); }
.k-backdrop-blur { backdrop-filter: blur(8px); }
.k-backdrop-blur-md { backdrop-filter: blur(12px); }
.k-backdrop-blur-lg { backdrop-filter: blur(16px); }
.k-backdrop-blur-xl { backdrop-filter: blur(24px); }
.k-backdrop-blur-2xl { backdrop-filter: blur(40px); }
.k-backdrop-blur-3xl { backdrop-filter: blur(64px); }

.k-backdrop-brightness-50 { backdrop-filter: brightness(0.5); }
.k-backdrop-brightness-75 { backdrop-filter: brightness(0.75); }
.k-backdrop-brightness-90 { backdrop-filter: brightness(0.9); }
.k-backdrop-brightness-95 { backdrop-filter: brightness(0.95); }
.k-backdrop-brightness-100 { backdrop-filter: brightness(1); }
.k-backdrop-brightness-105 { backdrop-filter: brightness(1.05); }
.k-backdrop-brightness-110 { backdrop-filter: brightness(1.1); }
.k-backdrop-brightness-125 { backdrop-filter: brightness(1.25); }

/* 🔮 Cyberpunk Effects */
.k-cyber-border {
  border: 2px solid var(--k-neon-blue);
  box-shadow: 
    0 0 5px var(--k-neon-blue),
    inset 0 0 5px var(--k-neon-blue);
  position: relative;
}

.k-cyber-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--k-neon-blue), var(--k-neon-pink), var(--k-neon-green));
  z-index: -1;
  border-radius: inherit;
  opacity: 0.3;
  animation: k-cyber-glow 2s ease-in-out infinite alternate;
}

@keyframes k-cyber-glow {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

.k-hologram-effect {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation: k-hologram-scan 3s linear infinite;
}

@keyframes k-hologram-scan {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

.k-matrix-effect {
  background: 
    radial-gradient(circle at 20% 50%, var(--k-neon-green) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, var(--k-neon-blue) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--k-neon-pink) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--k-neon-yellow) 0%, transparent 50%);
  background-size: 300% 300%;
  animation: k-matrix-flow 4s ease-in-out infinite;
}

@keyframes k-matrix-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
