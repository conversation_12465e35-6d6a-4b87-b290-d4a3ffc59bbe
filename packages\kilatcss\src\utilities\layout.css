/**
 * KilatCSS Layout Utilities ⚡
 * Display, positioning, flexbox, grid, and layout utilities
 */

/* 📦 Display */
.k-block { display: block; }
.k-inline-block { display: inline-block; }
.k-inline { display: inline; }
.k-flex { display: flex; }
.k-inline-flex { display: inline-flex; }
.k-grid { display: grid; }
.k-inline-grid { display: inline-grid; }
.k-table { display: table; }
.k-table-row { display: table-row; }
.k-table-cell { display: table-cell; }
.k-hidden { display: none; }

/* 🎯 Position */
.k-static { position: static; }
.k-fixed { position: fixed; }
.k-absolute { position: absolute; }
.k-relative { position: relative; }
.k-sticky { position: sticky; }

/* 📍 Top, Right, Bottom, Left */
.k-inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.k-inset-x-0 { left: 0; right: 0; }
.k-inset-y-0 { top: 0; bottom: 0; }
.k-top-0 { top: 0; }
.k-right-0 { right: 0; }
.k-bottom-0 { bottom: 0; }
.k-left-0 { left: 0; }
.k-top-1 { top: 0.25rem; }
.k-right-1 { right: 0.25rem; }
.k-bottom-1 { bottom: 0.25rem; }
.k-left-1 { left: 0.25rem; }
.k-top-2 { top: 0.5rem; }
.k-right-2 { right: 0.5rem; }
.k-bottom-2 { bottom: 0.5rem; }
.k-left-2 { left: 0.5rem; }
.k-top-4 { top: 1rem; }
.k-right-4 { right: 1rem; }
.k-bottom-4 { bottom: 1rem; }
.k-left-4 { left: 1rem; }

/* 🔄 Flexbox */
.k-flex-row { flex-direction: row; }
.k-flex-row-reverse { flex-direction: row-reverse; }
.k-flex-col { flex-direction: column; }
.k-flex-col-reverse { flex-direction: column-reverse; }

.k-flex-wrap { flex-wrap: wrap; }
.k-flex-wrap-reverse { flex-wrap: wrap-reverse; }
.k-flex-nowrap { flex-wrap: nowrap; }

.k-flex-1 { flex: 1 1 0%; }
.k-flex-auto { flex: 1 1 auto; }
.k-flex-initial { flex: 0 1 auto; }
.k-flex-none { flex: none; }

.k-flex-grow { flex-grow: 1; }
.k-flex-grow-0 { flex-grow: 0; }
.k-flex-shrink { flex-shrink: 1; }
.k-flex-shrink-0 { flex-shrink: 0; }

/* 🎯 Justify Content */
.k-justify-start { justify-content: flex-start; }
.k-justify-end { justify-content: flex-end; }
.k-justify-center { justify-content: center; }
.k-justify-between { justify-content: space-between; }
.k-justify-around { justify-content: space-around; }
.k-justify-evenly { justify-content: space-evenly; }

/* 🎯 Align Items */
.k-items-start { align-items: flex-start; }
.k-items-end { align-items: flex-end; }
.k-items-center { align-items: center; }
.k-items-baseline { align-items: baseline; }
.k-items-stretch { align-items: stretch; }

/* 🎯 Align Content */
.k-content-start { align-content: flex-start; }
.k-content-end { align-content: flex-end; }
.k-content-center { align-content: center; }
.k-content-between { align-content: space-between; }
.k-content-around { align-content: space-around; }
.k-content-evenly { align-content: space-evenly; }

/* 🎯 Align Self */
.k-self-auto { align-self: auto; }
.k-self-start { align-self: flex-start; }
.k-self-end { align-self: flex-end; }
.k-self-center { align-self: center; }
.k-self-stretch { align-self: stretch; }
.k-self-baseline { align-self: baseline; }

/* 🔲 Grid */
.k-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.k-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.k-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.k-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.k-grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.k-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.k-grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.k-grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.k-grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.k-grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.k-grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.k-grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.k-grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

.k-col-span-1 { grid-column: span 1 / span 1; }
.k-col-span-2 { grid-column: span 2 / span 2; }
.k-col-span-3 { grid-column: span 3 / span 3; }
.k-col-span-4 { grid-column: span 4 / span 4; }
.k-col-span-5 { grid-column: span 5 / span 5; }
.k-col-span-6 { grid-column: span 6 / span 6; }
.k-col-span-full { grid-column: 1 / -1; }

.k-row-span-1 { grid-row: span 1 / span 1; }
.k-row-span-2 { grid-row: span 2 / span 2; }
.k-row-span-3 { grid-row: span 3 / span 3; }
.k-row-span-4 { grid-row: span 4 / span 4; }
.k-row-span-5 { grid-row: span 5 / span 5; }
.k-row-span-6 { grid-row: span 6 / span 6; }
.k-row-span-full { grid-row: 1 / -1; }

.k-gap-0 { gap: 0; }
.k-gap-1 { gap: 0.25rem; }
.k-gap-2 { gap: 0.5rem; }
.k-gap-3 { gap: 0.75rem; }
.k-gap-4 { gap: 1rem; }
.k-gap-5 { gap: 1.25rem; }
.k-gap-6 { gap: 1.5rem; }
.k-gap-8 { gap: 2rem; }

/* 📏 Width */
.k-w-0 { width: 0; }
.k-w-1 { width: 0.25rem; }
.k-w-2 { width: 0.5rem; }
.k-w-3 { width: 0.75rem; }
.k-w-4 { width: 1rem; }
.k-w-5 { width: 1.25rem; }
.k-w-6 { width: 1.5rem; }
.k-w-8 { width: 2rem; }
.k-w-10 { width: 2.5rem; }
.k-w-12 { width: 3rem; }
.k-w-16 { width: 4rem; }
.k-w-20 { width: 5rem; }
.k-w-24 { width: 6rem; }
.k-w-32 { width: 8rem; }
.k-w-40 { width: 10rem; }
.k-w-48 { width: 12rem; }
.k-w-56 { width: 14rem; }
.k-w-64 { width: 16rem; }
.k-w-auto { width: auto; }
.k-w-1\/2 { width: 50%; }
.k-w-1\/3 { width: 33.333333%; }
.k-w-2\/3 { width: 66.666667%; }
.k-w-1\/4 { width: 25%; }
.k-w-2\/4 { width: 50%; }
.k-w-3\/4 { width: 75%; }
.k-w-full { width: 100%; }
.k-w-screen { width: 100vw; }

/* 📏 Height */
.k-h-0 { height: 0; }
.k-h-1 { height: 0.25rem; }
.k-h-2 { height: 0.5rem; }
.k-h-3 { height: 0.75rem; }
.k-h-4 { height: 1rem; }
.k-h-5 { height: 1.25rem; }
.k-h-6 { height: 1.5rem; }
.k-h-8 { height: 2rem; }
.k-h-10 { height: 2.5rem; }
.k-h-12 { height: 3rem; }
.k-h-16 { height: 4rem; }
.k-h-20 { height: 5rem; }
.k-h-24 { height: 6rem; }
.k-h-32 { height: 8rem; }
.k-h-40 { height: 10rem; }
.k-h-48 { height: 12rem; }
.k-h-56 { height: 14rem; }
.k-h-64 { height: 16rem; }
.k-h-auto { height: auto; }
.k-h-1\/2 { height: 50%; }
.k-h-1\/3 { height: 33.333333%; }
.k-h-2\/3 { height: 66.666667%; }
.k-h-1\/4 { height: 25%; }
.k-h-2\/4 { height: 50%; }
.k-h-3\/4 { height: 75%; }
.k-h-full { height: 100%; }
.k-h-screen { height: 100vh; }

/* 📏 Min/Max Width */
.k-min-w-0 { min-width: 0; }
.k-min-w-full { min-width: 100%; }
.k-min-w-min { min-width: min-content; }
.k-min-w-max { min-width: max-content; }

.k-max-w-0 { max-width: 0; }
.k-max-w-xs { max-width: 20rem; }
.k-max-w-sm { max-width: 24rem; }
.k-max-w-md { max-width: 28rem; }
.k-max-w-lg { max-width: 32rem; }
.k-max-w-xl { max-width: 36rem; }
.k-max-w-2xl { max-width: 42rem; }
.k-max-w-3xl { max-width: 48rem; }
.k-max-w-4xl { max-width: 56rem; }
.k-max-w-5xl { max-width: 64rem; }
.k-max-w-6xl { max-width: 72rem; }
.k-max-w-7xl { max-width: 80rem; }
.k-max-w-full { max-width: 100%; }
.k-max-w-screen-sm { max-width: 640px; }
.k-max-w-screen-md { max-width: 768px; }
.k-max-w-screen-lg { max-width: 1024px; }
.k-max-w-screen-xl { max-width: 1280px; }

/* 📏 Min/Max Height */
.k-min-h-0 { min-height: 0; }
.k-min-h-full { min-height: 100%; }
.k-min-h-screen { min-height: 100vh; }

.k-max-h-0 { max-height: 0; }
.k-max-h-full { max-height: 100%; }
.k-max-h-screen { max-height: 100vh; }

/* 🔄 Overflow */
.k-overflow-auto { overflow: auto; }
.k-overflow-hidden { overflow: hidden; }
.k-overflow-visible { overflow: visible; }
.k-overflow-scroll { overflow: scroll; }
.k-overflow-x-auto { overflow-x: auto; }
.k-overflow-y-auto { overflow-y: auto; }
.k-overflow-x-hidden { overflow-x: hidden; }
.k-overflow-y-hidden { overflow-y: hidden; }
.k-overflow-x-visible { overflow-x: visible; }
.k-overflow-y-visible { overflow-y: visible; }
.k-overflow-x-scroll { overflow-x: scroll; }
.k-overflow-y-scroll { overflow-y: scroll; }

/* 🎯 Z-Index */
.k-z-0 { z-index: 0; }
.k-z-10 { z-index: 10; }
.k-z-20 { z-index: 20; }
.k-z-30 { z-index: 30; }
.k-z-40 { z-index: 40; }
.k-z-50 { z-index: 50; }
.k-z-auto { z-index: auto; }

/* 📱 Responsive Layout */
@media (max-width: 640px) {
  .k-sm\:block { display: block; }
  .k-sm\:flex { display: flex; }
  .k-sm\:grid { display: grid; }
  .k-sm\:hidden { display: none; }
  .k-sm\:flex-col { flex-direction: column; }
  .k-sm\:flex-row { flex-direction: row; }
}

@media (min-width: 768px) {
  .k-md\:block { display: block; }
  .k-md\:flex { display: flex; }
  .k-md\:grid { display: grid; }
  .k-md\:hidden { display: none; }
  .k-md\:flex-col { flex-direction: column; }
  .k-md\:flex-row { flex-direction: row; }
}

@media (min-width: 1024px) {
  .k-lg\:block { display: block; }
  .k-lg\:flex { display: flex; }
  .k-lg\:grid { display: grid; }
  .k-lg\:hidden { display: none; }
  .k-lg\:flex-col { flex-direction: column; }
  .k-lg\:flex-row { flex-direction: row; }
}
