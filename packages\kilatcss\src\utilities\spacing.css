/**
 * KilatCSS Spacing Utilities
 * Padding, <PERSON>gin, Gap utilities with k- prefix
 */

/* 📦 Padding Utilities */
.k-p-0 { padding: 0; }
.k-p-1 { padding: var(--k-space-1); }
.k-p-2 { padding: var(--k-space-2); }
.k-p-3 { padding: var(--k-space-3); }
.k-p-4 { padding: var(--k-space-4); }
.k-p-5 { padding: var(--k-space-5); }
.k-p-6 { padding: var(--k-space-6); }
.k-p-8 { padding: var(--k-space-8); }
.k-p-10 { padding: var(--k-space-10); }
.k-p-12 { padding: var(--k-space-12); }
.k-p-16 { padding: var(--k-space-16); }
.k-p-20 { padding: var(--k-space-20); }
.k-p-24 { padding: var(--k-space-24); }

/* 📦 Padding X (Horizontal) */
.k-px-0 { padding-left: 0; padding-right: 0; }
.k-px-1 { padding-left: var(--k-space-1); padding-right: var(--k-space-1); }
.k-px-2 { padding-left: var(--k-space-2); padding-right: var(--k-space-2); }
.k-px-3 { padding-left: var(--k-space-3); padding-right: var(--k-space-3); }
.k-px-4 { padding-left: var(--k-space-4); padding-right: var(--k-space-4); }
.k-px-5 { padding-left: var(--k-space-5); padding-right: var(--k-space-5); }
.k-px-6 { padding-left: var(--k-space-6); padding-right: var(--k-space-6); }
.k-px-8 { padding-left: var(--k-space-8); padding-right: var(--k-space-8); }

/* 📦 Padding Y (Vertical) */
.k-py-0 { padding-top: 0; padding-bottom: 0; }
.k-py-1 { padding-top: var(--k-space-1); padding-bottom: var(--k-space-1); }
.k-py-2 { padding-top: var(--k-space-2); padding-bottom: var(--k-space-2); }
.k-py-3 { padding-top: var(--k-space-3); padding-bottom: var(--k-space-3); }
.k-py-4 { padding-top: var(--k-space-4); padding-bottom: var(--k-space-4); }
.k-py-5 { padding-top: var(--k-space-5); padding-bottom: var(--k-space-5); }
.k-py-6 { padding-top: var(--k-space-6); padding-bottom: var(--k-space-6); }
.k-py-8 { padding-top: var(--k-space-8); padding-bottom: var(--k-space-8); }

/* 📦 Individual Padding */
.k-pt-0 { padding-top: 0; }
.k-pt-1 { padding-top: var(--k-space-1); }
.k-pt-2 { padding-top: var(--k-space-2); }
.k-pt-3 { padding-top: var(--k-space-3); }
.k-pt-4 { padding-top: var(--k-space-4); }
.k-pt-6 { padding-top: var(--k-space-6); }
.k-pt-8 { padding-top: var(--k-space-8); }

.k-pr-0 { padding-right: 0; }
.k-pr-1 { padding-right: var(--k-space-1); }
.k-pr-2 { padding-right: var(--k-space-2); }
.k-pr-3 { padding-right: var(--k-space-3); }
.k-pr-4 { padding-right: var(--k-space-4); }
.k-pr-6 { padding-right: var(--k-space-6); }
.k-pr-8 { padding-right: var(--k-space-8); }

.k-pb-0 { padding-bottom: 0; }
.k-pb-1 { padding-bottom: var(--k-space-1); }
.k-pb-2 { padding-bottom: var(--k-space-2); }
.k-pb-3 { padding-bottom: var(--k-space-3); }
.k-pb-4 { padding-bottom: var(--k-space-4); }
.k-pb-6 { padding-bottom: var(--k-space-6); }
.k-pb-8 { padding-bottom: var(--k-space-8); }

.k-pl-0 { padding-left: 0; }
.k-pl-1 { padding-left: var(--k-space-1); }
.k-pl-2 { padding-left: var(--k-space-2); }
.k-pl-3 { padding-left: var(--k-space-3); }
.k-pl-4 { padding-left: var(--k-space-4); }
.k-pl-6 { padding-left: var(--k-space-6); }
.k-pl-8 { padding-left: var(--k-space-8); }

/* 📏 Margin Utilities */
.k-m-0 { margin: 0; }
.k-m-1 { margin: var(--k-space-1); }
.k-m-2 { margin: var(--k-space-2); }
.k-m-3 { margin: var(--k-space-3); }
.k-m-4 { margin: var(--k-space-4); }
.k-m-5 { margin: var(--k-space-5); }
.k-m-6 { margin: var(--k-space-6); }
.k-m-8 { margin: var(--k-space-8); }
.k-m-10 { margin: var(--k-space-10); }
.k-m-12 { margin: var(--k-space-12); }
.k-m-16 { margin: var(--k-space-16); }
.k-m-20 { margin: var(--k-space-20); }
.k-m-24 { margin: var(--k-space-24); }
.k-m-auto { margin: auto; }

/* 📏 Margin X (Horizontal) */
.k-mx-0 { margin-left: 0; margin-right: 0; }
.k-mx-1 { margin-left: var(--k-space-1); margin-right: var(--k-space-1); }
.k-mx-2 { margin-left: var(--k-space-2); margin-right: var(--k-space-2); }
.k-mx-3 { margin-left: var(--k-space-3); margin-right: var(--k-space-3); }
.k-mx-4 { margin-left: var(--k-space-4); margin-right: var(--k-space-4); }
.k-mx-5 { margin-left: var(--k-space-5); margin-right: var(--k-space-5); }
.k-mx-6 { margin-left: var(--k-space-6); margin-right: var(--k-space-6); }
.k-mx-8 { margin-left: var(--k-space-8); margin-right: var(--k-space-8); }
.k-mx-auto { margin-left: auto; margin-right: auto; }

/* 📏 Margin Y (Vertical) */
.k-my-0 { margin-top: 0; margin-bottom: 0; }
.k-my-1 { margin-top: var(--k-space-1); margin-bottom: var(--k-space-1); }
.k-my-2 { margin-top: var(--k-space-2); margin-bottom: var(--k-space-2); }
.k-my-3 { margin-top: var(--k-space-3); margin-bottom: var(--k-space-3); }
.k-my-4 { margin-top: var(--k-space-4); margin-bottom: var(--k-space-4); }
.k-my-5 { margin-top: var(--k-space-5); margin-bottom: var(--k-space-5); }
.k-my-6 { margin-top: var(--k-space-6); margin-bottom: var(--k-space-6); }
.k-my-8 { margin-top: var(--k-space-8); margin-bottom: var(--k-space-8); }

/* 📏 Individual Margin */
.k-mt-0 { margin-top: 0; }
.k-mt-1 { margin-top: var(--k-space-1); }
.k-mt-2 { margin-top: var(--k-space-2); }
.k-mt-3 { margin-top: var(--k-space-3); }
.k-mt-4 { margin-top: var(--k-space-4); }
.k-mt-6 { margin-top: var(--k-space-6); }
.k-mt-8 { margin-top: var(--k-space-8); }
.k-mt-10 { margin-top: var(--k-space-10); }
.k-mt-12 { margin-top: var(--k-space-12); }
.k-mt-16 { margin-top: var(--k-space-16); }
.k-mt-20 { margin-top: var(--k-space-20); }
.k-mt-24 { margin-top: var(--k-space-24); }

.k-mr-0 { margin-right: 0; }
.k-mr-1 { margin-right: var(--k-space-1); }
.k-mr-2 { margin-right: var(--k-space-2); }
.k-mr-3 { margin-right: var(--k-space-3); }
.k-mr-4 { margin-right: var(--k-space-4); }
.k-mr-6 { margin-right: var(--k-space-6); }
.k-mr-8 { margin-right: var(--k-space-8); }

.k-mb-0 { margin-bottom: 0; }
.k-mb-1 { margin-bottom: var(--k-space-1); }
.k-mb-2 { margin-bottom: var(--k-space-2); }
.k-mb-3 { margin-bottom: var(--k-space-3); }
.k-mb-4 { margin-bottom: var(--k-space-4); }
.k-mb-6 { margin-bottom: var(--k-space-6); }
.k-mb-8 { margin-bottom: var(--k-space-8); }
.k-mb-10 { margin-bottom: var(--k-space-10); }
.k-mb-12 { margin-bottom: var(--k-space-12); }
.k-mb-16 { margin-bottom: var(--k-space-16); }
.k-mb-20 { margin-bottom: var(--k-space-20); }
.k-mb-24 { margin-bottom: var(--k-space-24); }

.k-ml-0 { margin-left: 0; }
.k-ml-1 { margin-left: var(--k-space-1); }
.k-ml-2 { margin-left: var(--k-space-2); }
.k-ml-3 { margin-left: var(--k-space-3); }
.k-ml-4 { margin-left: var(--k-space-4); }
.k-ml-6 { margin-left: var(--k-space-6); }
.k-ml-8 { margin-left: var(--k-space-8); }

/* 🔗 Gap Utilities (for Flexbox/Grid) */
.k-gap-0 { gap: 0; }
.k-gap-1 { gap: var(--k-space-1); }
.k-gap-2 { gap: var(--k-space-2); }
.k-gap-3 { gap: var(--k-space-3); }
.k-gap-4 { gap: var(--k-space-4); }
.k-gap-5 { gap: var(--k-space-5); }
.k-gap-6 { gap: var(--k-space-6); }
.k-gap-8 { gap: var(--k-space-8); }
.k-gap-10 { gap: var(--k-space-10); }
.k-gap-12 { gap: var(--k-space-12); }

.k-gap-x-0 { column-gap: 0; }
.k-gap-x-1 { column-gap: var(--k-space-1); }
.k-gap-x-2 { column-gap: var(--k-space-2); }
.k-gap-x-3 { column-gap: var(--k-space-3); }
.k-gap-x-4 { column-gap: var(--k-space-4); }
.k-gap-x-6 { column-gap: var(--k-space-6); }
.k-gap-x-8 { column-gap: var(--k-space-8); }

.k-gap-y-0 { row-gap: 0; }
.k-gap-y-1 { row-gap: var(--k-space-1); }
.k-gap-y-2 { row-gap: var(--k-space-2); }
.k-gap-y-3 { row-gap: var(--k-space-3); }
.k-gap-y-4 { row-gap: var(--k-space-4); }
.k-gap-y-6 { row-gap: var(--k-space-6); }
.k-gap-y-8 { row-gap: var(--k-space-8); }
