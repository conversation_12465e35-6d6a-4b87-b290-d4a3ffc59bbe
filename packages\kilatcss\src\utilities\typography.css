/**
 * KilatCSS Typography Utilities ⚡
 * Font families, sizes, weights, and text effects
 */

/* 🔤 Font Families */
.k-font-sans { font-family: var(--k-font-sans); }
.k-font-mono { font-family: var(--k-font-mono); }
.k-font-display { font-family: var(--k-font-display); }

/* 📏 Font Sizes */
.k-text-xs { font-size: 0.75rem; line-height: 1rem; }
.k-text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.k-text-base { font-size: 1rem; line-height: 1.5rem; }
.k-text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.k-text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.k-text-2xl { font-size: 1.5rem; line-height: 2rem; }
.k-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.k-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.k-text-5xl { font-size: 3rem; line-height: 1; }
.k-text-6xl { font-size: 3.75rem; line-height: 1; }
.k-text-7xl { font-size: 4.5rem; line-height: 1; }
.k-text-8xl { font-size: 6rem; line-height: 1; }
.k-text-9xl { font-size: 8rem; line-height: 1; }

/* ⚖️ Font Weights */
.k-font-thin { font-weight: 100; }
.k-font-extralight { font-weight: 200; }
.k-font-light { font-weight: 300; }
.k-font-normal { font-weight: 400; }
.k-font-medium { font-weight: 500; }
.k-font-semibold { font-weight: 600; }
.k-font-bold { font-weight: 700; }
.k-font-extrabold { font-weight: 800; }
.k-font-black { font-weight: 900; }

/* 📐 Text Alignment */
.k-text-left { text-align: left; }
.k-text-center { text-align: center; }
.k-text-right { text-align: right; }
.k-text-justify { text-align: justify; }

/* 🎨 Text Colors */
.k-text-primary { color: var(--k-primary); }
.k-text-secondary { color: var(--k-secondary); }
.k-text-accent { color: var(--k-accent); }
.k-text-light { color: var(--k-text); }
.k-text-muted { color: var(--k-text-muted); }
.k-text-white { color: #ffffff; }
.k-text-black { color: #000000; }
.k-text-gray-100 { color: #f7fafc; }
.k-text-gray-200 { color: #edf2f7; }
.k-text-gray-300 { color: #e2e8f0; }
.k-text-gray-400 { color: #cbd5e0; }
.k-text-gray-500 { color: #a0aec0; }
.k-text-gray-600 { color: #718096; }
.k-text-gray-700 { color: #4a5568; }
.k-text-gray-800 { color: #2d3748; }
.k-text-gray-900 { color: #1a202c; }

/* 🌈 Neon Text Colors */
.k-text-neon-blue { color: var(--k-neon-blue); }
.k-text-neon-pink { color: var(--k-neon-pink); }
.k-text-neon-green { color: var(--k-neon-green); }
.k-text-neon-yellow { color: var(--k-neon-yellow); }
.k-text-neon-purple { color: var(--k-neon-purple); }
.k-text-neon-orange { color: var(--k-neon-orange); }
.k-text-neon-red { color: var(--k-neon-red); }

/* 📏 Line Height */
.k-leading-none { line-height: 1; }
.k-leading-tight { line-height: 1.25; }
.k-leading-snug { line-height: 1.375; }
.k-leading-normal { line-height: 1.5; }
.k-leading-relaxed { line-height: 1.625; }
.k-leading-loose { line-height: 2; }

/* 📝 Text Decoration */
.k-underline { text-decoration: underline; }
.k-line-through { text-decoration: line-through; }
.k-no-underline { text-decoration: none; }

/* 🔤 Text Transform */
.k-uppercase { text-transform: uppercase; }
.k-lowercase { text-transform: lowercase; }
.k-capitalize { text-transform: capitalize; }
.k-normal-case { text-transform: none; }

/* 📖 Text Overflow */
.k-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.k-text-ellipsis { text-overflow: ellipsis; }
.k-text-clip { text-overflow: clip; }

/* 🔤 White Space */
.k-whitespace-normal { white-space: normal; }
.k-whitespace-nowrap { white-space: nowrap; }
.k-whitespace-pre { white-space: pre; }
.k-whitespace-pre-line { white-space: pre-line; }
.k-whitespace-pre-wrap { white-space: pre-wrap; }

/* 📏 Letter Spacing */
.k-tracking-tighter { letter-spacing: -0.05em; }
.k-tracking-tight { letter-spacing: -0.025em; }
.k-tracking-normal { letter-spacing: 0em; }
.k-tracking-wide { letter-spacing: 0.025em; }
.k-tracking-wider { letter-spacing: 0.05em; }
.k-tracking-widest { letter-spacing: 0.1em; }

/* 🎭 Text Effects */
.k-text-shadow-sm { text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); }
.k-text-shadow { text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); }
.k-text-shadow-md { text-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06); }
.k-text-shadow-lg { text-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05); }
.k-text-shadow-xl { text-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04); }
.k-text-shadow-none { text-shadow: none; }

/* 🔮 Cyberpunk Text Effects */
.k-text-cyber {
  font-family: var(--k-font-display);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

.k-text-glitch {
  position: relative;
  font-family: var(--k-font-display);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.k-text-glitch::before,
.k-text-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.k-text-glitch::before {
  animation: k-glitch-1 0.5s infinite;
  color: var(--k-neon-pink);
  z-index: -1;
}

.k-text-glitch::after {
  animation: k-glitch-2 0.5s infinite;
  color: var(--k-neon-blue);
  z-index: -2;
}

@keyframes k-glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0, 0); }
  15%, 49% { transform: translate(-2px, -1px); }
}

@keyframes k-glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0, 0); }
  21%, 62% { transform: translate(2px, 1px); }
}

/* 🌟 Hologram Text Effect */
.k-text-hologram {
  background: linear-gradient(
    45deg,
    var(--k-neon-blue),
    var(--k-neon-pink),
    var(--k-neon-green),
    var(--k-neon-yellow)
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: k-hologram 3s ease-in-out infinite;
}

@keyframes k-hologram {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 📱 Responsive Typography */
@media (max-width: 640px) {
  .k-sm\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-sm\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
}

@media (min-width: 768px) {
  .k-md\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-md\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-md\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .k-md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .k-md\:text-5xl { font-size: 3rem; line-height: 1; }
}

@media (min-width: 1024px) {
  .k-lg\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .k-lg\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .k-lg\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .k-lg\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .k-lg\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .k-lg\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .k-lg\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .k-lg\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .k-lg\:text-5xl { font-size: 3rem; line-height: 1; }
  .k-lg\:text-6xl { font-size: 3.75rem; line-height: 1; }
}
