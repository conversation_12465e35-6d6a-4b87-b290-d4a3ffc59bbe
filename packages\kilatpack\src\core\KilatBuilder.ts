import * as esbuild from 'esbuild';
import { createLogger } from 'kilat-utils';
import type { 
  KilatPackConfig, 
  BuildResult, 
  DevServer, 
  KilatPackContext,
  PerformanceMetrics 
} from '../types';
import { DevServer as DevServerImpl } from '../dev/DevServer';
import { HMRManager } from '../dev/HMRManager';
import { bundleAnalyzer } from '../utils/analyzer';
import { optimizeBundle } from '../utils/optimizer';

/**
 * 🏗️ KilatBuilder - Main build orchestrator
 * Handles both development and production builds
 */
export class KilatBuilder {
  private config: KilatPackConfig;
  private context: KilatPackContext;
  private logger = createLogger({ level: 'info' });
  private devServer?: DevServerImpl;
  private hmrManager?: HMRManager;
  private buildContext?: esbuild.BuildContext;

  constructor(config: KilatPackConfig) {
    this.config = config;
    this.context = {
      config,
      mode: config.mode,
      command: 'build',
      isProduction: config.mode === 'production',
      isDevelopment: config.mode === 'development',
      logger: this.logger
    };

    if (this.config.debug?.enabled) {
      this.logger.info('KilatBuilder initialized', { config: this.config });
    }
  }

  // 🏗️ Production Build
  async build(): Promise<BuildResult> {
    const startTime = Date.now();
    this.context.command = 'build';
    
    this.logger.info('🚀 Starting production build...');

    try {
      // Prepare build options
      const buildOptions = await this.prepareBuildOptions();
      
      // Run esbuild
      const result = await esbuild.build(buildOptions);
      
      // Process results
      const buildResult = await this.processBuildResult(result, startTime);
      
      // Run analysis if enabled
      if (this.config.analyze?.enabled) {
        await this.runAnalysis(buildResult);
      }
      
      // Optimize bundle if needed
      if (this.config.mode === 'production') {
        await this.optimizeOutput(buildResult);
      }
      
      this.logger.success(`✅ Build completed in ${buildResult.duration}ms`);
      
      return buildResult;
      
    } catch (error) {
      this.logger.error('❌ Build failed:', error);
      throw error;
    }
  }

  // 🔥 Development Server
  async serve(): Promise<DevServer> {
    this.context.command = 'serve';
    
    this.logger.info('🔥 Starting development server...');

    try {
      // Create build context for watch mode
      const buildOptions = await this.prepareBuildOptions();
      buildOptions.write = false; // Don't write to disk in dev mode
      
      this.buildContext = await esbuild.context(buildOptions);
      
      // Start watching
      await this.buildContext.watch();
      
      // Initialize HMR if enabled
      if (this.config.hmr?.enabled) {
        this.hmrManager = new HMRManager(this.config, this.logger);
      }
      
      // Create and start dev server
      this.devServer = new DevServerImpl(this.config, this.buildContext, this.hmrManager);
      await this.devServer.start();
      
      this.logger.success(`✅ Dev server running at ${this.devServer.url}`);
      
      return this.devServer;
      
    } catch (error) {
      this.logger.error('❌ Failed to start dev server:', error);
      throw error;
    }
  }

  // 👀 Preview Build
  async preview(): Promise<DevServer> {
    this.context.command = 'preview';
    
    this.logger.info('👀 Starting preview server...');

    try {
      // First build for production
      await this.build();
      
      // Then serve the built files
      this.devServer = new DevServerImpl(this.config, undefined, undefined, true);
      await this.devServer.start();
      
      this.logger.success(`✅ Preview server running at ${this.devServer.url}`);
      
      return this.devServer;
      
    } catch (error) {
      this.logger.error('❌ Failed to start preview server:', error);
      throw error;
    }
  }

  // 🛑 Stop all services
  async stop(): Promise<void> {
    if (this.devServer) {
      await this.devServer.close();
    }
    
    if (this.buildContext) {
      await this.buildContext.dispose();
    }
    
    if (this.hmrManager) {
      await this.hmrManager.close();
    }
    
    this.logger.info('🛑 KilatBuilder stopped');
  }

  // 🔧 Prepare esbuild options
  private async prepareBuildOptions(): Promise<esbuild.BuildOptions> {
    const { config } = this;
    
    const options: esbuild.BuildOptions = {
      entryPoints: typeof config.entry === 'string' ? [config.entry] : config.entry,
      outdir: config.outDir,
      bundle: config.bundle,
      minify: config.minify,
      sourcemap: config.sourcemap,
      splitting: config.splitting,
      treeShaking: config.treeshaking,
      platform: config.platform,
      target: this.getESBuildTarget(),
      format: config.platform === 'node' ? 'cjs' : 'esm',
      external: config.external,
      define: {
        'process.env.NODE_ENV': `"${config.mode}"`,
        ...config.transform?.define
      },
      inject: config.transform?.inject,
      jsx: this.getJSXMode(),
      tsconfig: './tsconfig.json',
      metafile: true,
      write: true,
      allowOverwrite: true,
      logLevel: config.debug?.verbose ? 'verbose' : 'warning',
      plugins: await this.preparePlugins()
    };

    // Add loader configuration
    options.loader = {
      '.png': 'file',
      '.jpg': 'file',
      '.jpeg': 'file',
      '.gif': 'file',
      '.svg': 'file',
      '.webp': 'file',
      '.ico': 'file',
      '.woff': 'file',
      '.woff2': 'file',
      '.eot': 'file',
      '.ttf': 'file',
      '.otf': 'file'
    };

    return options;
  }

  // 🎯 Get esbuild target
  private getESBuildTarget(): string[] {
    switch (this.config.target) {
      case 'web':
        return ['es2020', 'chrome80', 'firefox78', 'safari14', 'edge80'];
      case 'node':
        return ['node18'];
      case 'electron':
        return ['chrome100', 'node18'];
      default:
        return ['es2020'];
    }
  }

  // ⚛️ Get JSX mode
  private getJSXMode(): esbuild.BuildOptions['jsx'] {
    switch (this.config.transform?.jsx) {
      case 'react':
        return 'automatic';
      case 'preact':
        return 'transform';
      case 'solid':
        return 'preserve';
      case 'vue':
        return 'preserve';
      default:
        return 'automatic';
    }
  }

  // 🔌 Prepare plugins
  private async preparePlugins(): Promise<esbuild.Plugin[]> {
    const plugins: esbuild.Plugin[] = [];
    
    // Add user plugins
    if (this.config.plugins) {
      for (const plugin of this.config.plugins) {
        const esbuildPlugin: esbuild.Plugin = {
          name: plugin.name,
          setup: plugin.setup
        };
        plugins.push(esbuildPlugin);
      }
    }
    
    // Add built-in plugins
    plugins.push(this.createResolvePlugin());
    plugins.push(this.createCSSPlugin());
    plugins.push(this.createAssetsPlugin());
    
    if (this.config.debug?.enabled) {
      plugins.push(this.createDebugPlugin());
    }
    
    return plugins;
  }

  // 🔍 Create resolve plugin
  private createResolvePlugin(): esbuild.Plugin {
    return {
      name: 'kilat-resolve',
      setup: (build) => {
        // Handle kilat: imports
        build.onResolve({ filter: /^kilat:/ }, (args) => {
          const module = args.path.replace('kilat:', '');
          return {
            path: require.resolve(`kilat-${module}`),
            external: false
          };
        });
      }
    };
  }

  // 🎨 Create CSS plugin
  private createCSSPlugin(): esbuild.Plugin {
    return {
      name: 'kilat-css',
      setup: (build) => {
        build.onLoad({ filter: /\.css$/ }, async (args) => {
          const fs = await import('fs/promises');
          const contents = await fs.readFile(args.path, 'utf8');
          
          // Process CSS based on config
          let processedCSS = contents;
          
          if (this.config.css?.minify && this.config.mode === 'production') {
            // Simple CSS minification
            processedCSS = contents
              .replace(/\/\*[\s\S]*?\*\//g, '')
              .replace(/\s+/g, ' ')
              .trim();
          }
          
          return {
            contents: processedCSS,
            loader: 'css'
          };
        });
      }
    };
  }

  // 🖼️ Create assets plugin
  private createAssetsPlugin(): esbuild.Plugin {
    return {
      name: 'kilat-assets',
      setup: (build) => {
        const { assets } = this.config;
        
        build.onLoad({ filter: /\.(png|jpg|jpeg|gif|svg|webp|ico)$/ }, async (args) => {
          const fs = await import('fs/promises');
          const path = await import('path');
          
          const contents = await fs.readFile(args.path);
          
          if (contents.length < (assets?.limit || 4096)) {
            // Inline small assets
            const ext = path.extname(args.path).slice(1);
            const mimeType = this.getMimeType(ext);
            const base64 = contents.toString('base64');
            
            return {
              contents: `export default "data:${mimeType};base64,${base64}"`,
              loader: 'js'
            };
          }
          
          // Let esbuild handle large assets
          return undefined;
        });
      }
    };
  }

  // 🐛 Create debug plugin
  private createDebugPlugin(): esbuild.Plugin {
    return {
      name: 'kilat-debug',
      setup: (build) => {
        build.onStart(() => {
          this.logger.debug('Build started');
        });
        
        build.onEnd((result) => {
          this.logger.debug('Build ended', {
            errors: result.errors.length,
            warnings: result.warnings.length
          });
        });
      }
    };
  }

  // 🎭 Get MIME type
  private getMimeType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'webp': 'image/webp',
      'ico': 'image/x-icon'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }

  // 📊 Process build result
  private async processBuildResult(result: esbuild.BuildResult, startTime: number): Promise<BuildResult> {
    const duration = Date.now() - startTime;
    
    const buildResult: BuildResult = {
      success: result.errors.length === 0,
      duration,
      outputFiles: [],
      warnings: result.warnings.map(w => ({
        id: 'esbuild',
        text: w.text,
        location: w.location ? {
          file: w.location.file,
          line: w.location.line,
          column: w.location.column,
          length: w.location.length,
          lineText: w.location.lineText
        } : undefined
      })),
      errors: result.errors.map(e => ({
        id: 'esbuild',
        text: e.text,
        location: e.location ? {
          file: e.location.file,
          line: e.location.line,
          column: e.location.column,
          length: e.location.length,
          lineText: e.location.lineText
        } : undefined
      })),
      metafile: result.metafile
    };
    
    // Process output files if available
    if (result.outputFiles) {
      buildResult.outputFiles = result.outputFiles.map(file => ({
        path: file.path,
        contents: file.contents,
        hash: this.generateHash(file.contents),
        size: file.contents.length,
        type: this.getFileType(file.path)
      }));
    }
    
    return buildResult;
  }

  // 📈 Run analysis
  private async runAnalysis(buildResult: BuildResult): Promise<void> {
    if (buildResult.metafile) {
      const analysis = await bundleAnalyzer.analyze(buildResult.metafile);
      this.logger.info('📊 Bundle Analysis:', analysis);
    }
  }

  // ⚡ Optimize output
  private async optimizeOutput(buildResult: BuildResult): Promise<void> {
    if (this.config.mode === 'production') {
      await optimizeBundle(buildResult, this.config);
    }
  }

  // 🔐 Generate hash
  private generateHash(contents: Uint8Array): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(contents).digest('hex').slice(0, 8);
  }

  // 📄 Get file type
  private getFileType(path: string): 'js' | 'css' | 'html' | 'asset' {
    if (path.endsWith('.js') || path.endsWith('.mjs')) return 'js';
    if (path.endsWith('.css')) return 'css';
    if (path.endsWith('.html')) return 'html';
    return 'asset';
  }
}
