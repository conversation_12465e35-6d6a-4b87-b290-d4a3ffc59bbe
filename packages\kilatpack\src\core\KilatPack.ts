import { build, createServer, defineConfig, Plugin, UserConfig } from 'vite';
import { resolve, join } from 'path';
import { existsSync, readFileSync } from 'fs';
import { createLogger } from 'kilat-utils';
import type { 
  KilatPackConfig, 
  BuildOptions, 
  DevServerOptions,
  BundleAnalysis,
  OptimizationOptions,
  PluginConfig
} from '../types';
import { KilatReactPlugin } from '../plugins/KilatReactPlugin';
import { KilatCSSPlugin } from '../plugins/KilatCSSPlugin';
import { KilatRouterPlugin } from '../plugins/KilatRouterPlugin';
import { KilatOptimizationPlugin } from '../plugins/KilatOptimizationPlugin';
import { KilatAnalyzerPlugin } from '../plugins/KilatAnalyzerPlugin';

/**
 * 📦 KilatPack - Advanced build tool based on Vite
 * Optimized for Kilat.js applications with smart bundling
 */
export class KilatPack {
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatPack' });
  private viteConfig?: UserConfig;
  private plugins: Plugin[] = [];

  constructor(config: Partial<KilatPackConfig> = {}) {
    this.config = {
      // Default configuration
      root: process.cwd(),
      mode: 'development',
      outDir: 'dist',
      publicDir: 'public',
      assetsDir: 'assets',
      sourcemap: true,
      minify: true,
      target: 'es2020',
      format: 'es',
      splitting: true,
      treeshaking: true,
      cssCodeSplit: true,
      cssMinify: true,
      imageOptimization: true,
      bundleAnalysis: false,
      gzip: true,
      brotli: false,
      legacy: false,
      pwa: false,
      ssr: false,
      prerender: false,
      ...config
    };

    this.initializePlugins();
    this.generateViteConfig();
  }

  // 🔧 Initialize plugins
  private initializePlugins(): void {
    // Core Kilat plugins
    this.plugins.push(new KilatReactPlugin(this.config));
    this.plugins.push(new KilatCSSPlugin(this.config));
    this.plugins.push(new KilatRouterPlugin(this.config));

    // Optimization plugins
    if (this.config.mode === 'production') {
      this.plugins.push(new KilatOptimizationPlugin(this.config));
    }

    // Bundle analyzer
    if (this.config.bundleAnalysis) {
      this.plugins.push(new KilatAnalyzerPlugin(this.config));
    }

    // Custom plugins
    if (this.config.plugins) {
      this.plugins.push(...this.config.plugins);
    }
  }

  // ⚙️ Generate Vite configuration
  private generateViteConfig(): void {
    this.viteConfig = defineConfig({
      root: this.config.root,
      mode: this.config.mode,
      publicDir: this.config.publicDir,
      
      // Build configuration
      build: {
        outDir: this.config.outDir,
        assetsDir: this.config.assetsDir,
        sourcemap: this.config.sourcemap,
        minify: this.config.minify ? 'esbuild' : false,
        target: this.config.target,
        cssCodeSplit: this.config.cssCodeSplit,
        cssMinify: this.config.cssMinify,
        rollupOptions: {
          output: {
            format: this.config.format,
            manualChunks: this.config.splitting ? this.generateChunkStrategy() : undefined,
            chunkFileNames: 'assets/[name]-[hash].js',
            entryFileNames: 'assets/[name]-[hash].js',
            assetFileNames: 'assets/[name]-[hash].[ext]'
          },
          treeshake: this.config.treeshaking
        }
      },

      // Development server
      server: {
        port: this.config.devServer?.port || 3000,
        host: this.config.devServer?.host || 'localhost',
        open: this.config.devServer?.open || false,
        cors: this.config.devServer?.cors || true,
        hmr: this.config.devServer?.hmr !== false,
        https: this.config.devServer?.https || false
      },

      // Preview server
      preview: {
        port: this.config.devServer?.port || 4173,
        host: this.config.devServer?.host || 'localhost',
        open: this.config.devServer?.open || false
      },

      // Plugins
      plugins: this.plugins,

      // Resolve configuration
      resolve: {
        alias: {
          '@': resolve(this.config.root, 'src'),
          '@/components': resolve(this.config.root, 'src/components'),
          '@/pages': resolve(this.config.root, 'src/pages'),
          '@/utils': resolve(this.config.root, 'src/utils'),
          '@/types': resolve(this.config.root, 'src/types'),
          '@/assets': resolve(this.config.root, 'src/assets'),
          '@/styles': resolve(this.config.root, 'src/styles'),
          ...this.config.alias
        }
      },

      // CSS configuration
      css: {
        modules: {
          localsConvention: 'camelCase'
        },
        preprocessorOptions: {
          scss: {
            additionalData: '@import "@/styles/variables.scss";'
          }
        }
      },

      // Optimization
      optimizeDeps: {
        include: [
          'react',
          'react-dom',
          'kilat-core',
          'kilat-ui',
          'kilat-router',
          'kilat-utils'
        ],
        exclude: ['kilat-cli']
      },

      // Environment variables
      define: {
        __KILAT_VERSION__: JSON.stringify(this.getKilatVersion()),
        __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
        __DEV__: this.config.mode === 'development',
        __PROD__: this.config.mode === 'production'
      }
    });
  }

  // 🏗️ Build for production
  async build(options: BuildOptions = {}): Promise<BundleAnalysis> {
    const startTime = Date.now();
    
    this.logger.info('🏗️ Building for production...');

    try {
      // Merge build options
      const buildConfig = {
        ...this.viteConfig,
        mode: 'production',
        build: {
          ...this.viteConfig?.build,
          ...options,
          outDir: options.outDir || this.config.outDir,
          sourcemap: options.sourcemap ?? this.config.sourcemap,
          minify: options.minify ?? this.config.minify
        }
      };

      // Run build
      const result = await build(buildConfig);
      
      const buildTime = Date.now() - startTime;
      this.logger.success(`✅ Build completed in ${buildTime}ms`);

      // Generate bundle analysis
      const analysis = await this.analyzeBuild(result);
      
      if (this.config.bundleAnalysis) {
        this.printBundleAnalysis(analysis);
      }

      return analysis;

    } catch (error) {
      this.logger.error('❌ Build failed:', error);
      throw error;
    }
  }

  // 🛠️ Start development server
  async dev(options: DevServerOptions = {}): Promise<void> {
    this.logger.info('🛠️ Starting development server...');

    try {
      // Merge dev server options
      const devConfig = {
        ...this.viteConfig,
        mode: 'development',
        server: {
          ...this.viteConfig?.server,
          ...options,
          port: options.port || this.config.devServer?.port || 3000,
          host: options.host || this.config.devServer?.host || 'localhost',
          open: options.open ?? this.config.devServer?.open ?? false
        }
      };

      // Create and start server
      const server = await createServer(devConfig);
      await server.listen();

      const { port, host } = server.config.server;
      const protocol = server.config.server.https ? 'https' : 'http';
      
      this.logger.success(`🚀 Server running at ${protocol}://${host}:${port}`);

      // Handle server shutdown
      process.on('SIGTERM', () => server.close());
      process.on('SIGINT', () => server.close());

    } catch (error) {
      this.logger.error('❌ Failed to start development server:', error);
      throw error;
    }
  }

  // 👀 Preview production build
  async preview(options: DevServerOptions = {}): Promise<void> {
    this.logger.info('👀 Starting preview server...');

    try {
      const { preview } = await import('vite');
      
      const previewConfig = {
        ...this.viteConfig,
        preview: {
          ...this.viteConfig?.preview,
          ...options,
          port: options.port || 4173,
          host: options.host || 'localhost',
          open: options.open ?? false
        }
      };

      const server = await preview(previewConfig);
      
      const { port, host } = server.config.preview;
      const protocol = 'http'; // Preview is always HTTP
      
      this.logger.success(`👀 Preview server running at ${protocol}://${host}:${port}`);

    } catch (error) {
      this.logger.error('❌ Failed to start preview server:', error);
      throw error;
    }
  }

  // 📊 Analyze bundle
  private async analyzeBuild(buildResult: any): Promise<BundleAnalysis> {
    const outputDir = this.config.outDir;
    const analysis: BundleAnalysis = {
      totalSize: 0,
      gzippedSize: 0,
      chunks: [],
      assets: [],
      dependencies: {},
      treeshakingEffectiveness: 0,
      duplicateModules: [],
      largestModules: [],
      recommendations: []
    };

    try {
      // Analyze output files
      const { readdirSync, statSync } = await import('fs');
      const { gzipSync } = await import('zlib');
      
      const analyzeDirectory = (dir: string, basePath: string = '') => {
        const files = readdirSync(dir);
        
        files.forEach(file => {
          const filePath = join(dir, file);
          const relativePath = join(basePath, file);
          const stats = statSync(filePath);
          
          if (stats.isDirectory()) {
            analyzeDirectory(filePath, relativePath);
          } else {
            const content = readFileSync(filePath);
            const size = stats.size;
            const gzippedSize = gzipSync(content).length;
            
            analysis.totalSize += size;
            analysis.gzippedSize += gzippedSize;
            
            if (file.endsWith('.js')) {
              analysis.chunks.push({
                name: file,
                size,
                gzippedSize,
                modules: [] // Would need more detailed analysis
              });
            } else {
              analysis.assets.push({
                name: file,
                size,
                gzippedSize,
                type: this.getAssetType(file)
              });
            }
          }
        });
      };

      if (existsSync(outputDir)) {
        analyzeDirectory(outputDir);
      }

      // Generate recommendations
      analysis.recommendations = this.generateOptimizationRecommendations(analysis);

    } catch (error) {
      this.logger.warn('Failed to analyze bundle:', error);
    }

    return analysis;
  }

  // 🎯 Generate chunk splitting strategy
  private generateChunkStrategy() {
    return {
      // Vendor chunks
      vendor: ['react', 'react-dom'],
      kilat: ['kilat-core', 'kilat-ui', 'kilat-router', 'kilat-utils'],
      
      // UI components
      components: (id: string) => {
        if (id.includes('src/components')) {
          return 'components';
        }
      },
      
      // Utilities
      utils: (id: string) => {
        if (id.includes('src/utils') || id.includes('src/helpers')) {
          return 'utils';
        }
      },
      
      // Pages (for code splitting)
      pages: (id: string) => {
        if (id.includes('src/pages')) {
          const match = id.match(/src\/pages\/([^\/]+)/);
          return match ? `page-${match[1]}` : 'pages';
        }
      }
    };
  }

  // 📊 Print bundle analysis
  private printBundleAnalysis(analysis: BundleAnalysis): void {
    console.log('\n📊 Bundle Analysis:');
    console.log(`   Total Size: ${this.formatBytes(analysis.totalSize)}`);
    console.log(`   Gzipped Size: ${this.formatBytes(analysis.gzippedSize)}`);
    console.log(`   Chunks: ${analysis.chunks.length}`);
    console.log(`   Assets: ${analysis.assets.length}`);
    
    if (analysis.recommendations.length > 0) {
      console.log('\n💡 Optimization Recommendations:');
      analysis.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }
  }

  // 💡 Generate optimization recommendations
  private generateOptimizationRecommendations(analysis: BundleAnalysis): string[] {
    const recommendations: string[] = [];
    
    // Large bundle size
    if (analysis.totalSize > 1024 * 1024) { // 1MB
      recommendations.push('Consider code splitting to reduce bundle size');
    }
    
    // Poor compression ratio
    const compressionRatio = analysis.gzippedSize / analysis.totalSize;
    if (compressionRatio > 0.7) {
      recommendations.push('Enable better compression or minification');
    }
    
    // Too many chunks
    if (analysis.chunks.length > 20) {
      recommendations.push('Consider reducing the number of chunks');
    }
    
    // Large individual chunks
    const largeChunks = analysis.chunks.filter(chunk => chunk.size > 512 * 1024);
    if (largeChunks.length > 0) {
      recommendations.push(`Split large chunks: ${largeChunks.map(c => c.name).join(', ')}`);
    }
    
    return recommendations;
  }

  // 🔧 Utility methods
  private getKilatVersion(): string {
    try {
      const packagePath = resolve(this.config.root, 'package.json');
      if (existsSync(packagePath)) {
        const pkg = JSON.parse(readFileSync(packagePath, 'utf-8'));
        return pkg.dependencies?.['kilat-core'] || '1.0.0';
      }
    } catch {
      // Ignore errors
    }
    return '1.0.0';
  }

  private getAssetType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    
    switch (ext) {
      case 'css':
      case 'scss':
      case 'sass':
      case 'less':
        return 'stylesheet';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
      case 'webp':
        return 'image';
      case 'woff':
      case 'woff2':
      case 'ttf':
      case 'eot':
        return 'font';
      case 'mp4':
      case 'webm':
      case 'ogg':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
        return 'audio';
      default:
        return 'other';
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 🔧 Configuration methods
  getConfig(): KilatPackConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<KilatPackConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.generateViteConfig();
    this.logger.info('Configuration updated');
  }

  addPlugin(plugin: Plugin): void {
    this.plugins.push(plugin);
    this.generateViteConfig();
  }

  removePlugin(pluginName: string): void {
    this.plugins = this.plugins.filter(p => p.name !== pluginName);
    this.generateViteConfig();
  }

  // 📊 Get build statistics
  async getStats(): Promise<{
    configSize: number;
    pluginCount: number;
    aliasCount: number;
    optimizedDeps: number;
  }> {
    return {
      configSize: JSON.stringify(this.viteConfig).length,
      pluginCount: this.plugins.length,
      aliasCount: Object.keys(this.viteConfig?.resolve?.alias || {}).length,
      optimizedDeps: this.viteConfig?.optimizeDeps?.include?.length || 0
    };
  }
}

// 🏭 Factory function
export function createKilatPack(config: Partial<KilatPackConfig> = {}): KilatPack {
  return new KilatPack(config);
}

// 🎯 Default export
export default {
  KilatPack,
  createKilatPack
};
