import { createServer, IncomingMessage, ServerResponse } from 'http';
import { createServer as createHttpsServer } from 'https';
import { join, extname, resolve } from 'path';
import { existsSync, readFileSync, statSync } from 'fs';
import { WebSocketServer } from 'ws';
import * as esbuild from 'esbuild';
import sirv from 'sirv';
import connect from 'connect';
import { createLogger } from 'kilat-utils';
import type { KilatPackConfig, DevServer as IDevServer, HMRMessage } from '../types';
import type { HMRManager } from './HMRManager';

/**
 * 🔥 DevServer - Development server with HMR
 * Serves files and handles hot module replacement
 */
export class DevServer implements IDevServer {
  public port: number;
  public host: string;
  public url: string;

  private config: KilatPackConfig;
  private buildContext?: esbuild.BuildContext;
  private hmrManager?: HMRManager;
  private server?: any;
  private wsServer?: WebSocketServer;
  private app: connect.Server;
  private logger = createLogger({ level: 'info' });
  private isPreview: boolean;

  constructor(
    config: KilatPackConfig,
    buildContext?: esbuild.BuildContext,
    hmrManager?: HMRManager,
    isPreview = false
  ) {
    this.config = config;
    this.buildContext = buildContext;
    this.hmrManager = hmrManager;
    this.isPreview = isPreview;
    this.port = config.server?.port || 3000;
    this.host = config.server?.host || 'localhost';
    this.url = `${config.server?.https ? 'https' : 'http'}://${this.host}:${this.port}`;
    this.app = connect();
  }

  // 🚀 Start the development server
  async start(): Promise<void> {
    await this.setupMiddleware();
    await this.startServer();
    
    if (this.config.hmr?.enabled && !this.isPreview) {
      await this.setupWebSocket();
    }
    
    if (this.config.server?.open) {
      await this.openBrowser();
    }
  }

  // 🛑 Stop the server
  async close(): Promise<void> {
    if (this.wsServer) {
      this.wsServer.close();
    }
    
    if (this.server) {
      await new Promise<void>((resolve) => {
        this.server.close(() => resolve());
      });
    }
    
    this.logger.info('🛑 Dev server stopped');
  }

  // 🔄 Reload the page
  reload(): void {
    this.send({
      type: 'full-reload'
    });
  }

  // 📤 Send HMR message
  send(message: HMRMessage): void {
    if (this.wsServer) {
      this.wsServer.clients.forEach(client => {
        if (client.readyState === 1) { // WebSocket.OPEN
          client.send(JSON.stringify(message));
        }
      });
    }
  }

  // 🔧 Setup middleware
  private async setupMiddleware(): Promise<void> {
    // CORS middleware
    if (this.config.server?.cors) {
      this.app.use(this.corsMiddleware);
    }

    // Proxy middleware
    if (this.config.server?.proxy) {
      this.setupProxyMiddleware();
    }

    // Static file serving
    this.setupStaticMiddleware();

    // API routes (if any)
    this.setupAPIMiddleware();

    // SPA fallback
    this.app.use(this.spaFallbackMiddleware);
  }

  // 🌐 CORS middleware
  private corsMiddleware = (req: IncomingMessage, res: ServerResponse, next: Function) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.statusCode = 200;
      res.end();
      return;
    }
    
    next();
  };

  // 🔄 Setup proxy middleware
  private setupProxyMiddleware(): void {
    const { proxy } = this.config.server!;
    
    if (proxy) {
      Object.entries(proxy).forEach(([path, target]) => {
        this.app.use(path, (req: IncomingMessage, res: ServerResponse, next: Function) => {
          // Simple proxy implementation
          const targetUrl = typeof target === 'string' ? target : target.target;
          const proxyUrl = new URL(req.url!, targetUrl);
          
          // Forward request
          const http = require(proxyUrl.protocol === 'https:' ? 'https' : 'http');
          const proxyReq = http.request(proxyUrl, {
            method: req.method,
            headers: req.headers
          }, (proxyRes: any) => {
            res.statusCode = proxyRes.statusCode;
            Object.entries(proxyRes.headers).forEach(([key, value]) => {
              res.setHeader(key, value as string);
            });
            proxyRes.pipe(res);
          });
          
          proxyReq.on('error', (err: Error) => {
            this.logger.error('Proxy error:', err);
            res.statusCode = 500;
            res.end('Proxy Error');
          });
          
          req.pipe(proxyReq);
        });
      });
    }
  }

  // 📁 Setup static file middleware
  private setupStaticMiddleware(): void {
    // Serve built files
    if (this.isPreview) {
      const staticHandler = sirv(this.config.outDir, {
        dev: false,
        etag: true,
        maxAge: 31536000, // 1 year
        immutable: true
      });
      this.app.use(staticHandler);
    } else {
      // In dev mode, serve from memory or temp directory
      this.app.use(this.devStaticMiddleware);
    }

    // Serve public directory
    if (this.config.publicDir && existsSync(this.config.publicDir)) {
      const publicHandler = sirv(this.config.publicDir, {
        dev: true,
        etag: true
      });
      this.app.use(publicHandler);
    }
  }

  // 🔧 Development static middleware
  private devStaticMiddleware = async (req: IncomingMessage, res: ServerResponse, next: Function) => {
    const url = req.url!;
    const filePath = join(this.config.outDir, url);
    
    // Check if file exists in output directory
    if (existsSync(filePath) && statSync(filePath).isFile()) {
      const ext = extname(filePath);
      const mimeType = this.getMimeType(ext);
      
      res.setHeader('Content-Type', mimeType);
      
      // Add HMR client script injection for HTML files
      if (ext === '.html' && this.config.hmr?.enabled) {
        let content = readFileSync(filePath, 'utf-8');
        content = this.injectHMRClient(content);
        res.end(content);
      } else {
        const content = readFileSync(filePath);
        res.end(content);
      }
    } else {
      next();
    }
  };

  // 🔌 Setup API middleware
  private setupAPIMiddleware(): void {
    this.app.use('/api', (req: IncomingMessage, res: ServerResponse, next: Function) => {
      // Handle API routes if kilat-backend is available
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 404;
      res.end(JSON.stringify({ error: 'API endpoint not found' }));
    });
  }

  // 📄 SPA fallback middleware
  private spaFallbackMiddleware = (req: IncomingMessage, res: ServerResponse) => {
    const url = req.url!;
    
    // Don't fallback for API routes or assets
    if (url.startsWith('/api') || url.includes('.')) {
      res.statusCode = 404;
      res.end('Not Found');
      return;
    }
    
    // Serve index.html for SPA routes
    const indexPath = join(this.config.outDir, 'index.html');
    
    if (existsSync(indexPath)) {
      let content = readFileSync(indexPath, 'utf-8');
      
      if (this.config.hmr?.enabled && !this.isPreview) {
        content = this.injectHMRClient(content);
      }
      
      res.setHeader('Content-Type', 'text/html');
      res.end(content);
    } else {
      res.statusCode = 404;
      res.end('Index.html not found');
    }
  };

  // 🚀 Start HTTP server
  private async startServer(): Promise<void> {
    const createServerFn = this.config.server?.https ? createHttpsServer : createServer;
    
    this.server = createServerFn(this.app);
    
    await new Promise<void>((resolve, reject) => {
      this.server.listen(this.port, this.host, () => {
        this.logger.success(`🔥 Dev server running at ${this.url}`);
        resolve();
      });
      
      this.server.on('error', (err: Error) => {
        if ((err as any).code === 'EADDRINUSE') {
          this.port++;
          this.url = `${this.config.server?.https ? 'https' : 'http'}://${this.host}:${this.port}`;
          this.server.listen(this.port, this.host, () => {
            this.logger.success(`🔥 Dev server running at ${this.url}`);
            resolve();
          });
        } else {
          reject(err);
        }
      });
    });
  }

  // 🔌 Setup WebSocket for HMR
  private async setupWebSocket(): Promise<void> {
    const wsPort = this.config.hmr?.port || this.port + 1;
    
    this.wsServer = new WebSocketServer({ port: wsPort });
    
    this.wsServer.on('connection', (ws) => {
      this.logger.debug('HMR client connected');
      
      // Send initial connection message
      ws.send(JSON.stringify({ type: 'connected' }));
      
      // Handle ping/pong
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          if (message.type === 'ping') {
            ws.send(JSON.stringify({ type: 'pong' }));
          }
        } catch (err) {
          // Ignore invalid messages
        }
      });
      
      ws.on('close', () => {
        this.logger.debug('HMR client disconnected');
      });
    });
    
    // Setup file watching for HMR
    if (this.hmrManager) {
      this.hmrManager.onUpdate((updates) => {
        this.send({
          type: 'update',
          updates
        });
      });
      
      this.hmrManager.onError((error) => {
        this.send({
          type: 'error',
          error: {
            message: error.message,
            stack: error.stack
          }
        });
      });
    }
  }

  // 💉 Inject HMR client script
  private injectHMRClient(html: string): string {
    const hmrScript = `
<script type="module">
  const ws = new WebSocket('ws://${this.host}:${this.config.hmr?.port || this.port + 1}');
  
  ws.addEventListener('message', (event) => {
    const data = JSON.parse(event.data);
    
    switch (data.type) {
      case 'update':
        handleUpdate(data.updates);
        break;
      case 'full-reload':
        location.reload();
        break;
      case 'error':
        showError(data.error);
        break;
      case 'connected':
        console.log('[HMR] Connected');
        break;
    }
  });
  
  function handleUpdate(updates) {
    updates.forEach(update => {
      if (update.type === 'css-update') {
        updateCSS(update.path);
      } else if (update.type === 'js-update') {
        location.reload(); // For now, just reload
      }
    });
  }
  
  function updateCSS(path) {
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    links.forEach(link => {
      if (link.href.includes(path)) {
        const newLink = link.cloneNode();
        newLink.href = link.href + '?t=' + Date.now();
        link.parentNode.insertBefore(newLink, link.nextSibling);
        link.remove();
      }
    });
  }
  
  function showError(error) {
    if (${this.config.hmr?.overlay}) {
      showErrorOverlay(error);
    } else {
      console.error('[HMR] Error:', error);
    }
  }
  
  function showErrorOverlay(error) {
    // Create error overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = \`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      font-family: monospace;
      font-size: 14px;
      padding: 20px;
      box-sizing: border-box;
      z-index: 9999;
      overflow: auto;
    \`;
    
    overlay.innerHTML = \`
      <div style="max-width: 800px; margin: 0 auto;">
        <h2 style="color: #ff6b6b; margin-top: 0;">Build Error</h2>
        <pre style="background: #2d2d2d; padding: 20px; border-radius: 4px; overflow: auto;">
\${error.message}

\${error.stack || ''}
        </pre>
        <button onclick="this.parentElement.parentElement.remove()" 
                style="background: #ff6b6b; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 20px;">
          Close
        </button>
      </div>
    \`;
    
    document.body.appendChild(overlay);
  }
  
  // Ping server to keep connection alive
  setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: 'ping' }));
    }
  }, 30000);
</script>`;

    return html.replace('</head>', `${hmrScript}\n</head>`);
  }

  // 🌐 Open browser
  private async openBrowser(): Promise<void> {
    const open = await import('open');
    try {
      await open.default(this.url);
    } catch (err) {
      this.logger.warn('Failed to open browser automatically');
    }
  }

  // 📄 Get MIME type
  private getMimeType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      '.html': 'text/html',
      '.js': 'application/javascript',
      '.mjs': 'application/javascript',
      '.css': 'text/css',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }
}
