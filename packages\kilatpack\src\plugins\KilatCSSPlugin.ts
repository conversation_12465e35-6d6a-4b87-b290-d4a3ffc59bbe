import { Plugin } from 'vite';
import { createLogger } from 'kilat-utils';
import type { KilatPackConfig } from '../types';

/**
 * 🎨 Kilat CSS Plugin
 * Advanced CSS processing with KilatCSS integration
 */
export class KilatCSSPlugin {
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatCSSPlugin' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  // 🔧 Create Vite plugin
  create(): Plugin {
    return {
      name: 'kilat-css',
      
      configResolved(config) {
        // Configure CSS processing
        config.css = config.css || {};
        config.css.postcss = config.css.postcss || {};
        
        // Add KilatCSS PostCSS plugins
        config.css.postcss.plugins = [
          ...(config.css.postcss.plugins || []),
          this.createKilatCSSProcessor(),
          this.createUtilityGenerator(),
          this.createThemeProcessor()
        ];
        
        // Configure CSS modules for Kilat components
        config.css.modules = {
          ...config.css.modules,
          generateScopedName: '[name]__[local]___[hash:base64:5]',
          localsConvention: 'camelCaseOnly'
        };
      },
      
      load(id) {
        // Handle KilatCSS imports
        if (id.includes('kilatcss') || id.endsWith('.kilat.css')) {
          return this.loadKilatCSS(id);
        }
      },
      
      transform(code, id) {
        // Transform CSS code
        if (id.endsWith('.css') || id.endsWith('.scss') || id.endsWith('.less')) {
          return this.transformCSS(code, id);
        }
        
        // Transform CSS-in-JS
        if (id.endsWith('.tsx') || id.endsWith('.jsx')) {
          return this.transformCSSInJS(code, id);
        }
      },
      
      generateBundle(options, bundle) {
        // Optimize CSS bundle
        this.optimizeCSSBundle(bundle);
      }
    };
  }

  // 🎨 KilatCSS processor
  private createKilatCSSProcessor() {
    return {
      postcssPlugin: 'kilat-css-processor',
      
      Once(root: any) {
        // Process KilatCSS utilities
        root.walkRules((rule: any) => {
          if (rule.selector.includes('k-')) {
            this.processKilatUtility(rule);
          }
        });
        
        // Add CSS custom properties for themes
        this.addThemeVariables(root);
        
        // Process animations
        this.processAnimations(root);
      }
    };
  }

  // 🔧 Utility generator
  private createUtilityGenerator() {
    return {
      postcssPlugin: 'kilat-utility-generator',
      
      Once(root: any) {
        // Generate responsive utilities
        this.generateResponsiveUtilities(root);
        
        // Generate state utilities (hover, focus, etc.)
        this.generateStateUtilities(root);
        
        // Generate theme-specific utilities
        this.generateThemeUtilities(root);
      }
    };
  }

  // 🎭 Theme processor
  private createThemeProcessor() {
    return {
      postcssPlugin: 'kilat-theme-processor',
      
      Once(root: any) {
        // Process theme switching
        this.processThemeSwitching(root);
        
        // Add dark mode support
        this.addDarkModeSupport(root);
        
        // Process cyberpunk effects
        this.processCyberpunkEffects(root);
      }
    };
  }

  // 📥 Load KilatCSS
  private async loadKilatCSS(id: string): Promise<string> {
    // Load base KilatCSS
    let css = `
      /* KilatCSS Base */
      @import 'kilatcss/base.css';
      @import 'kilatcss/utilities.css';
      @import 'kilatcss/components.css';
      @import 'kilatcss/themes.css';
      @import 'kilatcss/animations.css';
    `;

    // Add theme-specific CSS
    if (this.config.theme) {
      css += `@import 'kilatcss/themes/${this.config.theme}.css';`;
    }

    // Add custom CSS
    if (this.config.customCSS) {
      css += this.config.customCSS;
    }

    return css;
  }

  // 🔄 Transform CSS
  private transformCSS(code: string, id: string): string {
    let transformedCode = code;

    // Transform KilatCSS utilities
    transformedCode = this.transformKilatUtilities(transformedCode);
    
    // Transform animations
    transformedCode = this.transformAnimations(transformedCode);
    
    // Transform theme variables
    transformedCode = this.transformThemeVariables(transformedCode);
    
    // Add source map
    if (this.config.sourcemap) {
      transformedCode += `\n/*# sourceMappingURL=${id}.map */`;
    }

    return transformedCode;
  }

  // 🔄 Transform CSS-in-JS
  private transformCSSInJS(code: string, id: string): string {
    // Transform styled-components with KilatCSS
    let transformedCode = code.replace(
      /styled\.\w+`([^`]*)`/g,
      (match, styles) => {
        const transformedStyles = this.transformKilatUtilities(styles);
        return match.replace(styles, transformedStyles);
      }
    );

    // Transform emotion css`` templates
    transformedCode = transformedCode.replace(
      /css`([^`]*)`/g,
      (match, styles) => {
        const transformedStyles = this.transformKilatUtilities(styles);
        return match.replace(styles, transformedStyles);
      }
    );

    return transformedCode;
  }

  // 🎨 Process KilatCSS utility
  private processKilatUtility(rule: any): void {
    const selector = rule.selector;
    
    // Add responsive variants
    if (selector.includes('k-')) {
      this.addResponsiveVariants(rule);
    }
    
    // Add state variants
    this.addStateVariants(rule);
    
    // Add theme variants
    this.addThemeVariants(rule);
  }

  // 📱 Add responsive variants
  private addResponsiveVariants(rule: any): void {
    const breakpoints = {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px'
    };

    for (const [prefix, size] of Object.entries(breakpoints)) {
      const responsiveSelector = rule.selector.replace(/^\.k-/, `.k-${prefix}\\:`);
      const mediaRule = rule.root().rule(`@media (min-width: ${size})`);
      const responsiveRule = mediaRule.rule(responsiveSelector);
      
      rule.each((decl: any) => {
        responsiveRule.append(decl.clone());
      });
    }
  }

  // 🎯 Add state variants
  private addStateVariants(rule: any): void {
    const states = ['hover', 'focus', 'active', 'disabled', 'visited'];
    
    for (const state of states) {
      const stateSelector = rule.selector.replace(/^\.k-/, `.k-${state}\\:`);
      const stateRule = rule.root().rule(`${stateSelector}:${state}`);
      
      rule.each((decl: any) => {
        stateRule.append(decl.clone());
      });
    }
  }

  // 🎭 Add theme variants
  private addThemeVariants(rule: any): void {
    const themes = ['cyberpunk', 'nusantara', 'minimalist', 'retro'];
    
    for (const theme of themes) {
      const themeSelector = `[data-kilat-theme="${theme}"] ${rule.selector}`;
      const themeRule = rule.root().rule(themeSelector);
      
      rule.each((decl: any) => {
        themeRule.append(decl.clone());
      });
    }
  }

  // 🌈 Add theme variables
  private addThemeVariables(root: any): void {
    const themeVars = `
      :root {
        --k-primary: #00ffff;
        --k-secondary: #ff00ff;
        --k-accent: #ffff00;
        --k-background: #000011;
        --k-surface: #001122;
        --k-text: #ffffff;
        --k-text-muted: #888888;
        --k-border: #333333;
        
        /* Neon colors */
        --k-neon-blue: #00ffff;
        --k-neon-pink: #ff00ff;
        --k-neon-green: #00ff00;
        --k-neon-yellow: #ffff00;
        --k-neon-purple: #8000ff;
        --k-neon-orange: #ff8000;
        --k-neon-red: #ff0040;
        
        /* Transitions */
        --k-transition-fast: 150ms ease-out;
        --k-transition-normal: 300ms ease-out;
        --k-transition-slow: 500ms ease-out;
        
        /* Shadows */
        --k-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
        --k-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
        --k-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
        --k-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
        
        /* Border radius */
        --k-radius-sm: 4px;
        --k-radius-md: 8px;
        --k-radius-lg: 12px;
        --k-radius-xl: 16px;
        --k-radius-full: 9999px;
      }
    `;
    
    root.prepend(themeVars);
  }

  // 🎬 Process animations
  private processAnimations(root: any): void {
    // Add Kilat.js keyframes
    const keyframes = `
      @keyframes k-glow {
        0%, 100% { box-shadow: 0 0 5px currentColor; }
        50% { box-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
      }
      
      @keyframes k-pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      
      @keyframes k-bounce {
        0%, 100% { transform: translateY(-25%); }
        50% { transform: translateY(0); }
      }
      
      @keyframes k-spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
      
      @keyframes k-ping {
        75%, 100% { transform: scale(2); opacity: 0; }
      }
    `;
    
    root.append(keyframes);
  }

  // 🔄 Transform KilatCSS utilities
  private transformKilatUtilities(code: string): string {
    // Transform spacing utilities
    code = code.replace(/k-p-(\d+)/g, (match, value) => {
      return `padding: ${parseInt(value) * 0.25}rem`;
    });
    
    code = code.replace(/k-m-(\d+)/g, (match, value) => {
      return `margin: ${parseInt(value) * 0.25}rem`;
    });
    
    // Transform color utilities
    code = code.replace(/k-text-(\w+)/g, (match, color) => {
      return `color: var(--k-${color})`;
    });
    
    code = code.replace(/k-bg-(\w+)/g, (match, color) => {
      return `background-color: var(--k-${color})`;
    });
    
    return code;
  }

  // 🎬 Transform animations
  private transformAnimations(code: string): string {
    return code.replace(/k-animate-(\w+)/g, (match, animation) => {
      return `animation: k-${animation} 1s ease-in-out infinite`;
    });
  }

  // 🎭 Transform theme variables
  private transformThemeVariables(code: string): string {
    return code.replace(/theme\(([^)]+)\)/g, (match, variable) => {
      return `var(--k-${variable})`;
    });
  }

  // 📦 Optimize CSS bundle
  private optimizeCSSBundle(bundle: any): void {
    for (const [fileName, chunk] of Object.entries(bundle)) {
      if (fileName.endsWith('.css')) {
        this.optimizeCSSChunk(chunk as any);
      }
    }
  }

  // 🔧 Optimize CSS chunk
  private optimizeCSSChunk(chunk: any): void {
    if (this.config.mode === 'production') {
      // Remove unused CSS
      chunk.source = this.removeUnusedCSS(chunk.source);
      
      // Minify CSS
      chunk.source = this.minifyCSS(chunk.source);
      
      // Remove comments
      chunk.source = chunk.source.replace(/\/\*[^*]*\*+(?:[^/*][^*]*\*+)*\//g, '');
    }
  }

  // 🧹 Remove unused CSS
  private removeUnusedCSS(css: string): string {
    // Simple unused CSS removal (in production, use PurgeCSS)
    return css;
  }

  // 🗜️ Minify CSS
  private minifyCSS(css: string): string {
    return css
      .replace(/\s+/g, ' ')
      .replace(/;\s*}/g, '}')
      .replace(/{\s*/g, '{')
      .replace(/;\s*/g, ';')
      .trim();
  }
}
