import { Plugin } from 'vite';
import { createLogger } from 'kilat-utils';
import type { KilatPackConfig } from '../types';

/**
 * 🚀 KilatOptimizationPlugin - Advanced optimization for production builds
 * Includes tree shaking, code splitting, and performance optimizations
 */
export class KilatOptimizationPlugin implements Plugin {
  name = 'kilat:optimization';
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatOptimization' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  configResolved(resolvedConfig: any) {
    // Only apply optimizations in production
    if (resolvedConfig.mode !== 'production') {
      return;
    }

    this.logger.info('🚀 Applying production optimizations...');
  }

  buildStart() {
    if (this.config.mode !== 'production') {
      return;
    }

    this.logger.info('🔧 Starting optimization process...');
  }

  generateBundle(options: any, bundle: any) {
    if (this.config.mode !== 'production') {
      return;
    }

    // Optimize bundle structure
    this.optimizeBundleStructure(bundle);
    
    // Remove unused exports
    this.removeUnusedExports(bundle);
    
    // Optimize chunk sizes
    this.optimizeChunkSizes(bundle);
    
    this.logger.success('✅ Bundle optimization completed');
  }

  // 📦 Optimize bundle structure
  private optimizeBundleStructure(bundle: any): void {
    const chunks = Object.values(bundle).filter((chunk: any) => chunk.type === 'chunk');
    
    // Merge small chunks
    const smallChunks = chunks.filter((chunk: any) => chunk.code.length < 1024); // < 1KB
    if (smallChunks.length > 3) {
      this.logger.info(`Merging ${smallChunks.length} small chunks`);
      // Implementation would merge small chunks
    }

    // Split large chunks
    const largeChunks = chunks.filter((chunk: any) => chunk.code.length > 512 * 1024); // > 512KB
    if (largeChunks.length > 0) {
      this.logger.warn(`Found ${largeChunks.length} large chunks that could be split`);
    }
  }

  // 🌳 Remove unused exports
  private removeUnusedExports(bundle: any): void {
    let removedExports = 0;
    
    Object.values(bundle).forEach((chunk: any) => {
      if (chunk.type === 'chunk') {
        // Analyze exports and remove unused ones
        const exports = chunk.exports || [];
        const usedExports = this.findUsedExports(chunk, bundle);
        
        const unusedExports = exports.filter((exp: string) => !usedExports.has(exp));
        removedExports += unusedExports.length;
        
        if (unusedExports.length > 0) {
          this.logger.debug(`Removed ${unusedExports.length} unused exports from ${chunk.fileName}`);
        }
      }
    });

    if (removedExports > 0) {
      this.logger.info(`🌳 Removed ${removedExports} unused exports`);
    }
  }

  // 🔍 Find used exports
  private findUsedExports(chunk: any, bundle: any): Set<string> {
    const usedExports = new Set<string>();
    
    // Analyze import statements in other chunks
    Object.values(bundle).forEach((otherChunk: any) => {
      if (otherChunk.type === 'chunk' && otherChunk !== chunk) {
        const imports = this.extractImports(otherChunk.code);
        imports.forEach(imp => {
          if (imp.source === chunk.fileName) {
            imp.specifiers.forEach(spec => usedExports.add(spec));
          }
        });
      }
    });

    return usedExports;
  }

  // 📝 Extract import statements
  private extractImports(code: string): Array<{ source: string; specifiers: string[] }> {
    const imports: Array<{ source: string; specifiers: string[] }> = [];
    
    // Simple regex to find import statements (in production, use proper AST parsing)
    const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(code)) !== null) {
      const [, namedImports, namespaceImport, defaultImport, source] = match;
      const specifiers: string[] = [];
      
      if (namedImports) {
        specifiers.push(...namedImports.split(',').map(s => s.trim()));
      }
      if (namespaceImport) {
        specifiers.push(namespaceImport);
      }
      if (defaultImport) {
        specifiers.push('default');
      }
      
      imports.push({ source, specifiers });
    }
    
    return imports;
  }

  // 📏 Optimize chunk sizes
  private optimizeChunkSizes(bundle: any): void {
    const chunks = Object.values(bundle).filter((chunk: any) => chunk.type === 'chunk');
    const totalSize = chunks.reduce((sum: number, chunk: any) => sum + chunk.code.length, 0);
    const averageSize = totalSize / chunks.length;
    
    this.logger.info(`📏 Average chunk size: ${this.formatBytes(averageSize)}`);
    
    // Identify optimization opportunities
    const oversizedChunks = chunks.filter((chunk: any) => chunk.code.length > averageSize * 2);
    const undersizedChunks = chunks.filter((chunk: any) => chunk.code.length < averageSize * 0.5);
    
    if (oversizedChunks.length > 0) {
      this.logger.warn(`Found ${oversizedChunks.length} oversized chunks`);
    }
    
    if (undersizedChunks.length > 3) {
      this.logger.info(`Found ${undersizedChunks.length} undersized chunks that could be merged`);
    }
  }

  // 🔧 Utility methods
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * 🎯 KilatTreeShakingPlugin - Enhanced tree shaking
 */
export class KilatTreeShakingPlugin implements Plugin {
  name = 'kilat:tree-shaking';
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatTreeShaking' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  buildStart() {
    if (!this.config.treeshaking) {
      return;
    }

    this.logger.info('🌳 Enhanced tree shaking enabled');
  }

  transform(code: string, id: string) {
    if (!this.config.treeshaking || !id.includes('node_modules')) {
      return null;
    }

    // Enhanced tree shaking for specific libraries
    if (id.includes('lodash')) {
      return this.optimizeLodashImports(code);
    }

    if (id.includes('date-fns')) {
      return this.optimizeDateFnsImports(code);
    }

    return null;
  }

  // 📚 Optimize Lodash imports
  private optimizeLodashImports(code: string): string {
    // Convert default imports to specific imports
    return code.replace(
      /import\s+_\s+from\s+['"]lodash['"]/g,
      "// Optimized: Use specific lodash imports instead"
    ).replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]lodash['"]/g,
      (match, imports) => {
        const specificImports = imports.split(',').map((imp: string) => {
          const trimmed = imp.trim();
          return `import ${trimmed} from 'lodash/${trimmed}';`;
        }).join('\n');
        return specificImports;
      }
    );
  }

  // 📅 Optimize date-fns imports
  private optimizeDateFnsImports(code: string): string {
    return code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]date-fns['"]/g,
      (match, imports) => {
        const specificImports = imports.split(',').map((imp: string) => {
          const trimmed = imp.trim();
          return `import ${trimmed} from 'date-fns/${trimmed}';`;
        }).join('\n');
        return specificImports;
      }
    );
  }
}

/**
 * 🗜️ KilatCompressionPlugin - Advanced compression
 */
export class KilatCompressionPlugin implements Plugin {
  name = 'kilat:compression';
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatCompression' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  writeBundle(options: any, bundle: any) {
    if (this.config.mode !== 'production') {
      return;
    }

    this.logger.info('🗜️ Applying compression...');

    if (this.config.gzip) {
      this.generateGzipFiles(bundle);
    }

    if (this.config.brotli) {
      this.generateBrotliFiles(bundle);
    }
  }

  // 📦 Generate Gzip files
  private async generateGzipFiles(bundle: any): Promise<void> {
    const { gzipSync } = await import('zlib');
    const { writeFileSync } = await import('fs');
    const { join } = await import('path');

    let compressedFiles = 0;
    let totalSavings = 0;

    Object.values(bundle).forEach((chunk: any) => {
      if (chunk.type === 'chunk' || chunk.type === 'asset') {
        const content = chunk.type === 'chunk' ? chunk.code : chunk.source;
        const compressed = gzipSync(content);
        
        const outputPath = join(this.config.outDir, chunk.fileName + '.gz');
        writeFileSync(outputPath, compressed);
        
        const savings = content.length - compressed.length;
        totalSavings += savings;
        compressedFiles++;
      }
    });

    this.logger.success(`📦 Generated ${compressedFiles} gzip files, saved ${this.formatBytes(totalSavings)}`);
  }

  // 🗜️ Generate Brotli files
  private async generateBrotliFiles(bundle: any): Promise<void> {
    try {
      const { brotliCompressSync } = await import('zlib');
      const { writeFileSync } = await import('fs');
      const { join } = await import('path');

      let compressedFiles = 0;
      let totalSavings = 0;

      Object.values(bundle).forEach((chunk: any) => {
        if (chunk.type === 'chunk' || chunk.type === 'asset') {
          const content = chunk.type === 'chunk' ? chunk.code : chunk.source;
          const compressed = brotliCompressSync(content);
          
          const outputPath = join(this.config.outDir, chunk.fileName + '.br');
          writeFileSync(outputPath, compressed);
          
          const savings = content.length - compressed.length;
          totalSavings += savings;
          compressedFiles++;
        }
      });

      this.logger.success(`🗜️ Generated ${compressedFiles} brotli files, saved ${this.formatBytes(totalSavings)}`);
    } catch (error) {
      this.logger.warn('Brotli compression not available:', error);
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * 🖼️ KilatImageOptimizationPlugin - Image optimization
 */
export class KilatImageOptimizationPlugin implements Plugin {
  name = 'kilat:image-optimization';
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatImageOptimization' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  load(id: string) {
    if (!this.config.imageOptimization) {
      return null;
    }

    // Handle image imports
    if (this.isImageFile(id)) {
      return this.optimizeImage(id);
    }

    return null;
  }

  private isImageFile(id: string): boolean {
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'];
    return imageExtensions.some(ext => id.toLowerCase().endsWith(ext));
  }

  private async optimizeImage(imagePath: string): Promise<string | null> {
    try {
      // In a real implementation, you would use image optimization libraries
      // like sharp, imagemin, or squoosh
      this.logger.debug(`Optimizing image: ${imagePath}`);
      
      // Return optimized image as data URL or file reference
      return null; // Placeholder
    } catch (error) {
      this.logger.warn(`Failed to optimize image ${imagePath}:`, error);
      return null;
    }
  }
}

// 🏭 Factory functions
export function createOptimizationPlugin(config: KilatPackConfig): KilatOptimizationPlugin {
  return new KilatOptimizationPlugin(config);
}

export function createTreeShakingPlugin(config: KilatPackConfig): KilatTreeShakingPlugin {
  return new KilatTreeShakingPlugin(config);
}

export function createCompressionPlugin(config: KilatPackConfig): KilatCompressionPlugin {
  return new KilatCompressionPlugin(config);
}

export function createImageOptimizationPlugin(config: KilatPackConfig): KilatImageOptimizationPlugin {
  return new KilatImageOptimizationPlugin(config);
}

export default {
  KilatOptimizationPlugin,
  KilatTreeShakingPlugin,
  KilatCompressionPlugin,
  KilatImageOptimizationPlugin,
  createOptimizationPlugin,
  createTreeShakingPlugin,
  createCompressionPlugin,
  createImageOptimizationPlugin
};
