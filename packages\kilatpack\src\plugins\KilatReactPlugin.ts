import { Plugin } from 'vite';
import react from '@vitejs/plugin-react';
import { createLogger } from 'kilat-utils';
import type { KilatPackConfig } from '../types';

/**
 * ⚛️ Kilat React Plugin
 * Enhanced React support with Kilat.js optimizations
 */
export class KilatReactPlugin {
  private config: KilatPackConfig;
  private logger = createLogger({ prefix: 'KilatReactPlugin' });

  constructor(config: KilatPackConfig) {
    this.config = config;
  }

  // 🔧 Create Vite plugin
  create(): Plugin[] {
    const plugins: Plugin[] = [];

    // Base React plugin
    plugins.push(react({
      // Fast Refresh configuration
      fastRefresh: this.config.mode === 'development',
      
      // JSX runtime
      jsxRuntime: 'automatic',
      
      // Babel configuration for Kilat.js
      babel: {
        plugins: [
          // Add Kilat.js specific transformations
          this.createKilatTransformPlugin(),
          
          // Emotion support for styled components
          ['@emotion/babel-plugin', {
            sourceMap: this.config.sourcemap,
            autoLabel: 'dev-only',
            labelFormat: '[local]',
            cssPropOptimization: true
          }],
          
          // Import optimization
          ['babel-plugin-import', {
            libraryName: 'kilat-ui',
            libraryDirectory: 'es',
            style: true
          }]
        ],
        
        presets: [
          ['@babel/preset-env', {
            targets: this.config.target,
            modules: false,
            useBuiltIns: 'usage',
            corejs: 3
          }],
          ['@babel/preset-react', {
            runtime: 'automatic',
            development: this.config.mode === 'development'
          }],
          ['@babel/preset-typescript', {
            isTSX: true,
            allExtensions: true
          }]
        ]
      }
    }));

    // Kilat.js specific React optimizations
    plugins.push(this.createKilatReactOptimizationPlugin());

    // React DevTools integration
    if (this.config.mode === 'development') {
      plugins.push(this.createReactDevToolsPlugin());
    }

    return plugins;
  }

  // 🔄 Kilat.js transform plugin
  private createKilatTransformPlugin() {
    return {
      name: 'kilat-transform',
      visitor: {
        // Transform Kilat.js specific JSX attributes
        JSXAttribute(path: any) {
          const name = path.node.name.name;
          
          // Transform k-* attributes to className
          if (name.startsWith('k-')) {
            path.node.name.name = 'className';
            
            // Add Kilat CSS class prefix
            if (path.node.value && path.node.value.value) {
              path.node.value.value = `k-${path.node.value.value}`;
            }
          }
          
          // Transform kilat-* attributes to data attributes
          if (name.startsWith('kilat-')) {
            path.node.name.name = `data-${name}`;
          }
        },
        
        // Transform Kilat.js hooks
        CallExpression(path: any) {
          const callee = path.node.callee;
          
          if (callee.type === 'Identifier') {
            // Transform useKilat hooks
            if (callee.name.startsWith('useKilat')) {
              // Add import if not present
              this.ensureKilatImport(path);
            }
          }
        }
      }
    };
  }

  // ⚡ Kilat React optimization plugin
  private createKilatReactOptimizationPlugin(): Plugin {
    return {
      name: 'kilat-react-optimization',
      
      configResolved(config) {
        // Add Kilat.js specific optimizations
        config.optimizeDeps = config.optimizeDeps || {};
        config.optimizeDeps.include = config.optimizeDeps.include || [];
        
        // Pre-bundle Kilat.js dependencies
        config.optimizeDeps.include.push(
          'kilat-core',
          'kilat-router',
          'kilat-utils',
          'kilatcss',
          'kilatanim.js'
        );
        
        // Exclude from bundling (keep as external)
        config.optimizeDeps.exclude = config.optimizeDeps.exclude || [];
        config.optimizeDeps.exclude.push(
          'kilat-backend',
          'kilat-db'
        );
      },
      
      transform(code, id) {
        // Transform Kilat.js specific code
        if (id.includes('.kilat.') || id.includes('kilat-')) {
          return this.transformKilatCode(code, id);
        }
      },
      
      generateBundle(options, bundle) {
        // Optimize Kilat.js bundle
        this.optimizeKilatBundle(bundle);
      }
    };
  }

  // 🛠️ React DevTools plugin
  private createReactDevToolsPlugin(): Plugin {
    return {
      name: 'kilat-react-devtools',
      
      configureServer(server) {
        // Add React DevTools middleware
        server.middlewares.use('/kilat-devtools', (req, res, next) => {
          if (req.url === '/kilat-devtools/status') {
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              status: 'active',
              version: '1.0.0',
              features: ['react-devtools', 'kilat-inspector']
            }));
          } else {
            next();
          }
        });
      },
      
      transformIndexHtml(html) {
        // Inject React DevTools script
        return html.replace(
          '<head>',
          `<head>
            <script>
              window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = window.__REACT_DEVTOOLS_GLOBAL_HOOK__ || {};
              window.__KILAT_DEVTOOLS__ = true;
            </script>`
        );
      }
    };
  }

  // 🔄 Transform Kilat.js code
  private transformKilatCode(code: string, id: string): string {
    // Add Kilat.js specific transformations
    let transformedCode = code;
    
    // Transform Kilat CSS classes
    transformedCode = transformedCode.replace(
      /className=["']([^"']*k-[^"']*)["']/g,
      (match, classes) => {
        const kilatClasses = classes.split(' ')
          .map((cls: string) => cls.startsWith('k-') ? cls : `k-${cls}`)
          .join(' ');
        return `className="${kilatClasses}"`;
      }
    );
    
    // Transform Kilat animations
    transformedCode = transformedCode.replace(
      /kilat-animate-(\w+)/g,
      'k-animate-$1'
    );
    
    // Add source map comment
    if (this.config.sourcemap) {
      transformedCode += `\n//# sourceMappingURL=${id}.map`;
    }
    
    return transformedCode;
  }

  // 📦 Optimize Kilat bundle
  private optimizeKilatBundle(bundle: any): void {
    for (const [fileName, chunk] of Object.entries(bundle)) {
      if (chunk.type === 'chunk') {
        // Optimize Kilat.js chunks
        this.optimizeKilatChunk(chunk as any);
      }
    }
  }

  // 🔧 Optimize individual chunk
  private optimizeKilatChunk(chunk: any): void {
    // Remove development-only code in production
    if (this.config.mode === 'production') {
      chunk.code = chunk.code.replace(
        /if\s*\(\s*process\.env\.NODE_ENV\s*===\s*['"]development['"]\s*\)\s*{[^}]*}/g,
        ''
      );
      
      // Remove Kilat.js debug statements
      chunk.code = chunk.code.replace(
        /console\.(log|debug|info)\([^)]*kilat[^)]*\);?/gi,
        ''
      );
    }
    
    // Add Kilat.js runtime optimizations
    chunk.code = `
      // Kilat.js Runtime Optimizations
      if (typeof window !== 'undefined') {
        window.__KILAT_RUNTIME__ = {
          version: '1.0.0',
          mode: '${this.config.mode}',
          features: ['react', 'css', 'animations', 'router']
        };
      }
      ${chunk.code}
    `;
  }

  // 📥 Ensure Kilat import
  private ensureKilatImport(path: any): void {
    const program = path.findParent((p: any) => p.isProgram());
    
    // Check if Kilat.js is already imported
    let hasKilatImport = false;
    program.traverse({
      ImportDeclaration(importPath: any) {
        if (importPath.node.source.value.includes('kilat-')) {
          hasKilatImport = true;
        }
      }
    });
    
    // Add import if not present
    if (!hasKilatImport) {
      const importStatement = `import { useKilat } from 'kilat-core';`;
      program.unshiftContainer('body', importStatement);
    }
  }

  // 📊 Get plugin statistics
  getStats(): any {
    return {
      name: 'KilatReactPlugin',
      version: '1.0.0',
      features: [
        'Fast Refresh',
        'JSX Optimization',
        'Kilat Transforms',
        'DevTools Integration'
      ],
      config: {
        mode: this.config.mode,
        sourcemap: this.config.sourcemap,
        target: this.config.target
      }
    };
  }
}
