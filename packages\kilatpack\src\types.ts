// 🎯 KilatPack Types

export interface KilatPackConfig {
  // 📁 Input/Output
  entry: string | string[] | Record<string, string>;
  outDir: string;
  publicDir?: string;
  assetsDir?: string;
  
  // 🎯 Build Mode
  mode: 'development' | 'production';
  target: 'web' | 'node' | 'electron' | 'mobile';
  platform: 'browser' | 'node' | 'neutral';
  
  // 🔧 Build Options
  minify: boolean;
  sourcemap: boolean | 'inline' | 'external';
  splitting: boolean;
  treeshaking: boolean;
  bundle: boolean;
  
  // 🌐 Development Server
  server?: {
    port: number;
    host: string;
    https: boolean;
    open: boolean;
    cors: boolean;
    proxy?: Record<string, string | ProxyOptions>;
    hmr: boolean;
    liveReload: boolean;
  };
  
  // 🔄 Hot Module Replacement
  hmr?: {
    enabled: boolean;
    port?: number;
    overlay: boolean;
    clientPort?: number;
  };
  
  // 📦 External Dependencies
  external?: string[];
  globals?: Record<string, string>;
  
  // 🎨 CSS Processing
  css?: {
    modules: boolean;
    postcss: boolean;
    preprocessor: 'sass' | 'less' | 'stylus' | 'none';
    extract: boolean;
    minify: boolean;
  };
  
  // 🖼️ Asset Processing
  assets?: {
    limit: number; // Inline assets smaller than this (bytes)
    publicPath: string;
    hash: boolean;
    extensions: string[];
  };
  
  // 🔌 Plugins
  plugins?: KilatPackPlugin[];
  
  // 🎭 Transformations
  transform?: {
    jsx: 'react' | 'preact' | 'solid' | 'vue';
    typescript: boolean;
    decorators: boolean;
    define: Record<string, string>;
    inject: string[];
  };
  
  // 📊 Analysis
  analyze?: {
    enabled: boolean;
    bundleSize: boolean;
    dependencies: boolean;
    duplicates: boolean;
    treemap: boolean;
  };
  
  // 🐛 Debug
  debug?: {
    enabled: boolean;
    overlay: boolean;
    verbose: boolean;
    profile: boolean;
  };
  
  // ⚡ Performance
  performance?: {
    cache: boolean;
    parallel: boolean;
    workers: number;
    memoryLimit: number;
  };
}

export interface ProxyOptions {
  target: string;
  changeOrigin?: boolean;
  rewrite?: (path: string) => string;
  configure?: (proxy: any, options: any) => void;
}

export interface KilatPackPlugin {
  name: string;
  setup: (build: any) => void | Promise<void>;
  options?: Record<string, any>;
}

export interface BuildResult {
  success: boolean;
  duration: number;
  outputFiles: OutputFile[];
  warnings: BuildMessage[];
  errors: BuildMessage[];
  metafile?: Metafile;
  stats?: BuildStats;
}

export interface OutputFile {
  path: string;
  contents: Uint8Array;
  hash: string;
  size: number;
  type: 'js' | 'css' | 'html' | 'asset';
}

export interface BuildMessage {
  id: string;
  text: string;
  location?: {
    file: string;
    line: number;
    column: number;
    length: number;
    lineText: string;
  };
  notes?: BuildNote[];
  detail?: any;
}

export interface BuildNote {
  text: string;
  location?: {
    file: string;
    line: number;
    column: number;
    length: number;
    lineText: string;
  };
}

export interface Metafile {
  inputs: Record<string, MetafileInput>;
  outputs: Record<string, MetafileOutput>;
}

export interface MetafileInput {
  bytes: number;
  imports: MetafileImport[];
  format?: string;
}

export interface MetafileOutput {
  bytes: number;
  inputs: Record<string, MetafileOutputInput>;
  imports: MetafileImport[];
  exports: string[];
  entryPoint?: string;
  cssBundle?: string;
}

export interface MetafileImport {
  path: string;
  kind: string;
  external?: boolean;
}

export interface MetafileOutputInput {
  bytesInOutput: number;
}

export interface BuildStats {
  totalSize: number;
  gzipSize: number;
  brotliSize: number;
  chunks: ChunkInfo[];
  assets: AssetInfo[];
  dependencies: DependencyInfo[];
}

export interface ChunkInfo {
  name: string;
  size: number;
  gzipSize: number;
  modules: string[];
  isEntry: boolean;
  isDynamic: boolean;
}

export interface AssetInfo {
  name: string;
  size: number;
  type: string;
  hash: string;
}

export interface DependencyInfo {
  name: string;
  version: string;
  size: number;
  license: string;
  homepage?: string;
}

export interface DevServerOptions {
  config: KilatPackConfig;
  onReady?: (server: DevServer) => void;
  onError?: (error: Error) => void;
  onReload?: () => void;
}

export interface DevServer {
  port: number;
  host: string;
  url: string;
  close: () => Promise<void>;
  reload: () => void;
  send: (message: HMRMessage) => void;
}

export interface HMRMessage {
  type: 'update' | 'full-reload' | 'error' | 'connected' | 'ping' | 'pong';
  updates?: HMRUpdate[];
  error?: {
    message: string;
    stack?: string;
    id?: string;
    frame?: string;
    plugin?: string;
    pluginCode?: string;
    loc?: {
      file?: string;
      line: number;
      column: number;
    };
  };
}

export interface HMRUpdate {
  type: 'js-update' | 'css-update';
  path: string;
  acceptedPath: string;
  timestamp: number;
  explicitImportRequired?: boolean;
}

export interface WatchOptions {
  ignored?: string | string[];
  ignoreInitial?: boolean;
  followSymlinks?: boolean;
  cwd?: string;
  disableGlobbing?: boolean;
  usePolling?: boolean;
  interval?: number;
  binaryInterval?: number;
  alwaysStat?: boolean;
  depth?: number;
  awaitWriteFinish?: {
    stabilityThreshold: number;
    pollInterval: number;
  };
}

export interface CacheOptions {
  enabled: boolean;
  directory: string;
  maxAge: number;
  maxSize: number;
  compression: boolean;
}

export interface OptimizationOptions {
  minify: boolean;
  treeshake: boolean;
  deadCodeElimination: boolean;
  constantFolding: boolean;
  inlineConstants: boolean;
  removeConsole: boolean;
  removeDebugger: boolean;
}

export interface SourceMapOptions {
  enabled: boolean;
  type: 'inline' | 'external' | 'hidden';
  includeContent: boolean;
  exclude: string[];
}

export interface BundleAnalysis {
  totalSize: number;
  gzipSize: number;
  brotliSize: number;
  moduleCount: number;
  chunkCount: number;
  duplicates: DuplicateModule[];
  largestModules: ModuleSize[];
  unusedExports: UnusedExport[];
  circularDependencies: CircularDependency[];
}

export interface DuplicateModule {
  name: string;
  size: number;
  count: number;
  chunks: string[];
}

export interface ModuleSize {
  name: string;
  size: number;
  gzipSize: number;
  percentage: number;
}

export interface UnusedExport {
  module: string;
  export: string;
  size: number;
}

export interface CircularDependency {
  modules: string[];
  severity: 'warning' | 'error';
}

export interface PerformanceMetrics {
  buildTime: number;
  bundleTime: number;
  transformTime: number;
  resolveTime: number;
  writeTime: number;
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cacheHits: number;
  cacheMisses: number;
}

export interface KilatPackContext {
  config: KilatPackConfig;
  mode: 'development' | 'production';
  command: 'build' | 'serve' | 'preview';
  isProduction: boolean;
  isDevelopment: boolean;
  logger: Logger;
}

export interface Logger {
  info: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;
  debug: (message: string, ...args: any[]) => void;
  success: (message: string, ...args: any[]) => void;
  time: (label: string) => void;
  timeEnd: (label: string) => void;
}
