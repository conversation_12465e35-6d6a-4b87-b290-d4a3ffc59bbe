#!/bin/bash

# 🚀 Kilat.js Installation Script
# One-command installation for Kilat.js framework

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
KILAT_VERSION="latest"
INSTALL_DIR="$HOME/.kilat"
BIN_DIR="$HOME/.local/bin"
REPO_URL="https://github.com/kangpcode/kilat.js"
RELEASES_URL="https://api.github.com/repos/kangpcode/kilat.js/releases/latest"

# Functions
print_banner() {
    echo -e "${CYAN}"
    echo "  ⚡ Kilat.js Framework Installer"
    echo "  Framework masa depan dari Nusantara"
    echo -e "${NC}"
}

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detect OS and architecture
detect_platform() {
    local os
    local arch
    
    case "$(uname -s)" in
        Linux*)     os="linux";;
        Darwin*)    os="macos";;
        CYGWIN*|MINGW*|MSYS*) os="windows";;
        *)          error "Unsupported operating system: $(uname -s)";;
    esac
    
    case "$(uname -m)" in
        x86_64|amd64)   arch="x64";;
        arm64|aarch64)  arch="arm64";;
        armv7l)         arch="arm";;
        *)              error "Unsupported architecture: $(uname -m)";;
    esac
    
    echo "${os}-${arch}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check for curl or wget
    if ! command_exists curl && ! command_exists wget; then
        error "curl or wget is required for installation"
    fi
    
    # Check for Node.js or Bun
    if command_exists bun; then
        log "✅ Bun detected: $(bun --version)"
        RUNTIME="bun"
    elif command_exists node; then
        local node_version=$(node --version | sed 's/v//')
        local required_version="18.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
            log "✅ Node.js detected: v$node_version"
            RUNTIME="node"
        else
            error "Node.js version $required_version or higher is required (found: v$node_version)"
        fi
    else
        warn "Neither Bun nor Node.js found. Installing Bun..."
        install_bun
        RUNTIME="bun"
    fi
    
    # Check for Git
    if ! command_exists git; then
        error "Git is required for installation"
    fi
    
    log "✅ All prerequisites satisfied"
}

# Install Bun if not present
install_bun() {
    log "Installing Bun..."
    
    if command_exists curl; then
        curl -fsSL https://bun.sh/install | bash
    else
        error "curl is required to install Bun"
    fi
    
    # Add Bun to PATH for current session
    export PATH="$HOME/.bun/bin:$PATH"
    
    if command_exists bun; then
        log "✅ Bun installed successfully"
    else
        error "Failed to install Bun"
    fi
}

# Download and extract Kilat.js
download_kilat() {
    log "Downloading Kilat.js..."
    
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"
    
    # Download latest release
    if command_exists curl; then
        curl -fsSL "$REPO_URL/archive/refs/heads/main.zip" -o kilat.zip
    else
        wget -q "$REPO_URL/archive/refs/heads/main.zip" -O kilat.zip
    fi
    
    # Extract
    if command_exists unzip; then
        unzip -q kilat.zip
        mv kilat.js-main/* .
        rm -rf kilat.js-main kilat.zip
    else
        error "unzip is required for installation"
    fi
    
    log "✅ Kilat.js downloaded successfully"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    cd "$INSTALL_DIR"
    
    if [ "$RUNTIME" = "bun" ]; then
        bun install
    else
        npm install
    fi
    
    log "✅ Dependencies installed successfully"
}

# Build packages
build_packages() {
    log "Building packages..."
    
    cd "$INSTALL_DIR"
    
    if [ "$RUNTIME" = "bun" ]; then
        bun run build:packages
    else
        npm run build:packages
    fi
    
    log "✅ Packages built successfully"
}

# Setup CLI
setup_cli() {
    log "Setting up CLI..."
    
    # Create bin directory
    mkdir -p "$BIN_DIR"
    
    # Create kilat command
    cat > "$BIN_DIR/kilat" << EOF
#!/bin/bash
exec "$RUNTIME" "$INSTALL_DIR/packages/kilat-cli/dist/index.js" "\$@"
EOF
    
    chmod +x "$BIN_DIR/kilat"
    
    # Add to PATH if not already there
    local shell_rc=""
    case "$SHELL" in
        */bash) shell_rc="$HOME/.bashrc";;
        */zsh)  shell_rc="$HOME/.zshrc";;
        */fish) shell_rc="$HOME/.config/fish/config.fish";;
        *)      shell_rc="$HOME/.profile";;
    esac
    
    if [ -f "$shell_rc" ] && ! grep -q "$BIN_DIR" "$shell_rc"; then
        echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$shell_rc"
        log "Added $BIN_DIR to PATH in $shell_rc"
    fi
    
    log "✅ CLI setup completed"
}

# Verify installation
verify_installation() {
    log "Verifying installation..."
    
    # Add bin directory to current PATH
    export PATH="$BIN_DIR:$PATH"
    
    if command_exists kilat; then
        local version=$(kilat --version 2>/dev/null || echo "unknown")
        log "✅ Kilat.js CLI installed successfully (version: $version)"
    else
        error "CLI installation verification failed"
    fi
}

# Create sample project
create_sample_project() {
    read -p "Would you like to create a sample project? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Creating sample project..."
        
        cd "$HOME"
        kilat create kilat-sample --template basic
        
        log "✅ Sample project created at $HOME/kilat-sample"
        log "To get started:"
        log "  cd kilat-sample"
        log "  kilat dev"
    fi
}

# Cleanup on error
cleanup() {
    if [ -d "$INSTALL_DIR" ]; then
        rm -rf "$INSTALL_DIR"
    fi
}

# Main installation function
main() {
    print_banner
    
    # Set up error handling
    trap cleanup ERR
    
    log "Starting Kilat.js installation..."
    log "Platform: $(detect_platform)"
    
    check_prerequisites
    download_kilat
    install_dependencies
    build_packages
    setup_cli
    verify_installation
    
    echo
    echo -e "${GREEN}🎉 Kilat.js installed successfully!${NC}"
    echo
    echo -e "${CYAN}Quick Start:${NC}"
    echo -e "  ${YELLOW}kilat create my-app${NC}     # Create new project"
    echo -e "  ${YELLOW}cd my-app${NC}               # Enter project directory"
    echo -e "  ${YELLOW}kilat dev${NC}               # Start development server"
    echo
    echo -e "${CYAN}Documentation:${NC} https://docs.kilat-js.pcode.my.id"
    echo -e "${CYAN}Community:${NC} https://discord.gg/kilatjs"
    echo
    
    create_sample_project
    
    echo -e "${PURPLE}Happy coding with Kilat.js! ⚡${NC}"
}

# Run installation
main "$@"
