#!/usr/bin/env node

/**
 * 📦 Kilat.js Publishing Script
 * Automated publishing for all packages with version management
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const semver = require('semver');

// Configuration
const PACKAGES_DIR = path.join(__dirname, '../packages');
const APPS_DIR = path.join(__dirname, '../apps');
const ROOT_DIR = path.join(__dirname, '..');

// Package publishing order (dependencies first)
const PUBLISH_ORDER = [
  'kilat-utils',
  'kilat-core',
  'kilatcss',
  'kilatanim.js',
  'kilat-router',
  'kilat-platform',
  'kilat-db',
  'kilat-backend',
  'kilatpack',
  'kilat-plugins',
  'kilat-cli'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, cwd = ROOT_DIR) {
  try {
    return execSync(command, { 
      cwd, 
      stdio: 'inherit',
      encoding: 'utf8'
    });
  } catch (error) {
    log(`❌ Command failed: ${command}`, 'red');
    throw error;
  }
}

function getPackageInfo(packagePath) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    return null;
  }
  
  return JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
}

function updatePackageVersion(packagePath, newVersion) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  const packageJson = getPackageInfo(packagePath);
  
  packageJson.version = newVersion;
  
  fs.writeFileSync(
    packageJsonPath, 
    JSON.stringify(packageJson, null, 2) + '\n'
  );
}

function updateDependencyVersions(packagePath, dependencies) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  const packageJson = getPackageInfo(packagePath);
  
  // Update dependencies
  if (packageJson.dependencies) {
    Object.keys(packageJson.dependencies).forEach(dep => {
      if (dependencies[dep]) {
        packageJson.dependencies[dep] = `^${dependencies[dep]}`;
      }
    });
  }
  
  // Update devDependencies
  if (packageJson.devDependencies) {
    Object.keys(packageJson.devDependencies).forEach(dep => {
      if (dependencies[dep]) {
        packageJson.devDependencies[dep] = `^${dependencies[dep]}`;
      }
    });
  }
  
  fs.writeFileSync(
    packageJsonPath, 
    JSON.stringify(packageJson, null, 2) + '\n'
  );
}

function getAllPackages() {
  const packages = [];
  
  // Get packages from packages directory
  if (fs.existsSync(PACKAGES_DIR)) {
    const packageDirs = fs.readdirSync(PACKAGES_DIR);
    packageDirs.forEach(dir => {
      const packagePath = path.join(PACKAGES_DIR, dir);
      const packageInfo = getPackageInfo(packagePath);
      
      if (packageInfo && !packageInfo.private) {
        packages.push({
          name: packageInfo.name,
          path: packagePath,
          info: packageInfo
        });
      }
    });
  }
  
  return packages;
}

function buildPackage(packagePath) {
  log(`🏗️  Building package at ${packagePath}`, 'blue');
  
  // Check if build script exists
  const packageInfo = getPackageInfo(packagePath);
  if (packageInfo.scripts && packageInfo.scripts.build) {
    execCommand('bun run build', packagePath);
  } else {
    log(`⚠️  No build script found for ${packageInfo.name}`, 'yellow');
  }
}

function testPackage(packagePath) {
  log(`🧪 Testing package at ${packagePath}`, 'blue');
  
  // Check if test script exists
  const packageInfo = getPackageInfo(packagePath);
  if (packageInfo.scripts && packageInfo.scripts.test) {
    execCommand('bun run test', packagePath);
  } else {
    log(`⚠️  No test script found for ${packageInfo.name}`, 'yellow');
  }
}

function publishPackage(packagePath, tag = 'latest') {
  const packageInfo = getPackageInfo(packagePath);
  log(`📦 Publishing ${packageInfo.name}@${packageInfo.version}`, 'green');
  
  try {
    // Check if already published
    const publishedVersion = execSync(
      `npm view ${packageInfo.name} version`, 
      { encoding: 'utf8' }
    ).trim();
    
    if (publishedVersion === packageInfo.version) {
      log(`⚠️  Version ${packageInfo.version} already published`, 'yellow');
      return false;
    }
  } catch (error) {
    // Package doesn't exist yet, which is fine
  }
  
  // Publish to npm
  execCommand(`npm publish --tag ${tag}`, packagePath);
  log(`✅ Successfully published ${packageInfo.name}@${packageInfo.version}`, 'green');
  
  return true;
}

function createGitTag(version) {
  log(`🏷️  Creating git tag v${version}`, 'blue');
  
  execCommand(`git add .`);
  execCommand(`git commit -m "chore: release v${version}"`);
  execCommand(`git tag v${version}`);
  execCommand(`git push origin main --tags`);
}

function createGitHubRelease(version) {
  log(`🚀 Creating GitHub release v${version}`, 'blue');
  
  const releaseNotes = generateReleaseNotes(version);
  
  // Create release using GitHub CLI if available
  try {
    execCommand(`gh release create v${version} --title "Release v${version}" --notes "${releaseNotes}"`);
    log(`✅ GitHub release created successfully`, 'green');
  } catch (error) {
    log(`⚠️  GitHub CLI not available, skipping release creation`, 'yellow');
  }
}

function generateReleaseNotes(version) {
  // Generate release notes from git commits
  try {
    const commits = execSync(
      `git log --pretty=format:"- %s" --since="1 week ago"`,
      { encoding: 'utf8' }
    );
    
    return `## What's New in v${version}\n\n${commits}\n\n## Installation\n\n\`\`\`bash\nbun create kilat-app my-app\n\`\`\``;
  } catch (error) {
    return `## Release v${version}\n\nNew features and improvements.`;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const versionType = args[0] || 'patch'; // patch, minor, major
  const tag = args[1] || 'latest';
  const dryRun = args.includes('--dry-run');
  
  log('🚀 Starting Kilat.js publishing process...', 'cyan');
  
  if (dryRun) {
    log('🔍 Running in dry-run mode (no actual publishing)', 'yellow');
  }
  
  try {
    // 1. Get all packages
    const packages = getAllPackages();
    log(`📦 Found ${packages.length} packages to publish`, 'blue');
    
    // 2. Determine new version
    const rootPackage = getPackageInfo(ROOT_DIR);
    const currentVersion = rootPackage.version;
    const newVersion = semver.inc(currentVersion, versionType);
    
    log(`📈 Version bump: ${currentVersion} → ${newVersion}`, 'green');
    
    if (dryRun) {
      log('📋 Packages that would be published:', 'blue');
      packages.forEach(pkg => {
        log(`  - ${pkg.name}@${newVersion}`, 'cyan');
      });
      return;
    }
    
    // 3. Update root package version
    updatePackageVersion(ROOT_DIR, newVersion);
    
    // 4. Update all package versions
    const publishedVersions = {};
    packages.forEach(pkg => {
      updatePackageVersion(pkg.path, newVersion);
      publishedVersions[pkg.name] = newVersion;
    });
    
    // 5. Update cross-dependencies
    packages.forEach(pkg => {
      updateDependencyVersions(pkg.path, publishedVersions);
    });
    
    // 6. Build, test, and publish packages in order
    for (const packageName of PUBLISH_ORDER) {
      const pkg = packages.find(p => p.name === packageName);
      if (!pkg) continue;
      
      log(`\n🔄 Processing ${pkg.name}...`, 'magenta');
      
      // Build package
      buildPackage(pkg.path);
      
      // Test package
      testPackage(pkg.path);
      
      // Publish package
      publishPackage(pkg.path, tag);
    }
    
    // 7. Create git tag and GitHub release
    createGitTag(newVersion);
    createGitHubRelease(newVersion);
    
    log('\n🎉 Publishing completed successfully!', 'green');
    log(`📦 All packages published as v${newVersion}`, 'green');
    log(`🔗 GitHub release: https://github.com/kangpcode/kilat.js/releases/tag/v${newVersion}`, 'blue');
    
  } catch (error) {
    log(`\n❌ Publishing failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  getAllPackages,
  buildPackage,
  testPackage,
  publishPackage,
  createGitTag,
  createGitHubRelease
};
