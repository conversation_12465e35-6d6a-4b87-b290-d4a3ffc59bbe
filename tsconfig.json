{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@kilat/*": ["./packages/*/src"], "@kilat/core": ["./packages/kilat-core/src"], "@kilat/css": ["./packages/kilatcss/src"], "@kilat/anim": ["./packages/kilatanim.js/src"], "@kilat/router": ["./packages/kilat-router/src"], "@kilat/platform": ["./packages/kilat-platform/src"], "@kilat/pack": ["./packages/kilatpack/src"], "@kilat/cli": ["./packages/kilat-cli/src"], "@kilat/db": ["./packages/kilat-db/src"], "@kilat/utils": ["./packages/kilat-utils/src"], "@kilat/plugins": ["./packages/kilat-plugins/src"]}, "types": ["bun-types", "node"]}, "include": ["packages/*/src/**/*", "apps/*/src/**/*", "examples/**/*", "*.ts", "*.tsx"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "references": [{"path": "./packages/kilat-core"}, {"path": "./packages/kilatcss"}, {"path": "./packages/kilatanim.js"}, {"path": "./packages/kilat-router"}, {"path": "./packages/kilat-platform"}, {"path": "./packages/kilatpack"}, {"path": "./packages/kilat-cli"}, {"path": "./packages/kilat-db"}, {"path": "./packages/kilat-utils"}, {"path": "./packages/kilat-plugins"}]}